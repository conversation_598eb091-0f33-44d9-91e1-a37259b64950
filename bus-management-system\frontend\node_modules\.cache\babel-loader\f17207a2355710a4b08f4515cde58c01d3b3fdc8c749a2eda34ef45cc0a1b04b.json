{"ast": null, "code": "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { difference, replaceAt } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { QueryObserver } from './queryObserver';\nimport { Subscribable } from './subscribable';\nexport var QueriesObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueriesObserver, _Subscribable);\n  function QueriesObserver(client, queries) {\n    var _this;\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.queries = [];\n    _this.result = [];\n    _this.observers = [];\n    _this.observersMap = {};\n    if (queries) {\n      _this.setQueries(queries);\n    }\n    return _this;\n  }\n  var _proto = QueriesObserver.prototype;\n  _proto.onSubscribe = function onSubscribe() {\n    var _this2 = this;\n    if (this.listeners.length === 1) {\n      this.observers.forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this2.onUpdate(observer, result);\n        });\n      });\n    }\n  };\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.observers.forEach(function (observer) {\n      observer.destroy();\n    });\n  };\n  _proto.setQueries = function setQueries(queries, notifyOptions) {\n    this.queries = queries;\n    this.updateObservers(notifyOptions);\n  };\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.result;\n  };\n  _proto.getOptimisticResult = function getOptimisticResult(queries) {\n    return this.findMatchingObservers(queries).map(function (match) {\n      return match.observer.getOptimisticResult(match.defaultedQueryOptions);\n    });\n  };\n  _proto.findMatchingObservers = function findMatchingObservers(queries) {\n    var _this3 = this;\n    var prevObservers = this.observers;\n    var defaultedQueryOptions = queries.map(function (options) {\n      return _this3.client.defaultQueryObserverOptions(options);\n    });\n    var matchingObservers = defaultedQueryOptions.flatMap(function (defaultedOptions) {\n      var match = prevObservers.find(function (observer) {\n        return observer.options.queryHash === defaultedOptions.queryHash;\n      });\n      if (match != null) {\n        return [{\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        }];\n      }\n      return [];\n    });\n    var matchedQueryHashes = matchingObservers.map(function (match) {\n      return match.defaultedQueryOptions.queryHash;\n    });\n    var unmatchedQueries = defaultedQueryOptions.filter(function (defaultedOptions) {\n      return !matchedQueryHashes.includes(defaultedOptions.queryHash);\n    });\n    var unmatchedObservers = prevObservers.filter(function (prevObserver) {\n      return !matchingObservers.some(function (match) {\n        return match.observer === prevObserver;\n      });\n    });\n    var newOrReusedObservers = unmatchedQueries.map(function (options, index) {\n      if (options.keepPreviousData) {\n        // return previous data from one of the observers that no longer match\n        var previouslyUsedObserver = unmatchedObservers[index];\n        if (previouslyUsedObserver !== undefined) {\n          return {\n            defaultedQueryOptions: options,\n            observer: previouslyUsedObserver\n          };\n        }\n      }\n      return {\n        defaultedQueryOptions: options,\n        observer: _this3.getObserver(options)\n      };\n    });\n    var sortMatchesByOrderOfQueries = function sortMatchesByOrderOfQueries(a, b) {\n      return defaultedQueryOptions.indexOf(a.defaultedQueryOptions) - defaultedQueryOptions.indexOf(b.defaultedQueryOptions);\n    };\n    return matchingObservers.concat(newOrReusedObservers).sort(sortMatchesByOrderOfQueries);\n  };\n  _proto.getObserver = function getObserver(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var currentObserver = this.observersMap[defaultedOptions.queryHash];\n    return currentObserver != null ? currentObserver : new QueryObserver(this.client, defaultedOptions);\n  };\n  _proto.updateObservers = function updateObservers(notifyOptions) {\n    var _this4 = this;\n    notifyManager.batch(function () {\n      var prevObservers = _this4.observers;\n      var newObserverMatches = _this4.findMatchingObservers(_this4.queries); // set options for the new observers to notify of changes\n\n      newObserverMatches.forEach(function (match) {\n        return match.observer.setOptions(match.defaultedQueryOptions, notifyOptions);\n      });\n      var newObservers = newObserverMatches.map(function (match) {\n        return match.observer;\n      });\n      var newObserversMap = Object.fromEntries(newObservers.map(function (observer) {\n        return [observer.options.queryHash, observer];\n      }));\n      var newResult = newObservers.map(function (observer) {\n        return observer.getCurrentResult();\n      });\n      var hasIndexChange = newObservers.some(function (observer, index) {\n        return observer !== prevObservers[index];\n      });\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n      _this4.observers = newObservers;\n      _this4.observersMap = newObserversMap;\n      _this4.result = newResult;\n      if (!_this4.hasListeners()) {\n        return;\n      }\n      difference(prevObservers, newObservers).forEach(function (observer) {\n        observer.destroy();\n      });\n      difference(newObservers, prevObservers).forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this4.onUpdate(observer, result);\n        });\n      });\n      _this4.notify();\n    });\n  };\n  _proto.onUpdate = function onUpdate(observer, result) {\n    var index = this.observers.indexOf(observer);\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result);\n      this.notify();\n    }\n  };\n  _proto.notify = function notify() {\n    var _this5 = this;\n    notifyManager.batch(function () {\n      _this5.listeners.forEach(function (listener) {\n        listener(_this5.result);\n      });\n    });\n  };\n  return QueriesObserver;\n}(Subscribable);", "map": {"version": 3, "names": ["_inherits<PERSON><PERSON>e", "difference", "replaceAt", "notify<PERSON><PERSON>ger", "QueryObserver", "Subscribable", "QueriesObserver", "_Subscribable", "client", "queries", "_this", "call", "result", "observers", "observersMap", "setQueries", "_proto", "prototype", "onSubscribe", "_this2", "listeners", "length", "for<PERSON>ach", "observer", "subscribe", "onUpdate", "onUnsubscribe", "destroy", "notifyOptions", "updateObservers", "getCurrentResult", "getOptimisticResult", "findMatchingObservers", "map", "match", "defaultedQueryOptions", "_this3", "prevObservers", "options", "defaultQueryObserverOptions", "matchingObservers", "flatMap", "defaultedOptions", "find", "queryHash", "matchedQueryHashes", "unmatchedQueries", "filter", "includes", "unmatchedObservers", "prevObserver", "some", "newOrReusedObservers", "index", "keepPreviousData", "previouslyUsedObserver", "undefined", "getObserver", "sortMatchesByOrderOfQueries", "a", "b", "indexOf", "concat", "sort", "currentObserver", "_this4", "batch", "newObserverMatches", "setOptions", "newObservers", "newObserversMap", "Object", "fromEntries", "newResult", "hasIndexChange", "hasListeners", "notify", "_this5", "listener"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/core/queriesObserver.js"], "sourcesContent": ["import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { difference, replaceAt } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { QueryObserver } from './queryObserver';\nimport { Subscribable } from './subscribable';\nexport var QueriesObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueriesObserver, _Subscribable);\n\n  function QueriesObserver(client, queries) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.queries = [];\n    _this.result = [];\n    _this.observers = [];\n    _this.observersMap = {};\n\n    if (queries) {\n      _this.setQueries(queries);\n    }\n\n    return _this;\n  }\n\n  var _proto = QueriesObserver.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    var _this2 = this;\n\n    if (this.listeners.length === 1) {\n      this.observers.forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this2.onUpdate(observer, result);\n        });\n      });\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.observers.forEach(function (observer) {\n      observer.destroy();\n    });\n  };\n\n  _proto.setQueries = function setQueries(queries, notifyOptions) {\n    this.queries = queries;\n    this.updateObservers(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.result;\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(queries) {\n    return this.findMatchingObservers(queries).map(function (match) {\n      return match.observer.getOptimisticResult(match.defaultedQueryOptions);\n    });\n  };\n\n  _proto.findMatchingObservers = function findMatchingObservers(queries) {\n    var _this3 = this;\n\n    var prevObservers = this.observers;\n    var defaultedQueryOptions = queries.map(function (options) {\n      return _this3.client.defaultQueryObserverOptions(options);\n    });\n    var matchingObservers = defaultedQueryOptions.flatMap(function (defaultedOptions) {\n      var match = prevObservers.find(function (observer) {\n        return observer.options.queryHash === defaultedOptions.queryHash;\n      });\n\n      if (match != null) {\n        return [{\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        }];\n      }\n\n      return [];\n    });\n    var matchedQueryHashes = matchingObservers.map(function (match) {\n      return match.defaultedQueryOptions.queryHash;\n    });\n    var unmatchedQueries = defaultedQueryOptions.filter(function (defaultedOptions) {\n      return !matchedQueryHashes.includes(defaultedOptions.queryHash);\n    });\n    var unmatchedObservers = prevObservers.filter(function (prevObserver) {\n      return !matchingObservers.some(function (match) {\n        return match.observer === prevObserver;\n      });\n    });\n    var newOrReusedObservers = unmatchedQueries.map(function (options, index) {\n      if (options.keepPreviousData) {\n        // return previous data from one of the observers that no longer match\n        var previouslyUsedObserver = unmatchedObservers[index];\n\n        if (previouslyUsedObserver !== undefined) {\n          return {\n            defaultedQueryOptions: options,\n            observer: previouslyUsedObserver\n          };\n        }\n      }\n\n      return {\n        defaultedQueryOptions: options,\n        observer: _this3.getObserver(options)\n      };\n    });\n\n    var sortMatchesByOrderOfQueries = function sortMatchesByOrderOfQueries(a, b) {\n      return defaultedQueryOptions.indexOf(a.defaultedQueryOptions) - defaultedQueryOptions.indexOf(b.defaultedQueryOptions);\n    };\n\n    return matchingObservers.concat(newOrReusedObservers).sort(sortMatchesByOrderOfQueries);\n  };\n\n  _proto.getObserver = function getObserver(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var currentObserver = this.observersMap[defaultedOptions.queryHash];\n    return currentObserver != null ? currentObserver : new QueryObserver(this.client, defaultedOptions);\n  };\n\n  _proto.updateObservers = function updateObservers(notifyOptions) {\n    var _this4 = this;\n\n    notifyManager.batch(function () {\n      var prevObservers = _this4.observers;\n\n      var newObserverMatches = _this4.findMatchingObservers(_this4.queries); // set options for the new observers to notify of changes\n\n\n      newObserverMatches.forEach(function (match) {\n        return match.observer.setOptions(match.defaultedQueryOptions, notifyOptions);\n      });\n      var newObservers = newObserverMatches.map(function (match) {\n        return match.observer;\n      });\n      var newObserversMap = Object.fromEntries(newObservers.map(function (observer) {\n        return [observer.options.queryHash, observer];\n      }));\n      var newResult = newObservers.map(function (observer) {\n        return observer.getCurrentResult();\n      });\n      var hasIndexChange = newObservers.some(function (observer, index) {\n        return observer !== prevObservers[index];\n      });\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n\n      _this4.observers = newObservers;\n      _this4.observersMap = newObserversMap;\n      _this4.result = newResult;\n\n      if (!_this4.hasListeners()) {\n        return;\n      }\n\n      difference(prevObservers, newObservers).forEach(function (observer) {\n        observer.destroy();\n      });\n      difference(newObservers, prevObservers).forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this4.onUpdate(observer, result);\n        });\n      });\n\n      _this4.notify();\n    });\n  };\n\n  _proto.onUpdate = function onUpdate(observer, result) {\n    var index = this.observers.indexOf(observer);\n\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result);\n      this.notify();\n    }\n  };\n\n  _proto.notify = function notify() {\n    var _this5 = this;\n\n    notifyManager.batch(function () {\n      _this5.listeners.forEach(function (listener) {\n        listener(_this5.result);\n      });\n    });\n  };\n\n  return QueriesObserver;\n}(Subscribable);"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,UAAU,EAAEC,SAAS,QAAQ,SAAS;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,IAAIC,eAAe,GAAG,aAAa,UAAUC,aAAa,EAAE;EACjEP,cAAc,CAACM,eAAe,EAAEC,aAAa,CAAC;EAE9C,SAASD,eAAeA,CAACE,MAAM,EAAEC,OAAO,EAAE;IACxC,IAAIC,KAAK;IAETA,KAAK,GAAGH,aAAa,CAACI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACxCD,KAAK,CAACF,MAAM,GAAGA,MAAM;IACrBE,KAAK,CAACD,OAAO,GAAG,EAAE;IAClBC,KAAK,CAACE,MAAM,GAAG,EAAE;IACjBF,KAAK,CAACG,SAAS,GAAG,EAAE;IACpBH,KAAK,CAACI,YAAY,GAAG,CAAC,CAAC;IAEvB,IAAIL,OAAO,EAAE;MACXC,KAAK,CAACK,UAAU,CAACN,OAAO,CAAC;IAC3B;IAEA,OAAOC,KAAK;EACd;EAEA,IAAIM,MAAM,GAAGV,eAAe,CAACW,SAAS;EAEtCD,MAAM,CAACE,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAI,IAAI,CAACC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACR,SAAS,CAACS,OAAO,CAAC,UAAUC,QAAQ,EAAE;QACzCA,QAAQ,CAACC,SAAS,CAAC,UAAUZ,MAAM,EAAE;UACnCO,MAAM,CAACM,QAAQ,CAACF,QAAQ,EAAEX,MAAM,CAAC;QACnC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;EAEDI,MAAM,CAACU,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC9C,IAAI,CAAC,IAAI,CAACN,SAAS,CAACC,MAAM,EAAE;MAC1B,IAAI,CAACM,OAAO,CAAC,CAAC;IAChB;EACF,CAAC;EAEDX,MAAM,CAACW,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,IAAI,CAACP,SAAS,GAAG,EAAE;IACnB,IAAI,CAACP,SAAS,CAACS,OAAO,CAAC,UAAUC,QAAQ,EAAE;MACzCA,QAAQ,CAACI,OAAO,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EAEDX,MAAM,CAACD,UAAU,GAAG,SAASA,UAAUA,CAACN,OAAO,EAAEmB,aAAa,EAAE;IAC9D,IAAI,CAACnB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACoB,eAAe,CAACD,aAAa,CAAC;EACrC,CAAC;EAEDZ,MAAM,CAACc,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACpD,OAAO,IAAI,CAAClB,MAAM;EACpB,CAAC;EAEDI,MAAM,CAACe,mBAAmB,GAAG,SAASA,mBAAmBA,CAACtB,OAAO,EAAE;IACjE,OAAO,IAAI,CAACuB,qBAAqB,CAACvB,OAAO,CAAC,CAACwB,GAAG,CAAC,UAAUC,KAAK,EAAE;MAC9D,OAAOA,KAAK,CAACX,QAAQ,CAACQ,mBAAmB,CAACG,KAAK,CAACC,qBAAqB,CAAC;IACxE,CAAC,CAAC;EACJ,CAAC;EAEDnB,MAAM,CAACgB,qBAAqB,GAAG,SAASA,qBAAqBA,CAACvB,OAAO,EAAE;IACrE,IAAI2B,MAAM,GAAG,IAAI;IAEjB,IAAIC,aAAa,GAAG,IAAI,CAACxB,SAAS;IAClC,IAAIsB,qBAAqB,GAAG1B,OAAO,CAACwB,GAAG,CAAC,UAAUK,OAAO,EAAE;MACzD,OAAOF,MAAM,CAAC5B,MAAM,CAAC+B,2BAA2B,CAACD,OAAO,CAAC;IAC3D,CAAC,CAAC;IACF,IAAIE,iBAAiB,GAAGL,qBAAqB,CAACM,OAAO,CAAC,UAAUC,gBAAgB,EAAE;MAChF,IAAIR,KAAK,GAAGG,aAAa,CAACM,IAAI,CAAC,UAAUpB,QAAQ,EAAE;QACjD,OAAOA,QAAQ,CAACe,OAAO,CAACM,SAAS,KAAKF,gBAAgB,CAACE,SAAS;MAClE,CAAC,CAAC;MAEF,IAAIV,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,CAAC;UACNC,qBAAqB,EAAEO,gBAAgB;UACvCnB,QAAQ,EAAEW;QACZ,CAAC,CAAC;MACJ;MAEA,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAIW,kBAAkB,GAAGL,iBAAiB,CAACP,GAAG,CAAC,UAAUC,KAAK,EAAE;MAC9D,OAAOA,KAAK,CAACC,qBAAqB,CAACS,SAAS;IAC9C,CAAC,CAAC;IACF,IAAIE,gBAAgB,GAAGX,qBAAqB,CAACY,MAAM,CAAC,UAAUL,gBAAgB,EAAE;MAC9E,OAAO,CAACG,kBAAkB,CAACG,QAAQ,CAACN,gBAAgB,CAACE,SAAS,CAAC;IACjE,CAAC,CAAC;IACF,IAAIK,kBAAkB,GAAGZ,aAAa,CAACU,MAAM,CAAC,UAAUG,YAAY,EAAE;MACpE,OAAO,CAACV,iBAAiB,CAACW,IAAI,CAAC,UAAUjB,KAAK,EAAE;QAC9C,OAAOA,KAAK,CAACX,QAAQ,KAAK2B,YAAY;MACxC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIE,oBAAoB,GAAGN,gBAAgB,CAACb,GAAG,CAAC,UAAUK,OAAO,EAAEe,KAAK,EAAE;MACxE,IAAIf,OAAO,CAACgB,gBAAgB,EAAE;QAC5B;QACA,IAAIC,sBAAsB,GAAGN,kBAAkB,CAACI,KAAK,CAAC;QAEtD,IAAIE,sBAAsB,KAAKC,SAAS,EAAE;UACxC,OAAO;YACLrB,qBAAqB,EAAEG,OAAO;YAC9Bf,QAAQ,EAAEgC;UACZ,CAAC;QACH;MACF;MAEA,OAAO;QACLpB,qBAAqB,EAAEG,OAAO;QAC9Bf,QAAQ,EAAEa,MAAM,CAACqB,WAAW,CAACnB,OAAO;MACtC,CAAC;IACH,CAAC,CAAC;IAEF,IAAIoB,2BAA2B,GAAG,SAASA,2BAA2BA,CAACC,CAAC,EAAEC,CAAC,EAAE;MAC3E,OAAOzB,qBAAqB,CAAC0B,OAAO,CAACF,CAAC,CAACxB,qBAAqB,CAAC,GAAGA,qBAAqB,CAAC0B,OAAO,CAACD,CAAC,CAACzB,qBAAqB,CAAC;IACxH,CAAC;IAED,OAAOK,iBAAiB,CAACsB,MAAM,CAACV,oBAAoB,CAAC,CAACW,IAAI,CAACL,2BAA2B,CAAC;EACzF,CAAC;EAED1C,MAAM,CAACyC,WAAW,GAAG,SAASA,WAAWA,CAACnB,OAAO,EAAE;IACjD,IAAII,gBAAgB,GAAG,IAAI,CAAClC,MAAM,CAAC+B,2BAA2B,CAACD,OAAO,CAAC;IACvE,IAAI0B,eAAe,GAAG,IAAI,CAAClD,YAAY,CAAC4B,gBAAgB,CAACE,SAAS,CAAC;IACnE,OAAOoB,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAG,IAAI5D,aAAa,CAAC,IAAI,CAACI,MAAM,EAAEkC,gBAAgB,CAAC;EACrG,CAAC;EAED1B,MAAM,CAACa,eAAe,GAAG,SAASA,eAAeA,CAACD,aAAa,EAAE;IAC/D,IAAIqC,MAAM,GAAG,IAAI;IAEjB9D,aAAa,CAAC+D,KAAK,CAAC,YAAY;MAC9B,IAAI7B,aAAa,GAAG4B,MAAM,CAACpD,SAAS;MAEpC,IAAIsD,kBAAkB,GAAGF,MAAM,CAACjC,qBAAqB,CAACiC,MAAM,CAACxD,OAAO,CAAC,CAAC,CAAC;;MAGvE0D,kBAAkB,CAAC7C,OAAO,CAAC,UAAUY,KAAK,EAAE;QAC1C,OAAOA,KAAK,CAACX,QAAQ,CAAC6C,UAAU,CAAClC,KAAK,CAACC,qBAAqB,EAAEP,aAAa,CAAC;MAC9E,CAAC,CAAC;MACF,IAAIyC,YAAY,GAAGF,kBAAkB,CAAClC,GAAG,CAAC,UAAUC,KAAK,EAAE;QACzD,OAAOA,KAAK,CAACX,QAAQ;MACvB,CAAC,CAAC;MACF,IAAI+C,eAAe,GAAGC,MAAM,CAACC,WAAW,CAACH,YAAY,CAACpC,GAAG,CAAC,UAAUV,QAAQ,EAAE;QAC5E,OAAO,CAACA,QAAQ,CAACe,OAAO,CAACM,SAAS,EAAErB,QAAQ,CAAC;MAC/C,CAAC,CAAC,CAAC;MACH,IAAIkD,SAAS,GAAGJ,YAAY,CAACpC,GAAG,CAAC,UAAUV,QAAQ,EAAE;QACnD,OAAOA,QAAQ,CAACO,gBAAgB,CAAC,CAAC;MACpC,CAAC,CAAC;MACF,IAAI4C,cAAc,GAAGL,YAAY,CAAClB,IAAI,CAAC,UAAU5B,QAAQ,EAAE8B,KAAK,EAAE;QAChE,OAAO9B,QAAQ,KAAKc,aAAa,CAACgB,KAAK,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAIhB,aAAa,CAAChB,MAAM,KAAKgD,YAAY,CAAChD,MAAM,IAAI,CAACqD,cAAc,EAAE;QACnE;MACF;MAEAT,MAAM,CAACpD,SAAS,GAAGwD,YAAY;MAC/BJ,MAAM,CAACnD,YAAY,GAAGwD,eAAe;MACrCL,MAAM,CAACrD,MAAM,GAAG6D,SAAS;MAEzB,IAAI,CAACR,MAAM,CAACU,YAAY,CAAC,CAAC,EAAE;QAC1B;MACF;MAEA1E,UAAU,CAACoC,aAAa,EAAEgC,YAAY,CAAC,CAAC/C,OAAO,CAAC,UAAUC,QAAQ,EAAE;QAClEA,QAAQ,CAACI,OAAO,CAAC,CAAC;MACpB,CAAC,CAAC;MACF1B,UAAU,CAACoE,YAAY,EAAEhC,aAAa,CAAC,CAACf,OAAO,CAAC,UAAUC,QAAQ,EAAE;QAClEA,QAAQ,CAACC,SAAS,CAAC,UAAUZ,MAAM,EAAE;UACnCqD,MAAM,CAACxC,QAAQ,CAACF,QAAQ,EAAEX,MAAM,CAAC;QACnC,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFqD,MAAM,CAACW,MAAM,CAAC,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC;EAED5D,MAAM,CAACS,QAAQ,GAAG,SAASA,QAAQA,CAACF,QAAQ,EAAEX,MAAM,EAAE;IACpD,IAAIyC,KAAK,GAAG,IAAI,CAACxC,SAAS,CAACgD,OAAO,CAACtC,QAAQ,CAAC;IAE5C,IAAI8B,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACzC,MAAM,GAAGV,SAAS,CAAC,IAAI,CAACU,MAAM,EAAEyC,KAAK,EAAEzC,MAAM,CAAC;MACnD,IAAI,CAACgE,MAAM,CAAC,CAAC;IACf;EACF,CAAC;EAED5D,MAAM,CAAC4D,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,IAAIC,MAAM,GAAG,IAAI;IAEjB1E,aAAa,CAAC+D,KAAK,CAAC,YAAY;MAC9BW,MAAM,CAACzD,SAAS,CAACE,OAAO,CAAC,UAAUwD,QAAQ,EAAE;QAC3CA,QAAQ,CAACD,MAAM,CAACjE,MAAM,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED,OAAON,eAAe;AACxB,CAAC,CAACD,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "RegExpLiteral", "path", "node", "flags", "includes", "replaceWith", "t", "newExpression", "identifier", "stringLiteral", "pattern"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-sticky-regex\",\n\n    visitor: {\n      RegExpLiteral(path) {\n        const { node } = path;\n        if (!node.flags.includes(\"y\")) return;\n\n        path.replaceWith(\n          t.newExpression(t.identifier(\"RegExp\"), [\n            t.stringLiteral(node.pattern),\n            t.stringLiteral(node.flags),\n          ]),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,wBAAwB;IAE9BC,OAAO,EAAE;MACPC,aAAaA,CAACC,IAAI,EAAE;QAClB,MAAM;UAAEC;QAAK,CAAC,GAAGD,IAAI;QACrB,IAAI,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAE/BH,IAAI,CAACI,WAAW,CACdC,WAAC,CAACC,aAAa,CAACD,WAAC,CAACE,UAAU,CAAC,QAAQ,CAAC,EAAE,CACtCF,WAAC,CAACG,aAAa,CAACP,IAAI,CAACQ,OAAO,CAAC,EAC7BJ,WAAC,CAACG,aAAa,CAACP,IAAI,CAACC,KAAK,CAAC,CAC5B,CACH,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}
"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2018_regexp = void 0;
const base_config_1 = require("./base-config");
exports.es2018_regexp = {
    RegExpMatchArray: base_config_1.TYPE,
    RegExpExecArray: base_config_1.TYPE,
    RegExp: base_config_1.TYPE,
};
//# sourceMappingURL=es2018.regexp.js.map
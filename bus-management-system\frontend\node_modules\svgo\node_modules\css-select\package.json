{"name": "css-select", "version": "2.1.0", "description": "a CSS selector compiler/engine", "author": "<PERSON> <<EMAIL>>", "keywords": ["css", "selector", "sizzle"], "repository": {"type": "git", "url": "git://github.com/fb55/css-select.git"}, "files": ["index.js", "index.d.ts", "lib"], "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.2.1", "domutils": "^1.7.0", "nth-check": "^1.0.2"}, "devDependencies": {"cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^6.0.0", "expect.js": "^0.3.1", "htmlparser2": "^4.0.0", "istanbul": "^0.4.5", "mocha": "^6.0.0", "mocha-lcov-reporter": "^1.3.0"}, "scripts": {"test": "mocha && npm run lint", "lint": "eslint index.js lib/*.js test/*.js", "lcov": "istanbul cover _mocha --report lcovonly -- -R spec", "coveralls": "npm run lint && npm run lcov && (cat coverage/lcov.info | coveralls || exit 0)"}, "license": "BSD-2-<PERSON><PERSON>", "types": "index.d.ts", "prettier": {"tabWidth": 4}}
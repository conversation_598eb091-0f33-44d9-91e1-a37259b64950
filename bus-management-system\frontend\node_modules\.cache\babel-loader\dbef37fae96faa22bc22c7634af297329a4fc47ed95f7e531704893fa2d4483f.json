{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ConciergeBell = createLucideIcon(\"ConciergeBell\", [[\"path\", {\n  d: \"M2 18a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v2H2v-2Z\",\n  key: \"1co3i8\"\n}], [\"path\", {\n  d: \"M20 16a8 8 0 1 0-16 0\",\n  key: \"1pa543\"\n}], [\"path\", {\n  d: \"M12 4v4\",\n  key: \"1bq03y\"\n}], [\"path\", {\n  d: \"M10 4h4\",\n  key: \"1xpv9s\"\n}]]);\nexport { ConciergeBell as default };", "map": {"version": 3, "names": ["ConciergeBell", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\concierge-bell.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ConciergeBell\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxOGEyIDIgMCAwIDEgMi0yaDE2YTIgMiAwIDAgMSAyIDJ2Mkgydi0yWiIgLz4KICA8cGF0aCBkPSJNMjAgMTZhOCA4IDAgMSAwLTE2IDAiIC8+CiAgPHBhdGggZD0iTTEyIDR2NCIgLz4KICA8cGF0aCBkPSJNMTAgNGg0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/concierge-bell\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ConciergeBell = createLucideIcon('ConciergeBell', [\n  [\n    'path',\n    { d: 'M2 18a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v2H2v-2Z', key: '1co3i8' },\n  ],\n  ['path', { d: 'M20 16a8 8 0 1 0-16 0', key: '1pa543' }],\n  ['path', { d: 'M12 4v4', key: '1bq03y' }],\n  ['path', { d: 'M10 4h4', key: '1xpv9s' }],\n]);\n\nexport default ConciergeBell;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EAAEC,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
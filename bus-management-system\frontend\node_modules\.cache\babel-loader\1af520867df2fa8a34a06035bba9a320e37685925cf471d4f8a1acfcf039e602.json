{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { QueryObserver } from './queryObserver';\nimport { hasNextPage, hasPreviousPage, infiniteQueryBehavior } from './infiniteQueryBehavior';\nexport var InfiniteQueryObserver = /*#__PURE__*/function (_QueryObserver) {\n  _inheritsLoose(InfiniteQueryObserver, _QueryObserver);\n\n  // Type override\n  // Type override\n  // Type override\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  function InfiniteQueryObserver(client, options) {\n    return _QueryObserver.call(this, client, options) || this;\n  }\n  var _proto = InfiniteQueryObserver.prototype;\n  _proto.bindMethods = function bindMethods() {\n    _QueryObserver.prototype.bindMethods.call(this);\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  };\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    _QueryObserver.prototype.setOptions.call(this, _extends({}, options, {\n      behavior: infiniteQueryBehavior()\n    }), notifyOptions);\n  };\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    options.behavior = infiniteQueryBehavior();\n    return _QueryObserver.prototype.getOptimisticResult.call(this, options);\n  };\n  _proto.fetchNextPage = function fetchNextPage(options) {\n    var _options$cancelRefetc;\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'forward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n  _proto.fetchPreviousPage = function fetchPreviousPage(options) {\n    var _options$cancelRefetc2;\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc2 = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc2 : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'backward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n  _proto.createResult = function createResult(query, options) {\n    var _state$data, _state$data2, _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet;\n    var state = query.state;\n    var result = _QueryObserver.prototype.createResult.call(this, query, options);\n    return _extends({}, result, {\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),\n      hasPreviousPage: hasPreviousPage(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),\n      isFetchingNextPage: state.isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward',\n      isFetchingPreviousPage: state.isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward'\n    });\n  };\n  return InfiniteQueryObserver;\n}(QueryObserver);", "map": {"version": 3, "names": ["_extends", "_inherits<PERSON><PERSON>e", "QueryObserver", "hasNextPage", "hasPreviousPage", "infiniteQueryBehavior", "InfiniteQueryObserver", "_QueryObserver", "client", "options", "call", "_proto", "prototype", "bindMethods", "fetchNextPage", "bind", "fetchPreviousPage", "setOptions", "notifyOptions", "behavior", "getOptimisticResult", "_options$cancelRefetc", "fetch", "cancelRefetch", "throwOnError", "meta", "fetchMore", "direction", "pageParam", "_options$cancelRefetc2", "createResult", "query", "_state$data", "_state$data2", "_state$fetchMeta", "_state$fetchMeta$fetc", "_state$fetchMeta2", "_state$fetchMeta2$fet", "state", "result", "data", "pages", "isFetchingNextPage", "isFetching", "fetchMeta", "isFetchingPreviousPage"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/core/infiniteQueryObserver.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { QueryObserver } from './queryObserver';\nimport { hasNextPage, hasPreviousPage, infiniteQueryBehavior } from './infiniteQueryBehavior';\nexport var InfiniteQueryObserver = /*#__PURE__*/function (_QueryObserver) {\n  _inheritsLoose(InfiniteQueryObserver, _QueryObserver);\n\n  // Type override\n  // Type override\n  // Type override\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  function InfiniteQueryObserver(client, options) {\n    return _QueryObserver.call(this, client, options) || this;\n  }\n\n  var _proto = InfiniteQueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    _QueryObserver.prototype.bindMethods.call(this);\n\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    _QueryObserver.prototype.setOptions.call(this, _extends({}, options, {\n      behavior: infiniteQueryBehavior()\n    }), notifyOptions);\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    options.behavior = infiniteQueryBehavior();\n    return _QueryObserver.prototype.getOptimisticResult.call(this, options);\n  };\n\n  _proto.fetchNextPage = function fetchNextPage(options) {\n    var _options$cancelRefetc;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'forward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.fetchPreviousPage = function fetchPreviousPage(options) {\n    var _options$cancelRefetc2;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc2 = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc2 : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'backward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var _state$data, _state$data2, _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet;\n\n    var state = query.state;\n\n    var result = _QueryObserver.prototype.createResult.call(this, query, options);\n\n    return _extends({}, result, {\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),\n      hasPreviousPage: hasPreviousPage(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),\n      isFetchingNextPage: state.isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward',\n      isFetchingPreviousPage: state.isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward'\n    });\n  };\n\n  return InfiniteQueryObserver;\n}(QueryObserver);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,EAAEC,eAAe,EAAEC,qBAAqB,QAAQ,yBAAyB;AAC7F,OAAO,IAAIC,qBAAqB,GAAG,aAAa,UAAUC,cAAc,EAAE;EACxEN,cAAc,CAACK,qBAAqB,EAAEC,cAAc,CAAC;;EAErD;EACA;EACA;EACA;EACA,SAASD,qBAAqBA,CAACE,MAAM,EAAEC,OAAO,EAAE;IAC9C,OAAOF,cAAc,CAACG,IAAI,CAAC,IAAI,EAAEF,MAAM,EAAEC,OAAO,CAAC,IAAI,IAAI;EAC3D;EAEA,IAAIE,MAAM,GAAGL,qBAAqB,CAACM,SAAS;EAE5CD,MAAM,CAACE,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1CN,cAAc,CAACK,SAAS,CAACC,WAAW,CAACH,IAAI,CAAC,IAAI,CAAC;IAE/C,IAAI,CAACI,aAAa,GAAG,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACD,IAAI,CAAC,IAAI,CAAC;EAC5D,CAAC;EAEDJ,MAAM,CAACM,UAAU,GAAG,SAASA,UAAUA,CAACR,OAAO,EAAES,aAAa,EAAE;IAC9DX,cAAc,CAACK,SAAS,CAACK,UAAU,CAACP,IAAI,CAAC,IAAI,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAES,OAAO,EAAE;MACnEU,QAAQ,EAAEd,qBAAqB,CAAC;IAClC,CAAC,CAAC,EAAEa,aAAa,CAAC;EACpB,CAAC;EAEDP,MAAM,CAACS,mBAAmB,GAAG,SAASA,mBAAmBA,CAACX,OAAO,EAAE;IACjEA,OAAO,CAACU,QAAQ,GAAGd,qBAAqB,CAAC,CAAC;IAC1C,OAAOE,cAAc,CAACK,SAAS,CAACQ,mBAAmB,CAACV,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;EACzE,CAAC;EAEDE,MAAM,CAACG,aAAa,GAAG,SAASA,aAAaA,CAACL,OAAO,EAAE;IACrD,IAAIY,qBAAqB;IAEzB,OAAO,IAAI,CAACC,KAAK,CAAC;MAChB;MACAC,aAAa,EAAE,CAACF,qBAAqB,GAAGZ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACc,aAAa,KAAK,IAAI,GAAGF,qBAAqB,GAAG,IAAI;MAChIG,YAAY,EAAEf,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,YAAY;MAC7DC,IAAI,EAAE;QACJC,SAAS,EAAE;UACTC,SAAS,EAAE,SAAS;UACpBC,SAAS,EAAEnB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACmB;QAChD;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAEDjB,MAAM,CAACK,iBAAiB,GAAG,SAASA,iBAAiBA,CAACP,OAAO,EAAE;IAC7D,IAAIoB,sBAAsB;IAE1B,OAAO,IAAI,CAACP,KAAK,CAAC;MAChB;MACAC,aAAa,EAAE,CAACM,sBAAsB,GAAGpB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACc,aAAa,KAAK,IAAI,GAAGM,sBAAsB,GAAG,IAAI;MAClIL,YAAY,EAAEf,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,YAAY;MAC7DC,IAAI,EAAE;QACJC,SAAS,EAAE;UACTC,SAAS,EAAE,UAAU;UACrBC,SAAS,EAAEnB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACmB;QAChD;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAEDjB,MAAM,CAACmB,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEtB,OAAO,EAAE;IAC1D,IAAIuB,WAAW,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,qBAAqB;IAEhH,IAAIC,KAAK,GAAGP,KAAK,CAACO,KAAK;IAEvB,IAAIC,MAAM,GAAGhC,cAAc,CAACK,SAAS,CAACkB,YAAY,CAACpB,IAAI,CAAC,IAAI,EAAEqB,KAAK,EAAEtB,OAAO,CAAC;IAE7E,OAAOT,QAAQ,CAAC,CAAC,CAAC,EAAEuC,MAAM,EAAE;MAC1BzB,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCE,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCb,WAAW,EAAEA,WAAW,CAACM,OAAO,EAAE,CAACuB,WAAW,GAAGM,KAAK,CAACE,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,WAAW,CAACS,KAAK,CAAC;MAClGrC,eAAe,EAAEA,eAAe,CAACK,OAAO,EAAE,CAACwB,YAAY,GAAGK,KAAK,CAACE,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,YAAY,CAACQ,KAAK,CAAC;MAC5GC,kBAAkB,EAAEJ,KAAK,CAACK,UAAU,IAAI,CAAC,CAACT,gBAAgB,GAAGI,KAAK,CAACM,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACT,qBAAqB,GAAGD,gBAAgB,CAACR,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGS,qBAAqB,CAACR,SAAS,MAAM,SAAS;MACvNkB,sBAAsB,EAAEP,KAAK,CAACK,UAAU,IAAI,CAAC,CAACP,iBAAiB,GAAGE,KAAK,CAACM,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACP,qBAAqB,GAAGD,iBAAiB,CAACV,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,qBAAqB,CAACV,SAAS,MAAM;IACtN,CAAC,CAAC;EACJ,CAAC;EAED,OAAOrB,qBAAqB;AAC9B,CAAC,CAACJ,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
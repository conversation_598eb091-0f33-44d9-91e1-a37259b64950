{"name": "remove-accents", "version": "0.5.0", "description": "Converting the accented characters to their corresponding non-accented ASCII characters.", "main": "index.js", "dependencies": {}, "devDependencies": {"jest": "^29.6.2"}, "scripts": {"test": "jest -c=jest.config.js"}, "repository": {"type": "git", "url": "https://github.com/tyxla/remove-accents"}, "keywords": ["accent", "accents", "remove", "diacritic", "clean", "formatting", "umlaut", "grave", "circumflex", "tilde", "acute"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://marinatanasov.com"}, "contributors": ["tyxla", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CodeMan99", "gollenia", "glebtv", "jsnajdr", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "huy-vuong"], "bugs": {"url": "https://github.com/tyxla/remove-accents/issues"}, "homepage": "https://github.com/tyxla/remove-accents", "license": "MIT", "typings": "./index.d.ts"}
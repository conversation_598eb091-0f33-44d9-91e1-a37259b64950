{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\pages\\\\NotFound.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Home, ArrowLeft } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotFound = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-9xl font-bold text-gray-300\",\n          children: \"404\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mt-4\",\n          children: \"Page Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2\",\n          children: \"The page you're looking for doesn't exist or has been moved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/dashboard\",\n          className: \"btn btn-primary flex items-center justify-center space-x-2 w-full\",\n          children: [/*#__PURE__*/_jsxDEV(Home, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Go to Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"btn btn-outline flex items-center justify-center space-x-2 w-full\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Go Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = NotFound;\nexport default NotFound;\nvar _c;\n$RefreshReg$(_c, \"NotFound\");", "map": {"version": 3, "names": ["React", "Link", "Home", "ArrowLeft", "jsxDEV", "_jsxDEV", "NotFound", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onClick", "window", "history", "back", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/pages/NotFound.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Home, ArrowLeft } from 'lucide-react';\n\nconst NotFound = () => {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-9xl font-bold text-gray-300\">404</h1>\n          <h2 className=\"text-2xl font-bold text-gray-900 mt-4\">Page Not Found</h2>\n          <p className=\"text-gray-600 mt-2\">\n            The page you're looking for doesn't exist or has been moved.\n          </p>\n        </div>\n        \n        <div className=\"space-y-4\">\n          <Link\n            to=\"/dashboard\"\n            className=\"btn btn-primary flex items-center justify-center space-x-2 w-full\"\n          >\n            <Home className=\"h-5 w-5\" />\n            <span>Go to Dashboard</span>\n          </Link>\n          \n          <button\n            onClick={() => window.history.back()}\n            className=\"btn btn-outline flex items-center justify-center space-x-2 w-full\"\n          >\n            <ArrowLeft className=\"h-5 w-5\" />\n            <span>Go Back</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NotFound;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,IAAI,EAAEC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACED,OAAA;IAAKE,SAAS,EAAC,0DAA0D;IAAAC,QAAA,eACvEH,OAAA;MAAKE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CH,OAAA;QAAKE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBH,OAAA;UAAIE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDP,OAAA;UAAIE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEP,OAAA;UAAGE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA,CAACJ,IAAI;UACHY,EAAE,EAAC,YAAY;UACfN,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAE7EH,OAAA,CAACH,IAAI;YAACK,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BP,OAAA;YAAAG,QAAA,EAAM;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAEPP,OAAA;UACES,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCV,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAE7EH,OAAA,CAACF,SAAS;YAACI,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCP,OAAA;YAAAG,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GAhCIZ,QAAQ;AAkCd,eAAeA,QAAQ;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\pages\\\\FeeManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { DollarSign, Plus, CreditCard, Calculator, Award, TrendingUp, Users, AlertCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FeeManagement = () => {\n  _s();\n  const [showCalculator, setShowCalculator] = useState(false);\n  const [calculatorData, setCalculatorData] = useState({\n    family_income: '',\n    academic_score: '',\n    attendance_percentage: '',\n    has_sibling: false,\n    is_single_parent: false,\n    has_sports_achievement: false,\n    base_fee: 2000\n  });\n  const [calculationResult, setCalculationResult] = useState(null);\n\n  // Mock fee data\n  const feeStats = {\n    total_students: 5,\n    scholarship_recipients: 4,\n    scholarship_percentage: 80,\n    total_base_revenue: 10500,\n    total_actual_revenue: 7920,\n    total_scholarship_amount: 2580,\n    average_scholarship_percentage: 32.5,\n    scholarship_breakdown: {\n      Merit: {\n        count: 1,\n        total_savings: 500,\n        average_percentage: 25\n      },\n      'Need-based': {\n        count: 1,\n        total_savings: 880,\n        average_percentage: 40\n      },\n      Sports: {\n        count: 1,\n        total_savings: 900,\n        average_percentage: 50\n      },\n      Sibling: {\n        count: 1,\n        total_savings: 300,\n        average_percentage: 15\n      }\n    }\n  };\n  const scholarshipTypes = [{\n    type: 'Merit',\n    description: 'Academic excellence scholarship',\n    criteria: 'Academic score >= 90%',\n    percentage_range: [20, 30],\n    max_income: 60000\n  }, {\n    type: 'Need-based',\n    description: 'Financial assistance for low-income families',\n    criteria: 'Family income < 30,000',\n    percentage_range: [30, 50],\n    max_income: 30000\n  }, {\n    type: 'Sports',\n    description: 'Athletic achievement scholarship',\n    criteria: 'Sports excellence + attendance >= 95%',\n    percentage_range: [25, 50],\n    max_income: 50000\n  }, {\n    type: 'Sibling',\n    description: 'Multiple children from same family',\n    criteria: 'Second child onwards',\n    percentage_range: [10, 20],\n    max_income: 70000\n  }, {\n    type: 'Single Parent',\n    description: 'Support for single-parent families',\n    criteria: 'Single parent household',\n    percentage_range: [20, 35],\n    max_income: 40000\n  }];\n  const handleCalculatorChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setCalculatorData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const calculateScholarship = () => {\n    const {\n      family_income,\n      academic_score,\n      attendance_percentage,\n      has_sibling,\n      is_single_parent,\n      has_sports_achievement,\n      base_fee\n    } = calculatorData;\n    const eligible_scholarships = [];\n\n    // Merit scholarship\n    if (academic_score >= 90 && family_income <= 60000) {\n      const percentage = Math.min(30, 20 + (academic_score - 90) * 2);\n      eligible_scholarships.push({\n        type: 'Merit',\n        percentage: percentage,\n        amount: base_fee * percentage / 100,\n        reason: `Academic score: ${academic_score}%`\n      });\n    }\n\n    // Need-based scholarship\n    if (family_income <= 30000) {\n      const percentage = Math.max(30, Math.min(50, 60 - family_income / 1000));\n      eligible_scholarships.push({\n        type: 'Need-based',\n        percentage: percentage,\n        amount: base_fee * percentage / 100,\n        reason: `Family income: ₹${parseInt(family_income).toLocaleString()}`\n      });\n    }\n\n    // Sports scholarship\n    if (has_sports_achievement && attendance_percentage >= 95 && family_income <= 50000) {\n      const percentage = attendance_percentage >= 98 ? 40 : 30;\n      eligible_scholarships.push({\n        type: 'Sports',\n        percentage: percentage,\n        amount: base_fee * percentage / 100,\n        reason: `Sports achievement + ${attendance_percentage}% attendance`\n      });\n    }\n\n    // Sibling scholarship\n    if (has_sibling && family_income <= 70000) {\n      eligible_scholarships.push({\n        type: 'Sibling',\n        percentage: 15,\n        amount: base_fee * 15 / 100,\n        reason: 'Multiple children discount'\n      });\n    }\n\n    // Single Parent scholarship\n    if (is_single_parent && family_income <= 40000) {\n      const percentage = Math.min(35, 20 + (40000 - family_income) / 1000);\n      eligible_scholarships.push({\n        type: 'Single Parent',\n        percentage: percentage,\n        amount: base_fee * percentage / 100,\n        reason: 'Single parent household support'\n      });\n    }\n    const best_scholarship = eligible_scholarships.length > 0 ? eligible_scholarships.reduce((max, current) => current.amount > max.amount ? current : max) : null;\n    const final_fee = best_scholarship ? base_fee - best_scholarship.amount : base_fee;\n    setCalculationResult({\n      base_fee: parseInt(base_fee),\n      eligible_scholarships,\n      recommended_scholarship: best_scholarship,\n      final_fee,\n      total_savings: best_scholarship ? best_scholarship.amount : 0\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Fee Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Comprehensive fee management with scholarship support\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCalculator(!showCalculator),\n          className: \"btn btn-outline flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Calculator, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Scholarship Calculator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Generate Fees\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-lg bg-blue-100\",\n            children: /*#__PURE__*/_jsxDEV(DollarSign, {\n              className: \"h-6 w-6 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Monthly Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [\"\\u20B9\", feeStats.total_actual_revenue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"After scholarships\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-lg bg-green-100\",\n            children: /*#__PURE__*/_jsxDEV(Award, {\n              className: \"h-6 w-6 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Scholarship Savings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [\"\\u20B9\", feeStats.total_scholarship_amount.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Monthly assistance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-lg bg-purple-100\",\n            children: /*#__PURE__*/_jsxDEV(Users, {\n              className: \"h-6 w-6 text-purple-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Recipients\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: feeStats.scholarship_recipients\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: [feeStats.scholarship_percentage, \"% of students\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-lg bg-orange-100\",\n            children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-6 w-6 text-orange-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Avg. Scholarship\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [feeStats.average_scholarship_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Per recipient\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), showCalculator && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Scholarship Calculator\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Family Annual Income (\\u20B9)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"family_income\",\n              value: calculatorData.family_income,\n              onChange: handleCalculatorChange,\n              className: \"input\",\n              placeholder: \"e.g., 45000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Academic Score (%)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"academic_score\",\n              value: calculatorData.academic_score,\n              onChange: handleCalculatorChange,\n              className: \"input\",\n              placeholder: \"e.g., 92\",\n              min: \"0\",\n              max: \"100\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Attendance Percentage (%)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"attendance_percentage\",\n              value: calculatorData.attendance_percentage,\n              onChange: handleCalculatorChange,\n              className: \"input\",\n              placeholder: \"e.g., 95\",\n              min: \"0\",\n              max: \"100\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Base Monthly Fee (\\u20B9)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"base_fee\",\n              value: calculatorData.base_fee,\n              onChange: handleCalculatorChange,\n              className: \"input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Additional Criteria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"has_sibling\",\n                checked: calculatorData.has_sibling,\n                onChange: handleCalculatorChange,\n                className: \"rounded border-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Has sibling in school\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"is_single_parent\",\n                checked: calculatorData.is_single_parent,\n                onChange: handleCalculatorChange,\n                className: \"rounded border-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Single parent household\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"has_sports_achievement\",\n                checked: calculatorData.has_sports_achievement,\n                onChange: handleCalculatorChange,\n                className: \"rounded border-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Sports achievement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: calculateScholarship,\n            className: \"btn btn-primary w-full flex items-center justify-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Calculator, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Calculate Scholarship\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this), calculationResult && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-medium text-blue-900 mb-3\",\n          children: \"Scholarship Calculation Result\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [\"\\u20B9\", calculationResult.base_fee]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Base Fee\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-green-600\",\n              children: [\"\\u20B9\", Math.round(calculationResult.total_savings)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Scholarship Savings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-blue-600\",\n              children: [\"\\u20B9\", Math.round(calculationResult.final_fee)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Final Fee\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 15\n        }, this), calculationResult.recommended_scholarship ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"font-medium text-gray-900 mb-2\",\n            children: \"Recommended Scholarship\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"badge badge-success\",\n                children: calculationResult.recommended_scholarship.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: calculationResult.recommended_scholarship.reason\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg font-bold text-green-600\",\n                children: [Math.round(calculationResult.recommended_scholarship.percentage), \"% OFF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"\\u20B9\", Math.round(calculationResult.recommended_scholarship.amount), \" savings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-yellow-50 rounded-lg p-4 border border-yellow-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n              className: \"h-5 w-5 text-yellow-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-yellow-800\",\n              children: \"No scholarships available based on current criteria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 17\n        }, this), calculationResult.eligible_scholarships.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"font-medium text-gray-900 mb-2\",\n            children: \"All Eligible Scholarships\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: calculationResult.eligible_scholarships.map((scholarship, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center p-2 bg-gray-50 rounded\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: [scholarship.type, \" - \", scholarship.reason]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: [Math.round(scholarship.percentage), \"% (\\u20B9\", Math.round(scholarship.amount), \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 25\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 23\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Available Scholarship Types\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n        children: scholarshipTypes.map((scholarship, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border border-gray-200 rounded-lg p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900\",\n              children: scholarship.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-green-600 font-medium\",\n              children: [scholarship.percentage_range[0], \"-\", scholarship.percentage_range[1], \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-2\",\n            children: scholarship.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 mb-2\",\n            children: scholarship.criteria\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500\",\n            children: [\"Max income: \\u20B9\", scholarship.max_income.toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Current Scholarship Distribution\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n        children: Object.entries(feeStats.scholarship_breakdown).map(([type, data]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-bold text-gray-900\",\n            children: data.count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-green-600 font-medium\",\n            children: [\"\\u20B9\", data.total_savings.toLocaleString(), \" saved\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: [Math.round(data.average_percentage), \"% avg\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this)]\n        }, type, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(FeeManagement, \"et/SOYM+Ek2Lux5o8U9OvUqZHmA=\");\n_c = FeeManagement;\nexport default FeeManagement;\nvar _c;\n$RefreshReg$(_c, \"FeeManagement\");", "map": {"version": 3, "names": ["React", "useState", "DollarSign", "Plus", "CreditCard", "Calculator", "Award", "TrendingUp", "Users", "AlertCircle", "jsxDEV", "_jsxDEV", "FeeManagement", "_s", "showCalculator", "setShowCalculator", "calculatorData", "setCalculatorData", "family_income", "academic_score", "attendance_percentage", "has_sibling", "is_single_parent", "has_sports_achievement", "base_fee", "calculationResult", "setCalculationResult", "feeStats", "total_students", "scholarship_recipients", "scholarship_percentage", "total_base_revenue", "total_actual_revenue", "total_scholarship_amount", "average_scholarship_percentage", "scholarship_breakdown", "Merit", "count", "total_savings", "average_percentage", "Sports", "Sibling", "scholarshipTypes", "type", "description", "criteria", "percentage_range", "max_income", "handleCalculatorChange", "e", "name", "value", "checked", "target", "prev", "calculateScholarship", "eligible_scholarships", "percentage", "Math", "min", "push", "amount", "reason", "max", "parseInt", "toLocaleString", "best_scholarship", "length", "reduce", "current", "final_fee", "recommended_scholarship", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onChange", "placeholder", "round", "map", "scholarship", "index", "Object", "entries", "data", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/pages/FeeManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { DollarSign, Plus, CreditCard, Calculator, Award, TrendingUp, Users, AlertCircle } from 'lucide-react';\n\nconst FeeManagement = () => {\n  const [showCalculator, setShowCalculator] = useState(false);\n  const [calculatorData, setCalculatorData] = useState({\n    family_income: '',\n    academic_score: '',\n    attendance_percentage: '',\n    has_sibling: false,\n    is_single_parent: false,\n    has_sports_achievement: false,\n    base_fee: 2000\n  });\n  const [calculationResult, setCalculationResult] = useState(null);\n\n  // Mock fee data\n  const feeStats = {\n    total_students: 5,\n    scholarship_recipients: 4,\n    scholarship_percentage: 80,\n    total_base_revenue: 10500,\n    total_actual_revenue: 7920,\n    total_scholarship_amount: 2580,\n    average_scholarship_percentage: 32.5,\n    scholarship_breakdown: {\n      Merit: { count: 1, total_savings: 500, average_percentage: 25 },\n      'Need-based': { count: 1, total_savings: 880, average_percentage: 40 },\n      Sports: { count: 1, total_savings: 900, average_percentage: 50 },\n      Sibling: { count: 1, total_savings: 300, average_percentage: 15 }\n    }\n  };\n\n  const scholarshipTypes = [\n    {\n      type: 'Merit',\n      description: 'Academic excellence scholarship',\n      criteria: 'Academic score >= 90%',\n      percentage_range: [20, 30],\n      max_income: 60000\n    },\n    {\n      type: 'Need-based',\n      description: 'Financial assistance for low-income families',\n      criteria: 'Family income < 30,000',\n      percentage_range: [30, 50],\n      max_income: 30000\n    },\n    {\n      type: 'Sports',\n      description: 'Athletic achievement scholarship',\n      criteria: 'Sports excellence + attendance >= 95%',\n      percentage_range: [25, 50],\n      max_income: 50000\n    },\n    {\n      type: 'Sibling',\n      description: 'Multiple children from same family',\n      criteria: 'Second child onwards',\n      percentage_range: [10, 20],\n      max_income: 70000\n    },\n    {\n      type: 'Single Parent',\n      description: 'Support for single-parent families',\n      criteria: 'Single parent household',\n      percentage_range: [20, 35],\n      max_income: 40000\n    }\n  ];\n\n  const handleCalculatorChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setCalculatorData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const calculateScholarship = () => {\n    const { family_income, academic_score, attendance_percentage, has_sibling, is_single_parent, has_sports_achievement, base_fee } = calculatorData;\n\n    const eligible_scholarships = [];\n\n    // Merit scholarship\n    if (academic_score >= 90 && family_income <= 60000) {\n      const percentage = Math.min(30, 20 + (academic_score - 90) * 2);\n      eligible_scholarships.push({\n        type: 'Merit',\n        percentage: percentage,\n        amount: base_fee * percentage / 100,\n        reason: `Academic score: ${academic_score}%`\n      });\n    }\n\n    // Need-based scholarship\n    if (family_income <= 30000) {\n      const percentage = Math.max(30, Math.min(50, 60 - (family_income / 1000)));\n      eligible_scholarships.push({\n        type: 'Need-based',\n        percentage: percentage,\n        amount: base_fee * percentage / 100,\n        reason: `Family income: ₹${parseInt(family_income).toLocaleString()}`\n      });\n    }\n\n    // Sports scholarship\n    if (has_sports_achievement && attendance_percentage >= 95 && family_income <= 50000) {\n      const percentage = attendance_percentage >= 98 ? 40 : 30;\n      eligible_scholarships.push({\n        type: 'Sports',\n        percentage: percentage,\n        amount: base_fee * percentage / 100,\n        reason: `Sports achievement + ${attendance_percentage}% attendance`\n      });\n    }\n\n    // Sibling scholarship\n    if (has_sibling && family_income <= 70000) {\n      eligible_scholarships.push({\n        type: 'Sibling',\n        percentage: 15,\n        amount: base_fee * 15 / 100,\n        reason: 'Multiple children discount'\n      });\n    }\n\n    // Single Parent scholarship\n    if (is_single_parent && family_income <= 40000) {\n      const percentage = Math.min(35, 20 + (40000 - family_income) / 1000);\n      eligible_scholarships.push({\n        type: 'Single Parent',\n        percentage: percentage,\n        amount: base_fee * percentage / 100,\n        reason: 'Single parent household support'\n      });\n    }\n\n    const best_scholarship = eligible_scholarships.length > 0\n      ? eligible_scholarships.reduce((max, current) => current.amount > max.amount ? current : max)\n      : null;\n\n    const final_fee = best_scholarship ? base_fee - best_scholarship.amount : base_fee;\n\n    setCalculationResult({\n      base_fee: parseInt(base_fee),\n      eligible_scholarships,\n      recommended_scholarship: best_scholarship,\n      final_fee,\n      total_savings: best_scholarship ? best_scholarship.amount : 0\n    });\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Fee Management</h1>\n          <p className=\"text-gray-600\">Comprehensive fee management with scholarship support</p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={() => setShowCalculator(!showCalculator)}\n            className=\"btn btn-outline flex items-center space-x-2\"\n          >\n            <Calculator className=\"h-5 w-5\" />\n            <span>Scholarship Calculator</span>\n          </button>\n          <button className=\"btn btn-primary flex items-center space-x-2\">\n            <Plus className=\"h-5 w-5\" />\n            <span>Generate Fees</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Fee Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"card\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-lg bg-blue-100\">\n              <DollarSign className=\"h-6 w-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Monthly Revenue</p>\n              <p className=\"text-2xl font-bold text-gray-900\">₹{feeStats.total_actual_revenue.toLocaleString()}</p>\n              <p className=\"text-xs text-gray-500\">After scholarships</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-lg bg-green-100\">\n              <Award className=\"h-6 w-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Scholarship Savings</p>\n              <p className=\"text-2xl font-bold text-gray-900\">₹{feeStats.total_scholarship_amount.toLocaleString()}</p>\n              <p className=\"text-xs text-gray-500\">Monthly assistance</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-lg bg-purple-100\">\n              <Users className=\"h-6 w-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Recipients</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{feeStats.scholarship_recipients}</p>\n              <p className=\"text-xs text-gray-500\">{feeStats.scholarship_percentage}% of students</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-lg bg-orange-100\">\n              <TrendingUp className=\"h-6 w-6 text-orange-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Avg. Scholarship</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{feeStats.average_scholarship_percentage}%</p>\n              <p className=\"text-xs text-gray-500\">Per recipient</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scholarship Calculator */}\n      {showCalculator && (\n        <div className=\"card\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Scholarship Calculator</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"form-label\">Family Annual Income (₹)</label>\n                <input\n                  type=\"number\"\n                  name=\"family_income\"\n                  value={calculatorData.family_income}\n                  onChange={handleCalculatorChange}\n                  className=\"input\"\n                  placeholder=\"e.g., 45000\"\n                />\n              </div>\n\n              <div>\n                <label className=\"form-label\">Academic Score (%)</label>\n                <input\n                  type=\"number\"\n                  name=\"academic_score\"\n                  value={calculatorData.academic_score}\n                  onChange={handleCalculatorChange}\n                  className=\"input\"\n                  placeholder=\"e.g., 92\"\n                  min=\"0\"\n                  max=\"100\"\n                />\n              </div>\n\n              <div>\n                <label className=\"form-label\">Attendance Percentage (%)</label>\n                <input\n                  type=\"number\"\n                  name=\"attendance_percentage\"\n                  value={calculatorData.attendance_percentage}\n                  onChange={handleCalculatorChange}\n                  className=\"input\"\n                  placeholder=\"e.g., 95\"\n                  min=\"0\"\n                  max=\"100\"\n                />\n              </div>\n\n              <div>\n                <label className=\"form-label\">Base Monthly Fee (₹)</label>\n                <input\n                  type=\"number\"\n                  name=\"base_fee\"\n                  value={calculatorData.base_fee}\n                  onChange={handleCalculatorChange}\n                  className=\"input\"\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"space-y-3\">\n                <label className=\"form-label\">Additional Criteria</label>\n\n                <label className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"checkbox\"\n                    name=\"has_sibling\"\n                    checked={calculatorData.has_sibling}\n                    onChange={handleCalculatorChange}\n                    className=\"rounded border-gray-300\"\n                  />\n                  <span className=\"text-sm\">Has sibling in school</span>\n                </label>\n\n                <label className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"checkbox\"\n                    name=\"is_single_parent\"\n                    checked={calculatorData.is_single_parent}\n                    onChange={handleCalculatorChange}\n                    className=\"rounded border-gray-300\"\n                  />\n                  <span className=\"text-sm\">Single parent household</span>\n                </label>\n\n                <label className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"checkbox\"\n                    name=\"has_sports_achievement\"\n                    checked={calculatorData.has_sports_achievement}\n                    onChange={handleCalculatorChange}\n                    className=\"rounded border-gray-300\"\n                  />\n                  <span className=\"text-sm\">Sports achievement</span>\n                </label>\n              </div>\n\n              <button\n                onClick={calculateScholarship}\n                className=\"btn btn-primary w-full flex items-center justify-center space-x-2\"\n              >\n                <Calculator className=\"h-5 w-5\" />\n                <span>Calculate Scholarship</span>\n              </button>\n            </div>\n          </div>\n\n          {/* Calculation Result */}\n          {calculationResult && (\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\">\n              <h4 className=\"text-lg font-medium text-blue-900 mb-3\">Scholarship Calculation Result</h4>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-gray-900\">₹{calculationResult.base_fee}</div>\n                  <div className=\"text-sm text-gray-600\">Base Fee</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">₹{Math.round(calculationResult.total_savings)}</div>\n                  <div className=\"text-sm text-gray-600\">Scholarship Savings</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-600\">₹{Math.round(calculationResult.final_fee)}</div>\n                  <div className=\"text-sm text-gray-600\">Final Fee</div>\n                </div>\n              </div>\n\n              {calculationResult.recommended_scholarship ? (\n                <div className=\"bg-white rounded-lg p-4\">\n                  <h5 className=\"font-medium text-gray-900 mb-2\">Recommended Scholarship</h5>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <span className=\"badge badge-success\">{calculationResult.recommended_scholarship.type}</span>\n                      <p className=\"text-sm text-gray-600 mt-1\">{calculationResult.recommended_scholarship.reason}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-bold text-green-600\">\n                        {Math.round(calculationResult.recommended_scholarship.percentage)}% OFF\n                      </div>\n                      <div className=\"text-sm text-gray-600\">\n                        ₹{Math.round(calculationResult.recommended_scholarship.amount)} savings\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"bg-yellow-50 rounded-lg p-4 border border-yellow-200\">\n                  <div className=\"flex items-center space-x-2\">\n                    <AlertCircle className=\"h-5 w-5 text-yellow-600\" />\n                    <span className=\"text-yellow-800\">No scholarships available based on current criteria</span>\n                  </div>\n                </div>\n              )}\n\n              {calculationResult.eligible_scholarships.length > 1 && (\n                <div className=\"mt-4\">\n                  <h5 className=\"font-medium text-gray-900 mb-2\">All Eligible Scholarships</h5>\n                  <div className=\"space-y-2\">\n                    {calculationResult.eligible_scholarships.map((scholarship, index) => (\n                      <div key={index} className=\"flex justify-between items-center p-2 bg-gray-50 rounded\">\n                        <span className=\"text-sm\">{scholarship.type} - {scholarship.reason}</span>\n                        <span className=\"text-sm font-medium\">{Math.round(scholarship.percentage)}% (₹{Math.round(scholarship.amount)})</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Scholarship Types */}\n      <div className=\"card\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Available Scholarship Types</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {scholarshipTypes.map((scholarship, index) => (\n            <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h4 className=\"font-medium text-gray-900\">{scholarship.type}</h4>\n                <span className=\"text-sm text-green-600 font-medium\">\n                  {scholarship.percentage_range[0]}-{scholarship.percentage_range[1]}%\n                </span>\n              </div>\n              <p className=\"text-sm text-gray-600 mb-2\">{scholarship.description}</p>\n              <p className=\"text-xs text-gray-500 mb-2\">{scholarship.criteria}</p>\n              <p className=\"text-xs text-gray-500\">Max income: ₹{scholarship.max_income.toLocaleString()}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Scholarship Breakdown */}\n      <div className=\"card\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Current Scholarship Distribution</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          {Object.entries(feeStats.scholarship_breakdown).map(([type, data]) => (\n            <div key={type} className=\"text-center p-4 bg-gray-50 rounded-lg\">\n              <div className=\"text-lg font-bold text-gray-900\">{data.count}</div>\n              <div className=\"text-sm text-gray-600\">{type}</div>\n              <div className=\"text-xs text-green-600 font-medium\">₹{data.total_savings.toLocaleString()} saved</div>\n              <div className=\"text-xs text-gray-500\">{Math.round(data.average_percentage)}% avg</div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FeeManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,UAAU,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/G,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC;IACnDiB,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,qBAAqB,EAAE,EAAE;IACzBC,WAAW,EAAE,KAAK;IAClBC,gBAAgB,EAAE,KAAK;IACvBC,sBAAsB,EAAE,KAAK;IAC7BC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM0B,QAAQ,GAAG;IACfC,cAAc,EAAE,CAAC;IACjBC,sBAAsB,EAAE,CAAC;IACzBC,sBAAsB,EAAE,EAAE;IAC1BC,kBAAkB,EAAE,KAAK;IACzBC,oBAAoB,EAAE,IAAI;IAC1BC,wBAAwB,EAAE,IAAI;IAC9BC,8BAA8B,EAAE,IAAI;IACpCC,qBAAqB,EAAE;MACrBC,KAAK,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE,GAAG;QAAEC,kBAAkB,EAAE;MAAG,CAAC;MAC/D,YAAY,EAAE;QAAEF,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE,GAAG;QAAEC,kBAAkB,EAAE;MAAG,CAAC;MACtEC,MAAM,EAAE;QAAEH,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE,GAAG;QAAEC,kBAAkB,EAAE;MAAG,CAAC;MAChEE,OAAO,EAAE;QAAEJ,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE,GAAG;QAAEC,kBAAkB,EAAE;MAAG;IAClE;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAG,CACvB;IACEC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,iCAAiC;IAC9CC,QAAQ,EAAE,uBAAuB;IACjCC,gBAAgB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAC1BC,UAAU,EAAE;EACd,CAAC,EACD;IACEJ,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,8CAA8C;IAC3DC,QAAQ,EAAE,wBAAwB;IAClCC,gBAAgB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAC1BC,UAAU,EAAE;EACd,CAAC,EACD;IACEJ,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,kCAAkC;IAC/CC,QAAQ,EAAE,uCAAuC;IACjDC,gBAAgB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAC1BC,UAAU,EAAE;EACd,CAAC,EACD;IACEJ,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,oCAAoC;IACjDC,QAAQ,EAAE,sBAAsB;IAChCC,gBAAgB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAC1BC,UAAU,EAAE;EACd,CAAC,EACD;IACEJ,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE,oCAAoC;IACjDC,QAAQ,EAAE,yBAAyB;IACnCC,gBAAgB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAC1BC,UAAU,EAAE;EACd,CAAC,CACF;EAED,MAAMC,sBAAsB,GAAIC,CAAC,IAAK;IACpC,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAER,IAAI;MAAES;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CpC,iBAAiB,CAACqC,IAAI,KAAK;MACzB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGP,IAAI,KAAK,UAAU,GAAGS,OAAO,GAAGD;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAM;MAAErC,aAAa;MAAEC,cAAc;MAAEC,qBAAqB;MAAEC,WAAW;MAAEC,gBAAgB;MAAEC,sBAAsB;MAAEC;IAAS,CAAC,GAAGR,cAAc;IAEhJ,MAAMwC,qBAAqB,GAAG,EAAE;;IAEhC;IACA,IAAIrC,cAAc,IAAI,EAAE,IAAID,aAAa,IAAI,KAAK,EAAE;MAClD,MAAMuC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAACxC,cAAc,GAAG,EAAE,IAAI,CAAC,CAAC;MAC/DqC,qBAAqB,CAACI,IAAI,CAAC;QACzBjB,IAAI,EAAE,OAAO;QACbc,UAAU,EAAEA,UAAU;QACtBI,MAAM,EAAErC,QAAQ,GAAGiC,UAAU,GAAG,GAAG;QACnCK,MAAM,EAAE,mBAAmB3C,cAAc;MAC3C,CAAC,CAAC;IACJ;;IAEA;IACA,IAAID,aAAa,IAAI,KAAK,EAAE;MAC1B,MAAMuC,UAAU,GAAGC,IAAI,CAACK,GAAG,CAAC,EAAE,EAAEL,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIzC,aAAa,GAAG,IAAK,CAAC,CAAC;MAC1EsC,qBAAqB,CAACI,IAAI,CAAC;QACzBjB,IAAI,EAAE,YAAY;QAClBc,UAAU,EAAEA,UAAU;QACtBI,MAAM,EAAErC,QAAQ,GAAGiC,UAAU,GAAG,GAAG;QACnCK,MAAM,EAAE,mBAAmBE,QAAQ,CAAC9C,aAAa,CAAC,CAAC+C,cAAc,CAAC,CAAC;MACrE,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI1C,sBAAsB,IAAIH,qBAAqB,IAAI,EAAE,IAAIF,aAAa,IAAI,KAAK,EAAE;MACnF,MAAMuC,UAAU,GAAGrC,qBAAqB,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;MACxDoC,qBAAqB,CAACI,IAAI,CAAC;QACzBjB,IAAI,EAAE,QAAQ;QACdc,UAAU,EAAEA,UAAU;QACtBI,MAAM,EAAErC,QAAQ,GAAGiC,UAAU,GAAG,GAAG;QACnCK,MAAM,EAAE,wBAAwB1C,qBAAqB;MACvD,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIC,WAAW,IAAIH,aAAa,IAAI,KAAK,EAAE;MACzCsC,qBAAqB,CAACI,IAAI,CAAC;QACzBjB,IAAI,EAAE,SAAS;QACfc,UAAU,EAAE,EAAE;QACdI,MAAM,EAAErC,QAAQ,GAAG,EAAE,GAAG,GAAG;QAC3BsC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIxC,gBAAgB,IAAIJ,aAAa,IAAI,KAAK,EAAE;MAC9C,MAAMuC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,KAAK,GAAGzC,aAAa,IAAI,IAAI,CAAC;MACpEsC,qBAAqB,CAACI,IAAI,CAAC;QACzBjB,IAAI,EAAE,eAAe;QACrBc,UAAU,EAAEA,UAAU;QACtBI,MAAM,EAAErC,QAAQ,GAAGiC,UAAU,GAAG,GAAG;QACnCK,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IAEA,MAAMI,gBAAgB,GAAGV,qBAAqB,CAACW,MAAM,GAAG,CAAC,GACrDX,qBAAqB,CAACY,MAAM,CAAC,CAACL,GAAG,EAAEM,OAAO,KAAKA,OAAO,CAACR,MAAM,GAAGE,GAAG,CAACF,MAAM,GAAGQ,OAAO,GAAGN,GAAG,CAAC,GAC3F,IAAI;IAER,MAAMO,SAAS,GAAGJ,gBAAgB,GAAG1C,QAAQ,GAAG0C,gBAAgB,CAACL,MAAM,GAAGrC,QAAQ;IAElFE,oBAAoB,CAAC;MACnBF,QAAQ,EAAEwC,QAAQ,CAACxC,QAAQ,CAAC;MAC5BgC,qBAAqB;MACrBe,uBAAuB,EAAEL,gBAAgB;MACzCI,SAAS;MACThC,aAAa,EAAE4B,gBAAgB,GAAGA,gBAAgB,CAACL,MAAM,GAAG;IAC9D,CAAC,CAAC;EACJ,CAAC;EAED,oBACElD,OAAA;IAAK6D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB9D,OAAA;MAAK6D,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD9D,OAAA;QAAA8D,QAAA,gBACE9D,OAAA;UAAI6D,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpElE,OAAA;UAAG6D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAqD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC,eACNlE,OAAA;QAAK6D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9D,OAAA;UACEmE,OAAO,EAAEA,CAAA,KAAM/D,iBAAiB,CAAC,CAACD,cAAc,CAAE;UAClD0D,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAEvD9D,OAAA,CAACN,UAAU;YAACmE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClClE,OAAA;YAAA8D,QAAA,EAAM;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACTlE,OAAA;UAAQ6D,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC7D9D,OAAA,CAACR,IAAI;YAACqE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BlE,OAAA;YAAA8D,QAAA,EAAM;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD9D,OAAA;QAAK6D,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB9D,OAAA;UAAK6D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9D,OAAA;YAAK6D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzC9D,OAAA,CAACT,UAAU;cAACsE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNlE,OAAA;YAAK6D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB9D,OAAA;cAAG6D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpElE,OAAA;cAAG6D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,QAAC,EAAC9C,QAAQ,CAACK,oBAAoB,CAACiC,cAAc,CAAC,CAAC;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrGlE,OAAA;cAAG6D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB9D,OAAA;UAAK6D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9D,OAAA;YAAK6D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C9D,OAAA,CAACL,KAAK;cAACkE,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNlE,OAAA;YAAK6D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB9D,OAAA;cAAG6D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxElE,OAAA;cAAG6D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,QAAC,EAAC9C,QAAQ,CAACM,wBAAwB,CAACgC,cAAc,CAAC,CAAC;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzGlE,OAAA;cAAG6D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB9D,OAAA;UAAK6D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9D,OAAA;YAAK6D,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C9D,OAAA,CAACH,KAAK;cAACgE,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNlE,OAAA;YAAK6D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB9D,OAAA;cAAG6D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/DlE,OAAA;cAAG6D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAE9C,QAAQ,CAACE;YAAsB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrFlE,OAAA;cAAG6D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAE9C,QAAQ,CAACG,sBAAsB,EAAC,eAAa;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB9D,OAAA;UAAK6D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9D,OAAA;YAAK6D,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C9D,OAAA,CAACJ,UAAU;cAACiE,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNlE,OAAA;YAAK6D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB9D,OAAA;cAAG6D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrElE,OAAA;cAAG6D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAE9C,QAAQ,CAACO,8BAA8B,EAAC,GAAC;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9FlE,OAAA;cAAG6D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/D,cAAc,iBACbH,OAAA;MAAK6D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB9D,OAAA;QAAI6D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClFlE,OAAA;QAAK6D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD9D,OAAA;UAAK6D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9D,OAAA;YAAA8D,QAAA,gBACE9D,OAAA;cAAO6D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9DlE,OAAA;cACEgC,IAAI,EAAC,QAAQ;cACbO,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAEnC,cAAc,CAACE,aAAc;cACpC6D,QAAQ,EAAE/B,sBAAuB;cACjCwB,SAAS,EAAC,OAAO;cACjBQ,WAAW,EAAC;YAAa;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlE,OAAA;YAAA8D,QAAA,gBACE9D,OAAA;cAAO6D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxDlE,OAAA;cACEgC,IAAI,EAAC,QAAQ;cACbO,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAEnC,cAAc,CAACG,cAAe;cACrC4D,QAAQ,EAAE/B,sBAAuB;cACjCwB,SAAS,EAAC,OAAO;cACjBQ,WAAW,EAAC,UAAU;cACtBrB,GAAG,EAAC,GAAG;cACPI,GAAG,EAAC;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlE,OAAA;YAAA8D,QAAA,gBACE9D,OAAA;cAAO6D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/DlE,OAAA;cACEgC,IAAI,EAAC,QAAQ;cACbO,IAAI,EAAC,uBAAuB;cAC5BC,KAAK,EAAEnC,cAAc,CAACI,qBAAsB;cAC5C2D,QAAQ,EAAE/B,sBAAuB;cACjCwB,SAAS,EAAC,OAAO;cACjBQ,WAAW,EAAC,UAAU;cACtBrB,GAAG,EAAC,GAAG;cACPI,GAAG,EAAC;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlE,OAAA;YAAA8D,QAAA,gBACE9D,OAAA;cAAO6D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1DlE,OAAA;cACEgC,IAAI,EAAC,QAAQ;cACbO,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEnC,cAAc,CAACQ,QAAS;cAC/BuD,QAAQ,EAAE/B,sBAAuB;cACjCwB,SAAS,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlE,OAAA;UAAK6D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9D,OAAA;YAAK6D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9D,OAAA;cAAO6D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAEzDlE,OAAA;cAAO6D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC5C9D,OAAA;gBACEgC,IAAI,EAAC,UAAU;gBACfO,IAAI,EAAC,aAAa;gBAClBE,OAAO,EAAEpC,cAAc,CAACK,WAAY;gBACpC0D,QAAQ,EAAE/B,sBAAuB;gBACjCwB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACFlE,OAAA;gBAAM6D,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eAERlE,OAAA;cAAO6D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC5C9D,OAAA;gBACEgC,IAAI,EAAC,UAAU;gBACfO,IAAI,EAAC,kBAAkB;gBACvBE,OAAO,EAAEpC,cAAc,CAACM,gBAAiB;gBACzCyD,QAAQ,EAAE/B,sBAAuB;gBACjCwB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACFlE,OAAA;gBAAM6D,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAERlE,OAAA;cAAO6D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC5C9D,OAAA;gBACEgC,IAAI,EAAC,UAAU;gBACfO,IAAI,EAAC,wBAAwB;gBAC7BE,OAAO,EAAEpC,cAAc,CAACO,sBAAuB;gBAC/CwD,QAAQ,EAAE/B,sBAAuB;gBACjCwB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACFlE,OAAA;gBAAM6D,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENlE,OAAA;YACEmE,OAAO,EAAEvB,oBAAqB;YAC9BiB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBAE7E9D,OAAA,CAACN,UAAU;cAACmE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClClE,OAAA;cAAA8D,QAAA,EAAM;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLpD,iBAAiB,iBAChBd,OAAA;QAAK6D,SAAS,EAAC,uDAAuD;QAAAC,QAAA,gBACpE9D,OAAA;UAAI6D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE1FlE,OAAA;UAAK6D,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzD9D,OAAA;YAAK6D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9D,OAAA;cAAK6D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,QAAC,EAAChD,iBAAiB,CAACD,QAAQ;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrFlE,OAAA;cAAK6D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNlE,OAAA;YAAK6D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9D,OAAA;cAAK6D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,QAAC,EAACf,IAAI,CAACuB,KAAK,CAACxD,iBAAiB,CAACa,aAAa,CAAC;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvGlE,OAAA;cAAK6D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNlE,OAAA;YAAK6D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9D,OAAA;cAAK6D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,QAAC,EAACf,IAAI,CAACuB,KAAK,CAACxD,iBAAiB,CAAC6C,SAAS,CAAC;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClGlE,OAAA;cAAK6D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELpD,iBAAiB,CAAC8C,uBAAuB,gBACxC5D,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC9D,OAAA;YAAI6D,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3ElE,OAAA;YAAK6D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9D,OAAA;cAAA8D,QAAA,gBACE9D,OAAA;gBAAM6D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEhD,iBAAiB,CAAC8C,uBAAuB,CAAC5B;cAAI;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7FlE,OAAA;gBAAG6D,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEhD,iBAAiB,CAAC8C,uBAAuB,CAACT;cAAM;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eACNlE,OAAA;cAAK6D,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9D,OAAA;gBAAK6D,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAC9Cf,IAAI,CAACuB,KAAK,CAACxD,iBAAiB,CAAC8C,uBAAuB,CAACd,UAAU,CAAC,EAAC,OACpE;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlE,OAAA;gBAAK6D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,QACpC,EAACf,IAAI,CAACuB,KAAK,CAACxD,iBAAiB,CAAC8C,uBAAuB,CAACV,MAAM,CAAC,EAAC,UACjE;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENlE,OAAA;UAAK6D,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eACnE9D,OAAA;YAAK6D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C9D,OAAA,CAACF,WAAW;cAAC+D,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDlE,OAAA;cAAM6D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAmD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEApD,iBAAiB,CAAC+B,qBAAqB,CAACW,MAAM,GAAG,CAAC,iBACjDxD,OAAA;UAAK6D,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB9D,OAAA;YAAI6D,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ElE,OAAA;YAAK6D,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBhD,iBAAiB,CAAC+B,qBAAqB,CAAC0B,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAC9DzE,OAAA;cAAiB6D,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACnF9D,OAAA;gBAAM6D,SAAS,EAAC,SAAS;gBAAAC,QAAA,GAAEU,WAAW,CAACxC,IAAI,EAAC,KAAG,EAACwC,WAAW,CAACrB,MAAM;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1ElE,OAAA;gBAAM6D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,GAAEf,IAAI,CAACuB,KAAK,CAACE,WAAW,CAAC1B,UAAU,CAAC,EAAC,WAAI,EAACC,IAAI,CAACuB,KAAK,CAACE,WAAW,CAACtB,MAAM,CAAC,EAAC,GAAC;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAF9GO,KAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDlE,OAAA;MAAK6D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB9D,OAAA;QAAI6D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvFlE,OAAA;QAAK6D,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE/B,gBAAgB,CAACwC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACvCzE,OAAA;UAAiB6D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAChE9D,OAAA;YAAK6D,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9D,OAAA;cAAI6D,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEU,WAAW,CAACxC;YAAI;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjElE,OAAA;cAAM6D,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GACjDU,WAAW,CAACrC,gBAAgB,CAAC,CAAC,CAAC,EAAC,GAAC,EAACqC,WAAW,CAACrC,gBAAgB,CAAC,CAAC,CAAC,EAAC,GACrE;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNlE,OAAA;YAAG6D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAEU,WAAW,CAACvC;UAAW;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvElE,OAAA;YAAG6D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAEU,WAAW,CAACtC;UAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpElE,OAAA;YAAG6D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,oBAAa,EAACU,WAAW,CAACpC,UAAU,CAACkB,cAAc,CAAC,CAAC;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GATvFO,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB9D,OAAA;QAAI6D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5FlE,OAAA;QAAK6D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDY,MAAM,CAACC,OAAO,CAAC3D,QAAQ,CAACQ,qBAAqB,CAAC,CAAC+C,GAAG,CAAC,CAAC,CAACvC,IAAI,EAAE4C,IAAI,CAAC,kBAC/D5E,OAAA;UAAgB6D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAC/D9D,OAAA;YAAK6D,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEc,IAAI,CAAClD;UAAK;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnElE,OAAA;YAAK6D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAE9B;UAAI;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDlE,OAAA;YAAK6D,SAAS,EAAC,oCAAoC;YAAAC,QAAA,GAAC,QAAC,EAACc,IAAI,CAACjD,aAAa,CAAC2B,cAAc,CAAC,CAAC,EAAC,QAAM;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtGlE,OAAA;YAAK6D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAEf,IAAI,CAACuB,KAAK,CAACM,IAAI,CAAChD,kBAAkB,CAAC,EAAC,OAAK;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA,GAJ/ElC,IAAI;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChE,EAAA,CAnbID,aAAa;AAAA4E,EAAA,GAAb5E,aAAa;AAqbnB,eAAeA,aAAa;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
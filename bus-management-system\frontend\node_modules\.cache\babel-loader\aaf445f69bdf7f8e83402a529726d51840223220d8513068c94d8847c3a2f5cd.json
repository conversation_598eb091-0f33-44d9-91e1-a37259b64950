{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst RedoDot = createLucideIcon(\"RedoDot\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"17\",\n  r: \"1\",\n  key: \"1ixnty\"\n}], [\"path\", {\n  d: \"M21 7v6h-6\",\n  key: \"3ptur4\"\n}], [\"path\", {\n  d: \"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7\",\n  key: \"1kgawr\"\n}]]);\nexport { RedoDot as default };", "map": {"version": 3, "names": ["RedoDot", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\redo-dot.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RedoDot\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE3IiByPSIxIiAvPgogIDxwYXRoIGQ9Ik0yMSA3djZoLTYiIC8+CiAgPHBhdGggZD0iTTMgMTdhOSA5IDAgMCAxIDktOSA5IDkgMCAwIDEgNiAyLjNsMyAyLjciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/redo-dot\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RedoDot = createLucideIcon('RedoDot', [\n  ['circle', { cx: '12', cy: '17', r: '1', key: '1ixnty' }],\n  ['path', { d: 'M21 7v6h-6', key: '3ptur4' }],\n  ['path', { d: 'M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7', key: '1kgawr' }],\n]);\n\nexport default RedoDot;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
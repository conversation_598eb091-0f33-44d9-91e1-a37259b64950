{"version": 3, "names": ["_semver", "require", "_availablePlugins", "addProposalSyntaxPlugins", "items", "proposalSyntaxPlugins", "for<PERSON>ach", "plugin", "add", "removeUnnecessaryItems", "overlapping", "item", "_overlapping$item", "name", "delete", "removeUnsupportedItems", "babelVersion", "hasOwnProperty", "call", "minVersions", "semver", "lt", "legacyBabel7SyntaxPlugins", "has"], "sources": ["../src/filter-items.ts"], "sourcesContent": ["import semver from \"semver\";\nimport { minVersions, legacyBabel7SyntaxPlugins } from \"./available-plugins.ts\";\n\nexport function addProposalSyntaxPlugins(\n  items: Set<string>,\n  proposalSyntaxPlugins: readonly string[],\n) {\n  proposalSyntaxPlugins.forEach(plugin => {\n    items.add(plugin);\n  });\n}\nexport function removeUnnecessaryItems(\n  items: Set<string>,\n  overlapping: { [name: string]: string[] },\n) {\n  items.forEach(item => {\n    overlapping[item]?.forEach(name => items.delete(name));\n  });\n}\nexport function removeUnsupportedItems(\n  items: Set<string>,\n  babelVersion: string,\n) {\n  items.forEach(item => {\n    if (\n      Object.hasOwn(minVersions, item) &&\n      semver.lt(\n        babelVersion,\n        // @ts-expect-error we have checked minVersions[item] in has call\n        minVersions[item],\n      )\n    ) {\n      items.delete(item);\n    } else if (\n      !process.env.BABEL_8_BREAKING &&\n      babelVersion[0] === \"8\" &&\n      legacyBabel7SyntaxPlugins.has(item)\n    ) {\n      items.delete(item);\n    }\n  });\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AAEO,SAASE,wBAAwBA,CACtCC,KAAkB,EAClBC,qBAAwC,EACxC;EACAA,qBAAqB,CAACC,OAAO,CAACC,MAAM,IAAI;IACtCH,KAAK,CAACI,GAAG,CAACD,MAAM,CAAC;EACnB,CAAC,CAAC;AACJ;AACO,SAASE,sBAAsBA,CACpCL,KAAkB,EAClBM,WAAyC,EACzC;EACAN,KAAK,CAACE,OAAO,CAACK,IAAI,IAAI;IAAA,IAAAC,iBAAA;IACpB,CAAAA,iBAAA,GAAAF,WAAW,CAACC,IAAI,CAAC,aAAjBC,iBAAA,CAAmBN,OAAO,CAACO,IAAI,IAAIT,KAAK,CAACU,MAAM,CAACD,IAAI,CAAC,CAAC;EACxD,CAAC,CAAC;AACJ;AACO,SAASE,sBAAsBA,CACpCX,KAAkB,EAClBY,YAAoB,EACpB;EACAZ,KAAK,CAACE,OAAO,CAACK,IAAI,IAAI;IACpB,IACEM,cAAA,CAAAC,IAAA,CAAcC,6BAAW,EAAER,IAAI,CAAC,IAChCS,OAAM,CAACC,EAAE,CACPL,YAAY,EAEZG,6BAAW,CAACR,IAAI,CAClB,CAAC,EACD;MACAP,KAAK,CAACU,MAAM,CAACH,IAAI,CAAC;IACpB,CAAC,MAAM,IAELK,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IACvBM,2CAAyB,CAACC,GAAG,CAACZ,IAAI,CAAC,EACnC;MACAP,KAAK,CAACU,MAAM,CAACH,IAAI,CAAC;IACpB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}
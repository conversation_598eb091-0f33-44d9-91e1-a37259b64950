{"version": 3, "file": "index.mjs", "sources": ["../src/index.ts"], "sourcesContent": ["import defineProvider from \"@babel/helper-define-polyfill-provider\";\nimport type { PluginPass } from \"@babel/core\";\n\nconst runtimeCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\ntype Options = {\n  \"#__secret_key__@babel/runtime__compatibility\": void | {\n    useBabelRuntime: boolean;\n    moduleName: string;\n  };\n};\n\nexport default defineProvider<Options>(({ debug, targets, babel }, options) => {\n  if (!shallowEqual(targets, babel.targets())) {\n    throw new Error(\n      \"This plugin does not use the targets option. Only preset-env's targets\" +\n        \" or top-level targets need to be configured for this plugin to work.\" +\n        \" See https://github.com/babel/babel-polyfills/issues/36 for more\" +\n        \" details.\",\n    );\n  }\n\n  const {\n    [runtimeCompat]: { moduleName = null, useBabelRuntime = false } = {},\n  } = options;\n\n  return {\n    name: \"regenerator\",\n\n    polyfills: [\"regenerator-runtime\"],\n\n    usageGlobal(meta, utils): undefined {\n      if (isRegenerator(meta)) {\n        debug(\"regenerator-runtime\");\n        utils.injectGlobalImport(\"regenerator-runtime/runtime.js\");\n      }\n    },\n    usagePure(meta, utils, path) {\n      if (isRegenerator(meta)) {\n        let pureName = \"regenerator-runtime\";\n        if (useBabelRuntime) {\n          const runtimeName =\n            moduleName ??\n            ((path.hub as any).file as PluginPass).get(\n              \"runtimeHelpersModuleName\",\n            ) ??\n            \"@babel/runtime\";\n          pureName = `${runtimeName}/regenerator`;\n        }\n\n        path.replaceWith(\n          utils.injectDefaultImport(pureName, \"regenerator-runtime\"),\n        );\n      }\n    },\n  };\n});\n\nconst isRegenerator = meta =>\n  meta.kind === \"global\" && meta.name === \"regeneratorRuntime\";\n\nfunction shallowEqual(obj1: any, obj2: any) {\n  return JSON.stringify(obj1) === JSON.stringify(obj2);\n}\n"], "names": ["runtimeCompat", "define<PERSON>rovider", "debug", "targets", "babel", "options", "shallowEqual", "Error", "moduleName", "useBabelRuntime", "name", "polyfills", "usageGlobal", "meta", "utils", "isRegenerator", "injectGlobalImport", "usagePure", "path", "pureName", "_ref", "runtimeName", "hub", "file", "get", "replaceWith", "injectDefaultImport", "kind", "obj1", "obj2", "JSON", "stringify"], "mappings": ";;AAGA,MAAMA,aAAa,GAAG,8CAA8C,CAAA;AASpE,YAAeC,cAAc,CAAU,CAAC;EAAEC,KAAK;EAAEC,OAAO;AAAEC,EAAAA,KAAAA;AAAM,CAAC,EAAEC,OAAO,KAAK;EAC7E,IAAI,CAACC,YAAY,CAACH,OAAO,EAAEC,KAAK,CAACD,OAAO,EAAE,CAAC,EAAE;IAC3C,MAAM,IAAII,KAAK,CACb,wEAAwE,GACtE,sEAAsE,GACtE,kEAAkE,GAClE,WACJ,CAAC,CAAA;AACH,GAAA;EAEA,MAAM;AACJ,IAAA,CAACP,aAAa,GAAG;AAAEQ,MAAAA,UAAU,GAAG,IAAI;AAAEC,MAAAA,eAAe,GAAG,KAAA;AAAM,KAAC,GAAG,EAAC;AACrE,GAAC,GAAGJ,OAAO,CAAA;EAEX,OAAO;AACLK,IAAAA,IAAI,EAAE,aAAa;IAEnBC,SAAS,EAAE,CAAC,qBAAqB,CAAC;AAElCC,IAAAA,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAa;AAClC,MAAA,IAAIC,aAAa,CAACF,IAAI,CAAC,EAAE;QACvBX,KAAK,CAAC,qBAAqB,CAAC,CAAA;AAC5BY,QAAAA,KAAK,CAACE,kBAAkB,CAAC,gCAAgC,CAAC,CAAA;AAC5D,OAAA;KACD;AACDC,IAAAA,SAASA,CAACJ,IAAI,EAAEC,KAAK,EAAEI,IAAI,EAAE;AAC3B,MAAA,IAAIH,aAAa,CAACF,IAAI,CAAC,EAAE;QACvB,IAAIM,QAAQ,GAAG,qBAAqB,CAAA;AACpC,QAAA,IAAIV,eAAe,EAAE;AAAA,UAAA,IAAAW,IAAA,CAAA;UACnB,MAAMC,WAAW,IAAAD,IAAA,GACfZ,UAAU,IAAVA,IAAAA,GAAAA,UAAU,GACRU,IAAI,CAACI,GAAG,CAASC,IAAI,CAAgBC,GAAG,CACxC,0BACF,CAAC,KAAA,IAAA,GAAAJ,IAAA,GACD,gBAAgB,CAAA;UAClBD,QAAQ,GAAG,CAAGE,EAAAA,WAAW,CAAc,YAAA,CAAA,CAAA;AACzC,SAAA;QAEAH,IAAI,CAACO,WAAW,CACdX,KAAK,CAACY,mBAAmB,CAACP,QAAQ,EAAE,qBAAqB,CAC3D,CAAC,CAAA;AACH,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC,CAAA;AAEF,MAAMJ,aAAa,GAAGF,IAAI,IACxBA,IAAI,CAACc,IAAI,KAAK,QAAQ,IAAId,IAAI,CAACH,IAAI,KAAK,oBAAoB,CAAA;AAE9D,SAASJ,YAAYA,CAACsB,IAAS,EAAEC,IAAS,EAAE;AAC1C,EAAA,OAAOC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,KAAKE,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAA;AACtD;;;;"}
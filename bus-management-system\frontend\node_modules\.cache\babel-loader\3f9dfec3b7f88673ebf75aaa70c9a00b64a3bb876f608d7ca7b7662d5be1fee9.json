{"ast": null, "code": "import React from 'react';\nvar defaultContext = /*#__PURE__*/React.createContext(undefined);\nvar QueryClientSharingContext = /*#__PURE__*/React.createContext(false); // if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\n\nfunction getQueryClientContext(contextSharing) {\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext;\n    }\n    return window.ReactQueryClientContext;\n  }\n  return defaultContext;\n}\nexport var useQueryClient = function useQueryClient() {\n  var queryClient = React.useContext(getQueryClientContext(React.useContext(QueryClientSharingContext)));\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one');\n  }\n  return queryClient;\n};\nexport var QueryClientProvider = function QueryClientProvider(_ref) {\n  var client = _ref.client,\n    _ref$contextSharing = _ref.contextSharing,\n    contextSharing = _ref$contextSharing === void 0 ? false : _ref$contextSharing,\n    children = _ref.children;\n  React.useEffect(function () {\n    client.mount();\n    return function () {\n      client.unmount();\n    };\n  }, [client]);\n  var Context = getQueryClientContext(contextSharing);\n  return /*#__PURE__*/React.createElement(QueryClientSharingContext.Provider, {\n    value: contextSharing\n  }, /*#__PURE__*/React.createElement(Context.Provider, {\n    value: client\n  }, children));\n};", "map": {"version": 3, "names": ["React", "defaultContext", "createContext", "undefined", "QueryClientSharingContext", "getQueryClientContext", "contextSharing", "window", "ReactQueryClientContext", "useQueryClient", "queryClient", "useContext", "Error", "QueryClientProvider", "_ref", "client", "_ref$contextSharing", "children", "useEffect", "mount", "unmount", "Context", "createElement", "Provider", "value"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/react/QueryClientProvider.js"], "sourcesContent": ["import React from 'react';\nvar defaultContext = /*#__PURE__*/React.createContext(undefined);\nvar QueryClientSharingContext = /*#__PURE__*/React.createContext(false); // if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\n\nfunction getQueryClientContext(contextSharing) {\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext;\n    }\n\n    return window.ReactQueryClientContext;\n  }\n\n  return defaultContext;\n}\n\nexport var useQueryClient = function useQueryClient() {\n  var queryClient = React.useContext(getQueryClientContext(React.useContext(QueryClientSharingContext)));\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one');\n  }\n\n  return queryClient;\n};\nexport var QueryClientProvider = function QueryClientProvider(_ref) {\n  var client = _ref.client,\n      _ref$contextSharing = _ref.contextSharing,\n      contextSharing = _ref$contextSharing === void 0 ? false : _ref$contextSharing,\n      children = _ref.children;\n  React.useEffect(function () {\n    client.mount();\n    return function () {\n      client.unmount();\n    };\n  }, [client]);\n  var Context = getQueryClientContext(contextSharing);\n  return /*#__PURE__*/React.createElement(QueryClientSharingContext.Provider, {\n    value: contextSharing\n  }, /*#__PURE__*/React.createElement(Context.Provider, {\n    value: client\n  }, children));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,IAAIC,cAAc,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAChE,IAAIC,yBAAyB,GAAG,aAAaJ,KAAK,CAACE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AACzE;AACA;AACA;AACA;AACA;;AAEA,SAASG,qBAAqBA,CAACC,cAAc,EAAE;EAC7C,IAAIA,cAAc,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACnD,IAAI,CAACA,MAAM,CAACC,uBAAuB,EAAE;MACnCD,MAAM,CAACC,uBAAuB,GAAGP,cAAc;IACjD;IAEA,OAAOM,MAAM,CAACC,uBAAuB;EACvC;EAEA,OAAOP,cAAc;AACvB;AAEA,OAAO,IAAIQ,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;EACpD,IAAIC,WAAW,GAAGV,KAAK,CAACW,UAAU,CAACN,qBAAqB,CAACL,KAAK,CAACW,UAAU,CAACP,yBAAyB,CAAC,CAAC,CAAC;EAEtG,IAAI,CAACM,WAAW,EAAE;IAChB,MAAM,IAAIE,KAAK,CAAC,wDAAwD,CAAC;EAC3E;EAEA,OAAOF,WAAW;AACpB,CAAC;AACD,OAAO,IAAIG,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,IAAI,EAAE;EAClE,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACpBC,mBAAmB,GAAGF,IAAI,CAACR,cAAc;IACzCA,cAAc,GAAGU,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,mBAAmB;IAC7EC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5BjB,KAAK,CAACkB,SAAS,CAAC,YAAY;IAC1BH,MAAM,CAACI,KAAK,CAAC,CAAC;IACd,OAAO,YAAY;MACjBJ,MAAM,CAACK,OAAO,CAAC,CAAC;IAClB,CAAC;EACH,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;EACZ,IAAIM,OAAO,GAAGhB,qBAAqB,CAACC,cAAc,CAAC;EACnD,OAAO,aAAaN,KAAK,CAACsB,aAAa,CAAClB,yBAAyB,CAACmB,QAAQ,EAAE;IAC1EC,KAAK,EAAElB;EACT,CAAC,EAAE,aAAaN,KAAK,CAACsB,aAAa,CAACD,OAAO,CAACE,QAAQ,EAAE;IACpDC,KAAK,EAAET;EACT,CAAC,EAAEE,QAAQ,CAAC,CAAC;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
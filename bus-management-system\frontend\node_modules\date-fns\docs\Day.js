/**
 * @category Types
 * @summary Day type
 *
 * @description
 * The day of the week type alias (`0 | 1 | 2 | 3 | 4 | 5 | 6`). Unlike the date
 * (the number of days since the beginningof the month), which starts with 1
 * and is dynamic (can go up to 28, 30, or 31), the day starts with 0 and static
 * (always ends at 6). Look at it as an index in an array where Sunday is
 * the first element, and Saturday is the last.
 *
 * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6} Day
 */
const Day = 0
module.exports = Day

from flask import Blueprint, request
from flask_jwt_extended import jwt_required
from app import db
from app.models.course import Course
from app.models.department import Department
from app.models.faculty import Faculty
from app.models.enrollment import Enrollment
from app.utils.decorators import admin_required, faculty_required
from app.utils.helpers import success_response, error_response, paginate_query

courses_bp = Blueprint('courses', __name__)

@courses_bp.route('', methods=['GET'])
@jwt_required()
def get_courses():
    """Get all courses with pagination and filtering."""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        department_id = request.args.get('department_id', type=int)
        year = request.args.get('year', type=int)
        semester = request.args.get('semester', type=int)
        search = request.args.get('search', '').strip()
        
        query = Course.query.filter_by(is_active=True)
        
        if department_id:
            query = query.filter_by(department_id=department_id)
        
        if year:
            query = query.filter_by(year=year)
        
        if semester:
            query = query.filter_by(semester=semester)
        
        if search:
            query = query.filter(
                (Course.name.contains(search)) |
                (Course.code.contains(search))
            )
        
        query = query.order_by(Course.code)
        paginated = paginate_query(query, page, per_page)
        
        if not paginated:
            return error_response('Invalid pagination parameters')
        
        courses_data = [course.to_dict() for course in paginated['items']]
        
        return success_response('Courses retrieved successfully', {
            'courses': courses_data,
            'pagination': {
                'total': paginated['total'],
                'pages': paginated['pages'],
                'current_page': paginated['current_page'],
                'per_page': paginated['per_page'],
                'has_next': paginated['has_next'],
                'has_prev': paginated['has_prev']
            }
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve courses: {str(e)}', 500)

@courses_bp.route('/<int:course_id>', methods=['GET'])
@jwt_required()
def get_course(course_id):
    """Get a specific course by ID."""
    try:
        course = Course.query.filter_by(id=course_id, is_active=True).first()
        
        if not course:
            return error_response('Course not found', 404)
        
        return success_response('Course retrieved successfully', {
            'course': course.to_dict()
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve course: {str(e)}', 500)

@courses_bp.route('', methods=['POST'])
@admin_required
def create_course(current_user):
    """Create a new course."""
    try:
        data = request.get_json()
        
        if not data:
            return error_response('No data provided')
        
        required_fields = ['code', 'name', 'department_id', 'year', 'semester']
        for field in required_fields:
            if not data.get(field):
                return error_response(f'{field.replace("_", " ").title()} is required')
        
        # Check if course code already exists
        if Course.query.filter_by(code=data.get('code')).first():
            return error_response('Course code already exists')
        
        # Validate department
        department = Department.query.get(data.get('department_id'))
        if not department:
            return error_response('Department not found')
        
        # Validate faculty if provided
        faculty_id = data.get('faculty_id')
        if faculty_id:
            faculty = Faculty.query.get(faculty_id)
            if not faculty:
                return error_response('Faculty not found')
        
        course = Course(
            code=data.get('code').upper(),
            name=data.get('name'),
            description=data.get('description'),
            credits=data.get('credits', 3),
            department_id=data.get('department_id'),
            faculty_id=faculty_id,
            semester=data.get('semester'),
            year=data.get('year'),
            course_type=data.get('course_type', 'Core'),
            max_students=data.get('max_students', 60),
            syllabus=data.get('syllabus')
        )
        
        db.session.add(course)
        db.session.commit()
        
        return success_response('Course created successfully', {
            'course': course.to_dict()
        }, 201)
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to create course: {str(e)}', 500)

@courses_bp.route('/<int:course_id>', methods=['PUT'])
@admin_required
def update_course(current_user, course_id):
    """Update a course."""
    try:
        course = Course.query.filter_by(id=course_id, is_active=True).first()
        
        if not course:
            return error_response('Course not found', 404)
        
        data = request.get_json()
        if not data:
            return error_response('No data provided')
        
        # Update fields if provided
        updateable_fields = [
            'name', 'description', 'credits', 'faculty_id', 'semester',
            'year', 'course_type', 'max_students', 'syllabus'
        ]
        
        for field in updateable_fields:
            if field in data:
                if field == 'faculty_id' and data[field]:
                    faculty = Faculty.query.get(data[field])
                    if not faculty:
                        return error_response('Faculty not found')
                setattr(course, field, data[field])
        
        # Check course code uniqueness if being updated
        if 'code' in data:
            existing_course = Course.query.filter(
                Course.code == data['code'], Course.id != course_id
            ).first()
            if existing_course:
                return error_response('Course code already exists')
            course.code = data['code'].upper()
        
        db.session.commit()
        
        return success_response('Course updated successfully', {
            'course': course.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to update course: {str(e)}', 500)

@courses_bp.route('/<int:course_id>', methods=['DELETE'])
@admin_required
def delete_course(current_user, course_id):
    """Delete (deactivate) a course."""
    try:
        course = Course.query.filter_by(id=course_id, is_active=True).first()
        
        if not course:
            return error_response('Course not found', 404)
        
        course.is_active = False
        db.session.commit()
        
        return success_response('Course deleted successfully')
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to delete course: {str(e)}', 500)

@courses_bp.route('/<int:course_id>/students', methods=['GET'])
@faculty_required
def get_course_students(current_user, course_id):
    """Get students enrolled in a course."""
    try:
        course = Course.query.filter_by(id=course_id, is_active=True).first()
        
        if not course:
            return error_response('Course not found', 404)
        
        enrollments = Enrollment.query.filter_by(
            course_id=course_id, is_active=True
        ).all()
        
        students_data = []
        for enrollment in enrollments:
            student_data = enrollment.student.to_dict()
            student_data['enrollment_date'] = enrollment.enrollment_date.isoformat()
            students_data.append(student_data)
        
        return success_response('Course students retrieved successfully', {
            'course': course.to_dict(),
            'students': students_data,
            'total_students': len(students_data)
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve course students: {str(e)}', 500)

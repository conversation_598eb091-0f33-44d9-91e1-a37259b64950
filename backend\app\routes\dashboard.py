from flask import Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.user import User
from app.models.student import Student
from app.models.faculty import Faculty
from app.models.course import Course
from app.models.department import Department
from app.models.notice import Notice
from app.models.fee import Fee
from app.utils.helpers import success_response, error_response

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/stats', methods=['GET'])
@jwt_required()
def get_dashboard_stats():
    """Get dashboard statistics based on user role."""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response('User not found', 404)
        
        stats = {}
        
        if user.role == 'admin':
            # Admin dashboard stats
            stats = {
                'total_students': Student.query.filter_by(is_active=True).count(),
                'total_faculty': Faculty.query.filter_by(is_active=True).count(),
                'total_courses': Course.query.filter_by(is_active=True).count(),
                'total_departments': Department.query.filter_by(is_active=True).count(),
                'active_notices': Notice.query.filter_by(is_active=True).count(),
                'pending_fees': Fee.query.filter_by(status='Pending').count(),
                'overdue_fees': Fee.query.filter_by(status='Overdue').count()
            }
            
            # Recent activities
            recent_students = Student.query.filter_by(is_active=True).order_by(
                Student.created_at.desc()
            ).limit(5).all()
            
            recent_notices = Notice.query.filter_by(is_active=True).order_by(
                Notice.created_at.desc()
            ).limit(5).all()
            
            stats['recent_students'] = [student.to_dict() for student in recent_students]
            stats['recent_notices'] = [notice.to_dict() for notice in recent_notices]
            
        elif user.role == 'faculty':
            # Faculty dashboard stats
            faculty = Faculty.query.filter_by(user_id=user.id).first()
            if faculty:
                assigned_courses = Course.query.filter_by(
                    faculty_id=faculty.id, is_active=True
                ).all()
                
                total_students = 0
                for course in assigned_courses:
                    total_students += course.get_enrollment_count()
                
                stats = {
                    'assigned_courses': len(assigned_courses),
                    'total_students': total_students,
                    'department': faculty.department.name if faculty.department else None,
                    'courses': [course.to_dict() for course in assigned_courses]
                }
                
                # Recent notices for faculty department
                if faculty.department:
                    recent_notices = Notice.query.filter(
                        (Notice.department_id == faculty.department_id) |
                        (Notice.target_audience.in_(['All', 'Faculty']))
                    ).filter_by(is_active=True).order_by(
                        Notice.created_at.desc()
                    ).limit(5).all()
                    
                    stats['recent_notices'] = [notice.to_dict() for notice in recent_notices]
            
        elif user.role == 'student':
            # Student dashboard stats
            student = Student.query.filter_by(user_id=user.id).first()
            if student:
                from app.models.enrollment import Enrollment
                from app.models.attendance import Attendance
                from app.models.grade import Grade
                
                enrollments = Enrollment.query.filter_by(
                    student_id=student.id, is_active=True
                ).all()
                
                enrolled_courses = [enrollment.course for enrollment in enrollments]
                
                # Calculate attendance percentage
                total_classes = Attendance.query.filter_by(student_id=student.id).count()
                present_classes = Attendance.query.filter_by(
                    student_id=student.id, status='Present'
                ).count()
                
                attendance_percentage = 0
                if total_classes > 0:
                    attendance_percentage = round((present_classes / total_classes) * 100, 2)
                
                # Get CGPA
                cgpa = Grade.calculate_cgpa(student.id)
                
                # Get fee summary
                fee_summary = Fee.get_student_fee_summary(student.id)
                
                stats = {
                    'enrolled_courses': len(enrolled_courses),
                    'attendance_percentage': attendance_percentage,
                    'cgpa': cgpa,
                    'department': student.department.name if student.department else None,
                    'year': student.year,
                    'semester': student.semester,
                    'courses': [course.to_dict() for course in enrolled_courses],
                    'fee_summary': fee_summary
                }
                
                # Recent notices for student department
                if student.department:
                    recent_notices = Notice.query.filter(
                        (Notice.department_id == student.department_id) |
                        (Notice.target_audience.in_(['All', 'Students']))
                    ).filter_by(is_active=True).order_by(
                        Notice.created_at.desc()
                    ).limit(5).all()
                    
                    stats['recent_notices'] = [notice.to_dict() for notice in recent_notices]
        
        return success_response('Dashboard stats retrieved successfully', {
            'user_role': user.role,
            'stats': stats
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve dashboard stats: {str(e)}', 500)

@dashboard_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_user_profile():
    """Get current user's profile information."""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return error_response('User not found', 404)
        
        profile_data = user.to_dict()
        
        if user.role == 'student' and user.student:
            profile_data['student_details'] = user.student.to_dict()
        elif user.role == 'faculty' and user.faculty:
            profile_data['faculty_details'] = user.faculty.to_dict()
        
        return success_response('Profile retrieved successfully', {
            'profile': profile_data
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve profile: {str(e)}', 500)

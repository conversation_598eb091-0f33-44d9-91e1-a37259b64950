{"name": "detect-node", "version": "2.1.0", "description": "Detect Node.JS (as opposite to browser environment) (reliable)", "main": "index.js", "module": "index.esm.js", "browser": "browser.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/iliakan/detect-node"}, "keywords": ["detect", "node"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/iliakan/detect-node/issues"}, "homepage": "https://github.com/iliakan/detect-node"}
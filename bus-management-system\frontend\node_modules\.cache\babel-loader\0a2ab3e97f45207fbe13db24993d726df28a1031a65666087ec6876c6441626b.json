{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst AlignHorizontalSpaceAround = createLucideIcon(\"AlignHorizontalSpaceAround\", [[\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"9\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"yn7j0q\"\n}], [\"path\", {\n  d: \"M4 22V2\",\n  key: \"tsjzd3\"\n}], [\"path\", {\n  d: \"M20 22V2\",\n  key: \"1bnhr8\"\n}]]);\nexport { AlignHorizontalSpaceAround as default };", "map": {"version": 3, "names": ["AlignHorizontalSpaceAround", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\align-horizontal-space-around.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignHorizontalSpaceAround\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSIxMCIgeD0iOSIgeT0iNyIgcng9IjIiIC8+CiAgPHBhdGggZD0iTTQgMjJWMiIgLz4KICA8cGF0aCBkPSJNMjAgMjJWMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/align-horizontal-space-around\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignHorizontalSpaceAround = createLucideIcon(\n  'AlignHorizontalSpaceAround',\n  [\n    [\n      'rect',\n      { width: '6', height: '10', x: '9', y: '7', rx: '2', key: 'yn7j0q' },\n    ],\n    ['path', { d: 'M4 22V2', key: 'tsjzd3' }],\n    ['path', { d: 'M20 22V2', key: '1bnhr8' }],\n  ],\n);\n\nexport default AlignHorizontalSpaceAround;\n"], "mappings": ";;;;;AAaA,MAAMA,0BAA6B,GAAAC,gBAAA,CACjC,8BACA,CACE,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAE7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
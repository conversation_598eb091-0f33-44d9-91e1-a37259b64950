from datetime import datetime
from app import db, ma

class Notice(db.Model):
    """Notice board model."""
    __tablename__ = 'notices'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    author_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)
    target_audience = db.Column(db.Enum('All', 'Students', 'Faculty', 'Department', 
                                       name='notice_audience'), default='All')
    priority = db.Column(db.Enum('Low', 'Medium', 'High', 'Urgent', name='notice_priority'), 
                        default='Medium')
    is_active = db.Column(db.<PERSON>, default=True)
    publish_date = db.Column(db.DateTime, default=datetime.utcnow)
    expiry_date = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    author = db.relationship('User', backref='notices')
    
    @property
    def is_expired(self):
        """Check if notice is expired."""
        if self.expiry_date:
            return datetime.utcnow() > self.expiry_date
        return False
    
    @property
    def days_remaining(self):
        """Get days remaining until expiry."""
        if self.expiry_date:
            delta = self.expiry_date - datetime.utcnow()
            return delta.days if delta.days > 0 else 0
        return None
    
    def to_dict(self):
        """Convert notice to dictionary."""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'author_id': self.author_id,
            'author_name': self.author.username if self.author else None,
            'department_id': self.department_id,
            'department_name': self.department.name if self.department else None,
            'target_audience': self.target_audience,
            'priority': self.priority,
            'is_active': self.is_active,
            'is_expired': self.is_expired,
            'days_remaining': self.days_remaining,
            'publish_date': self.publish_date.isoformat() if self.publish_date else None,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Notice {self.title}>'

class NoticeSchema(ma.SQLAlchemyAutoSchema):
    """Notice serialization schema."""
    class Meta:
        model = Notice
        load_instance = True
        include_fk = True

notice_schema = NoticeSchema()
notices_schema = NoticeSchema(many=True)

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).ReactQueryCreateAsyncStoragePersistorExperimental={})}(this,(function(e){"use strict";function t(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function n(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}function r(){}function i(e){var t=e();if(t&&t.then)return t.then(r)}function o(e,r){var o=void 0===r?{}:r,u=o.interval,f=void 0===u?1e3:u,c=o.limit,s=void 0===c?1:c;if("function"!=typeof e)throw new Error("argument is not function.");var a,l={current:!1},v=0,d=[];return function(){for(var r=arguments.length,o=new Array(r),u=0;u<r;u++)o[u]=arguments[u];return n((function(){return l.current&&(v=Date.now(),d.length>s&&d.shift(),d.push(o),clearTimeout(a)),i((function(){if(Date.now()-v>f)return l.current=!0,t(e.apply(void 0,o),(function(){v=Date.now(),l.current=!1}));if(d.length>0){var r=d[d.length-1];a=setTimeout(n((function(){return i((function(){if(!l.current)return l.current=!0,t(e.apply(void 0,r),(function(){l.current=!1}))}))})),f)}}))}))()}}e.createAsyncStoragePersistor=function(e){var r=e.storage,i=e.key,u=void 0===i?"REACT_QUERY_OFFLINE_CACHE":i,f=e.throttleTime,c=void 0===f?1e3:f,s=e.serialize,a=void 0===s?JSON.stringify:s,l=e.deserialize,v=void 0===l?JSON.parse:l;return{persistClient:o((function(e){return r.setItem(u,a(e))}),{interval:c}),restoreClient:n((function(){return t(r.getItem(u),(function(e){if(e)return v(e)}))})),removeClient:function(){return r.removeItem(u)}}},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=createAsyncStoragePersistor-experimental.production.min.js.map

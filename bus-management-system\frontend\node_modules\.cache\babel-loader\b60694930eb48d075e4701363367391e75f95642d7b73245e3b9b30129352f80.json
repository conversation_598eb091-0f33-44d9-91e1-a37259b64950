{"ast": null, "code": "import React from 'react'; // CONTEXT\n\nfunction createValue() {\n  var _isReset = false;\n  return {\n    clearReset: function clearReset() {\n      _isReset = false;\n    },\n    reset: function reset() {\n      _isReset = true;\n    },\n    isReset: function isReset() {\n      return _isReset;\n    }\n  };\n}\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/React.createContext(createValue()); // HOOK\n\nexport var useQueryErrorResetBoundary = function useQueryErrorResetBoundary() {\n  return React.useContext(QueryErrorResetBoundaryContext);\n}; // COMPONENT\n\nexport var QueryErrorResetBoundary = function QueryErrorResetBoundary(_ref) {\n  var children = _ref.children;\n  var value = React.useMemo(function () {\n    return createValue();\n  }, []);\n  return /*#__PURE__*/React.createElement(QueryErrorResetBoundaryContext.Provider, {\n    value: value\n  }, typeof children === 'function' ? children(value) : children);\n};", "map": {"version": 3, "names": ["React", "createValue", "_isReset", "clear<PERSON><PERSON>t", "reset", "isReset", "QueryErrorResetBoundaryContext", "createContext", "useQueryErrorResetBoundary", "useContext", "QueryErrorResetBoundary", "_ref", "children", "value", "useMemo", "createElement", "Provider"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/react/QueryErrorResetBoundary.js"], "sourcesContent": ["import React from 'react'; // CONTEXT\n\nfunction createValue() {\n  var _isReset = false;\n  return {\n    clearReset: function clearReset() {\n      _isReset = false;\n    },\n    reset: function reset() {\n      _isReset = true;\n    },\n    isReset: function isReset() {\n      return _isReset;\n    }\n  };\n}\n\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/React.createContext(createValue()); // HOOK\n\nexport var useQueryErrorResetBoundary = function useQueryErrorResetBoundary() {\n  return React.useContext(QueryErrorResetBoundaryContext);\n}; // COMPONENT\n\nexport var QueryErrorResetBoundary = function QueryErrorResetBoundary(_ref) {\n  var children = _ref.children;\n  var value = React.useMemo(function () {\n    return createValue();\n  }, []);\n  return /*#__PURE__*/React.createElement(QueryErrorResetBoundaryContext.Provider, {\n    value: value\n  }, typeof children === 'function' ? children(value) : children);\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO,CAAC,CAAC;;AAE3B,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAIC,QAAQ,GAAG,KAAK;EACpB,OAAO;IACLC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChCD,QAAQ,GAAG,KAAK;IAClB,CAAC;IACDE,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBF,QAAQ,GAAG,IAAI;IACjB,CAAC;IACDG,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1B,OAAOH,QAAQ;IACjB;EACF,CAAC;AACH;AAEA,IAAII,8BAA8B,GAAG,aAAaN,KAAK,CAACO,aAAa,CAACN,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtF,OAAO,IAAIO,0BAA0B,GAAG,SAASA,0BAA0BA,CAAA,EAAG;EAC5E,OAAOR,KAAK,CAACS,UAAU,CAACH,8BAA8B,CAAC;AACzD,CAAC,CAAC,CAAC;;AAEH,OAAO,IAAII,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,IAAI,EAAE;EAC1E,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,IAAIC,KAAK,GAAGb,KAAK,CAACc,OAAO,CAAC,YAAY;IACpC,OAAOb,WAAW,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAaD,KAAK,CAACe,aAAa,CAACT,8BAA8B,CAACU,QAAQ,EAAE;IAC/EH,KAAK,EAAEA;EACT,CAAC,EAAE,OAAOD,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACC,KAAK,CAAC,GAAGD,QAAQ,CAAC;AACjE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
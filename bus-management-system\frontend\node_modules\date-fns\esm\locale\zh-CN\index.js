import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Chinese Simplified locale.
 * @language Chinese Simplified
 * @iso-639-2 zho
 * <AUTHOR> [@KingMario]{@link https://github.com/KingMario}
 * <AUTHOR> [@fnlctrl]{@link https://github.com/fnlctrl}
 * <AUTHOR> [@sabrinamiao]{@link https://github.com/sabrinamiao}
 * <AUTHOR> [@cubicwork]{@link https://github.com/cubicwork}
 * <AUTHOR> [@skyuplam]{@link https://github.com/skyuplam}
 */
var locale = {
  code: 'zh-C<PERSON>',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4
  }
};
export default locale;
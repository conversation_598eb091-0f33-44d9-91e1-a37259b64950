{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\components\\\\LoadingSpinner.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'medium',\n  text = 'Loading...',\n  fullScreen = true\n}) => {\n  const sizeClasses = {\n    small: 'h-4 w-4',\n    medium: 'h-8 w-8',\n    large: 'h-12 w-12'\n  };\n  const content = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `loading-spinner ${sizeClasses[size]}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), text && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mt-2 text-sm text-gray-600\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n  if (fullScreen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n      children: content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this);\n  }\n  return content;\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "text", "fullScreen", "sizeClasses", "small", "medium", "large", "content", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/components/LoadingSpinner.js"], "sourcesContent": ["import React from 'react';\n\nconst LoadingSpinner = ({ size = 'medium', text = 'Loading...', fullScreen = true }) => {\n  const sizeClasses = {\n    small: 'h-4 w-4',\n    medium: 'h-8 w-8',\n    large: 'h-12 w-12',\n  };\n\n  const content = (\n    <div className=\"text-center\">\n      <div className=\"flex justify-center\">\n        <div className={`loading-spinner ${sizeClasses[size]}`}></div>\n      </div>\n      {text && (\n        <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n      )}\n    </div>\n  );\n\n  if (fullScreen) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        {content}\n      </div>\n    );\n  }\n\n  return content;\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI,GAAG,QAAQ;EAAEC,IAAI,GAAG,YAAY;EAAEC,UAAU,GAAG;AAAK,CAAC,KAAK;EACtF,MAAMC,WAAW,GAAG;IAClBC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,OAAO,gBACXT,OAAA;IAAKU,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BX,OAAA;MAAKU,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCX,OAAA;QAAKU,SAAS,EAAE,mBAAmBL,WAAW,CAACH,IAAI,CAAC;MAAG;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,EACLZ,IAAI,iBACHH,OAAA;MAAGU,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAER;IAAI;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CACpD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,IAAIX,UAAU,EAAE;IACd,oBACEJ,OAAA;MAAKU,SAAS,EAAC,0DAA0D;MAAAC,QAAA,EACtEF;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,OAAON,OAAO;AAChB,CAAC;AAACO,EAAA,GA3BIf,cAAc;AA6BpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
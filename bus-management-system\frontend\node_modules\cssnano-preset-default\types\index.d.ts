export = defaultPreset;
/**
 * @param {Options} opts
 * @return {{plugins: [import('postcss').PluginCreator<any>, boolean | Record<string, any> | undefined][]}}
 */
declare function defaultPreset(opts?: Options): {
    plugins: [import('postcss').PluginCreator<any>, boolean | Record<string, any> | undefined][];
};
declare namespace defaultPreset {
    export { Options };
}
type Options = {
    discardComments?: false | import('postcss-discard-comments').Options & {
        exclude?: true;
    };
    reduceInitial?: false | {
        exclude?: true;
    };
    minifyGradients?: false | {
        exclude?: true;
    };
    svgo?: false | import('postcss-svgo').Options & {
        exclude?: true;
    };
    reduceTransforms?: false | {
        exclude?: true;
    };
    convertValues?: false | import('postcss-convert-values').Options & {
        exclude?: true;
    };
    calc?: false | import('postcss-calc').PostCssCalcOptions & {
        exclude?: true;
    };
    colormin?: false | (Record<string, any> & {
        exclude?: true;
    });
    orderedValues?: false | {
        exclude?: true;
    };
    minifySelectors?: false | {
        exclude?: true;
    };
    minifyParams?: false | {
        exclude?: true;
    };
    normalizeCharset?: false | import('postcss-normalize-charset').Options & {
        exclude?: true;
    };
    minifyFontValues?: false | import('postcss-minify-font-values').Options & {
        exclude?: true;
    };
    normalizeUrl?: false | import('postcss-normalize-url').Options & {
        exclude?: true;
    };
    mergeLonghand?: false | {
        exclude?: true;
    };
    discardDuplicates?: false | {
        exclude?: true;
    };
    discardOverridden?: false | {
        exclude?: true;
    };
    normalizeRepeatStyle?: false | {
        exclude?: true;
    };
    mergeRules?: false | {
        exclude?: true;
    };
    discardEmpty?: false | {
        exclude?: true;
    };
    uniqueSelectors?: false | {
        exclude?: true;
    };
    normalizeString?: false | import('postcss-normalize-string').Options & {
        exclude?: true;
    };
    normalizePositions?: false | {
        exclude?: true;
    };
    normalizeWhitespace?: false | {
        exclude?: true;
    };
    normalizeUnicode?: false | {
        exclude?: true;
    };
    normalizeDisplayValues?: false | {
        exclude?: true;
    };
    normalizeTimingFunctions?: false | {
        exclude?: true;
    };
    rawCache?: false | {
        exclude?: true;
    };
};
import { rawCache } from "cssnano-utils";

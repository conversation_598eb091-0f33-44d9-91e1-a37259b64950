{"version": 3, "names": ["inferers", "require", "_t", "anyTypeAnnotation", "isAnyTypeAnnotation", "isArrayTypeAnnotation", "isBooleanTypeAnnotation", "isEmptyTypeAnnotation", "isFlowBaseAnnotation", "isGenericTypeAnnotation", "isIdentifier", "isMixedTypeAnnotation", "isNumberTypeAnnotation", "isStringTypeAnnotation", "isTSArrayType", "isTSTypeAnnotation", "isTSTypeReference", "isTupleTypeAnnotation", "isTypeAnnotation", "isUnionTypeAnnotation", "isVoidTypeAnnotation", "stringTypeAnnotation", "voidTypeAnnotation", "getTypeAnnotation", "type", "getData", "_getTypeAnnotation", "call", "typeAnnotation", "setData", "typeAnnotationInferringNodes", "WeakSet", "node", "key", "parentPath", "isVariableDeclarator", "declar", "declar<PERSON><PERSON>nt", "isForInStatement", "isForOfStatement", "has", "add", "_inferer", "inferer", "validParent", "delete", "isBaseType", "baseName", "soft", "_isBaseType", "Error", "couldBeBaseType", "name", "type2", "types", "baseTypeStrictlyMatches", "rightArg", "left", "right", "isGenericType", "genericName", "id", "typeName"], "sources": ["../../../src/path/inference/index.ts"], "sourcesContent": ["import type NodePath from \"../index.ts\";\nimport * as inferers from \"./inferers.ts\";\nimport {\n  anyTypeAnnotation,\n  isAnyTypeAnnotation,\n  isArrayTypeAnnotation,\n  isBooleanTypeAnnotation,\n  isEmptyTypeAnnotation,\n  isFlowBaseAnnotation,\n  isGenericTypeAnnotation,\n  isIdentifier,\n  isMixedTypeAnnotation,\n  isNumberTypeAnnotation,\n  isStringTypeAnnotation,\n  isTSArrayType,\n  isTSTypeAnnotation,\n  isTSTypeReference,\n  isTupleTypeAnnotation,\n  isTypeAnnotation,\n  isUnionTypeAnnotation,\n  isVoidTypeAnnotation,\n  stringTypeAnnotation,\n  voidTypeAnnotation,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\n/**\n * Infer the type of the current `NodePath`.\n */\n\nexport function getTypeAnnotation(this: NodePath): t.FlowType | t.TSType {\n  let type = this.getData(\"typeAnnotation\");\n  if (type != null) {\n    return type;\n  }\n  type = _getTypeAnnotation.call(this) || anyTypeAnnotation();\n  if (isTypeAnnotation(type) || isTSTypeAnnotation(type)) {\n    type = type.typeAnnotation;\n  }\n  this.setData(\"typeAnnotation\", type);\n  return type;\n}\n\n// Used to avoid infinite recursion in cases like\n//   var b, c; if (0) { c = 1; b = c; } c = b;\n// It also works with indirect recursion.\nconst typeAnnotationInferringNodes = new WeakSet();\n\n/**\n * todo: split up this method\n */\n\nexport function _getTypeAnnotation(this: NodePath): any {\n  const node = this.node;\n\n  if (!node) {\n    // handle initializerless variables, add in checks for loop initializers too\n    if (this.key === \"init\" && this.parentPath.isVariableDeclarator()) {\n      const declar = this.parentPath.parentPath;\n      const declarParent = declar.parentPath;\n\n      // for (let NODE in bar) {}\n      if (declar.key === \"left\" && declarParent.isForInStatement()) {\n        return stringTypeAnnotation();\n      }\n\n      // for (let NODE of bar) {}\n      if (declar.key === \"left\" && declarParent.isForOfStatement()) {\n        return anyTypeAnnotation();\n      }\n\n      return voidTypeAnnotation();\n    } else {\n      return;\n    }\n  }\n\n  // @ts-expect-error typeAnnotation may not index node\n  if (node.typeAnnotation) {\n    // @ts-expect-error typeAnnotation may not index node\n    return node.typeAnnotation;\n  }\n\n  if (typeAnnotationInferringNodes.has(node)) {\n    // Bail out from type inference to avoid infinite loops\n    return;\n  }\n  typeAnnotationInferringNodes.add(node);\n\n  try {\n    let inferer =\n      // @ts-expect-error inferers do not cover all AST types\n      inferers[node.type];\n    if (inferer) {\n      return inferer.call(this, node);\n    }\n\n    // @ts-expect-error inferers do not cover all AST types\n    inferer = inferers[this.parentPath.type];\n    if (inferer?.validParent) {\n      return this.parentPath.getTypeAnnotation();\n    }\n  } finally {\n    typeAnnotationInferringNodes.delete(node);\n  }\n}\n\nexport function isBaseType(\n  this: NodePath,\n  baseName: string,\n  soft?: boolean,\n): boolean {\n  return _isBaseType(baseName, this.getTypeAnnotation(), soft);\n}\n\nfunction _isBaseType(\n  baseName: string,\n  type?: t.FlowType | t.TSType,\n  soft?: boolean,\n): boolean {\n  if (baseName === \"string\") {\n    return isStringTypeAnnotation(type);\n  } else if (baseName === \"number\") {\n    return isNumberTypeAnnotation(type);\n  } else if (baseName === \"boolean\") {\n    return isBooleanTypeAnnotation(type);\n  } else if (baseName === \"any\") {\n    return isAnyTypeAnnotation(type);\n  } else if (baseName === \"mixed\") {\n    return isMixedTypeAnnotation(type);\n  } else if (baseName === \"empty\") {\n    return isEmptyTypeAnnotation(type);\n  } else if (baseName === \"void\") {\n    return isVoidTypeAnnotation(type);\n  } else {\n    if (soft) {\n      return false;\n    } else {\n      throw new Error(`Unknown base type ${baseName}`);\n    }\n  }\n}\n\nexport function couldBeBaseType(this: NodePath, name: string): boolean {\n  const type = this.getTypeAnnotation();\n  if (isAnyTypeAnnotation(type)) return true;\n\n  if (isUnionTypeAnnotation(type)) {\n    for (const type2 of type.types) {\n      if (isAnyTypeAnnotation(type2) || _isBaseType(name, type2, true)) {\n        return true;\n      }\n    }\n    return false;\n  } else {\n    return _isBaseType(name, type, true);\n  }\n}\n\nexport function baseTypeStrictlyMatches(\n  this: NodePath,\n  rightArg: NodePath,\n): boolean {\n  const left = this.getTypeAnnotation();\n  const right = rightArg.getTypeAnnotation();\n\n  if (!isAnyTypeAnnotation(left) && isFlowBaseAnnotation(left)) {\n    return right.type === left.type;\n  }\n  return false;\n}\n\nexport function isGenericType(this: NodePath, genericName: string): boolean {\n  const type = this.getTypeAnnotation();\n  if (genericName === \"Array\") {\n    // T[]\n    if (\n      isTSArrayType(type) ||\n      isArrayTypeAnnotation(type) ||\n      isTupleTypeAnnotation(type)\n    ) {\n      return true;\n    }\n  }\n  return (\n    (isGenericTypeAnnotation(type) &&\n      isIdentifier(type.id, {\n        name: genericName,\n      })) ||\n    (isTSTypeReference(type) &&\n      isIdentifier(type.typeName, {\n        name: genericName,\n      }))\n  );\n}\n"], "mappings": ";;;;;;;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAD,OAAA;AAqBsB;EApBpBE,iBAAiB;EACjBC,mBAAmB;EACnBC,qBAAqB;EACrBC,uBAAuB;EACvBC,qBAAqB;EACrBC,oBAAoB;EACpBC,uBAAuB;EACvBC,YAAY;EACZC,qBAAqB;EACrBC,sBAAsB;EACtBC,sBAAsB;EACtBC,aAAa;EACbC,kBAAkB;EAClBC,iBAAiB;EACjBC,qBAAqB;EACrBC,gBAAgB;EAChBC,qBAAqB;EACrBC,oBAAoB;EACpBC,oBAAoB;EACpBC;AAAkB,IAAApB,EAAA;AAQb,SAASqB,iBAAiBA,CAAA,EAAwC;EACvE,IAAIC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACzC,IAAID,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOA,IAAI;EACb;EACAA,IAAI,GAAGE,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC,IAAIxB,iBAAiB,CAAC,CAAC;EAC3D,IAAIe,gBAAgB,CAACM,IAAI,CAAC,IAAIT,kBAAkB,CAACS,IAAI,CAAC,EAAE;IACtDA,IAAI,GAAGA,IAAI,CAACI,cAAc;EAC5B;EACA,IAAI,CAACC,OAAO,CAAC,gBAAgB,EAAEL,IAAI,CAAC;EACpC,OAAOA,IAAI;AACb;AAKA,MAAMM,4BAA4B,GAAG,IAAIC,OAAO,CAAC,CAAC;AAM3C,SAASL,kBAAkBA,CAAA,EAAsB;EACtD,MAAMM,IAAI,GAAG,IAAI,CAACA,IAAI;EAEtB,IAAI,CAACA,IAAI,EAAE;IAET,IAAI,IAAI,CAACC,GAAG,KAAK,MAAM,IAAI,IAAI,CAACC,UAAU,CAACC,oBAAoB,CAAC,CAAC,EAAE;MACjE,MAAMC,MAAM,GAAG,IAAI,CAACF,UAAU,CAACA,UAAU;MACzC,MAAMG,YAAY,GAAGD,MAAM,CAACF,UAAU;MAGtC,IAAIE,MAAM,CAACH,GAAG,KAAK,MAAM,IAAII,YAAY,CAACC,gBAAgB,CAAC,CAAC,EAAE;QAC5D,OAAOjB,oBAAoB,CAAC,CAAC;MAC/B;MAGA,IAAIe,MAAM,CAACH,GAAG,KAAK,MAAM,IAAII,YAAY,CAACE,gBAAgB,CAAC,CAAC,EAAE;QAC5D,OAAOpC,iBAAiB,CAAC,CAAC;MAC5B;MAEA,OAAOmB,kBAAkB,CAAC,CAAC;IAC7B,CAAC,MAAM;MACL;IACF;EACF;EAGA,IAAIU,IAAI,CAACJ,cAAc,EAAE;IAEvB,OAAOI,IAAI,CAACJ,cAAc;EAC5B;EAEA,IAAIE,4BAA4B,CAACU,GAAG,CAACR,IAAI,CAAC,EAAE;IAE1C;EACF;EACAF,4BAA4B,CAACW,GAAG,CAACT,IAAI,CAAC;EAEtC,IAAI;IAAA,IAAAU,QAAA;IACF,IAAIC,OAAO,GAET3C,QAAQ,CAACgC,IAAI,CAACR,IAAI,CAAC;IACrB,IAAImB,OAAO,EAAE;MACX,OAAOA,OAAO,CAAChB,IAAI,CAAC,IAAI,EAAEK,IAAI,CAAC;IACjC;IAGAW,OAAO,GAAG3C,QAAQ,CAAC,IAAI,CAACkC,UAAU,CAACV,IAAI,CAAC;IACxC,KAAAkB,QAAA,GAAIC,OAAO,aAAPD,QAAA,CAASE,WAAW,EAAE;MACxB,OAAO,IAAI,CAACV,UAAU,CAACX,iBAAiB,CAAC,CAAC;IAC5C;EACF,CAAC,SAAS;IACRO,4BAA4B,CAACe,MAAM,CAACb,IAAI,CAAC;EAC3C;AACF;AAEO,SAASc,UAAUA,CAExBC,QAAgB,EAChBC,IAAc,EACL;EACT,OAAOC,WAAW,CAACF,QAAQ,EAAE,IAAI,CAACxB,iBAAiB,CAAC,CAAC,EAAEyB,IAAI,CAAC;AAC9D;AAEA,SAASC,WAAWA,CAClBF,QAAgB,EAChBvB,IAA4B,EAC5BwB,IAAc,EACL;EACT,IAAID,QAAQ,KAAK,QAAQ,EAAE;IACzB,OAAOlC,sBAAsB,CAACW,IAAI,CAAC;EACrC,CAAC,MAAM,IAAIuB,QAAQ,KAAK,QAAQ,EAAE;IAChC,OAAOnC,sBAAsB,CAACY,IAAI,CAAC;EACrC,CAAC,MAAM,IAAIuB,QAAQ,KAAK,SAAS,EAAE;IACjC,OAAOzC,uBAAuB,CAACkB,IAAI,CAAC;EACtC,CAAC,MAAM,IAAIuB,QAAQ,KAAK,KAAK,EAAE;IAC7B,OAAO3C,mBAAmB,CAACoB,IAAI,CAAC;EAClC,CAAC,MAAM,IAAIuB,QAAQ,KAAK,OAAO,EAAE;IAC/B,OAAOpC,qBAAqB,CAACa,IAAI,CAAC;EACpC,CAAC,MAAM,IAAIuB,QAAQ,KAAK,OAAO,EAAE;IAC/B,OAAOxC,qBAAqB,CAACiB,IAAI,CAAC;EACpC,CAAC,MAAM,IAAIuB,QAAQ,KAAK,MAAM,EAAE;IAC9B,OAAO3B,oBAAoB,CAACI,IAAI,CAAC;EACnC,CAAC,MAAM;IACL,IAAIwB,IAAI,EAAE;MACR,OAAO,KAAK;IACd,CAAC,MAAM;MACL,MAAM,IAAIE,KAAK,CAAC,qBAAqBH,QAAQ,EAAE,CAAC;IAClD;EACF;AACF;AAEO,SAASI,eAAeA,CAAiBC,IAAY,EAAW;EACrE,MAAM5B,IAAI,GAAG,IAAI,CAACD,iBAAiB,CAAC,CAAC;EACrC,IAAInB,mBAAmB,CAACoB,IAAI,CAAC,EAAE,OAAO,IAAI;EAE1C,IAAIL,qBAAqB,CAACK,IAAI,CAAC,EAAE;IAC/B,KAAK,MAAM6B,KAAK,IAAI7B,IAAI,CAAC8B,KAAK,EAAE;MAC9B,IAAIlD,mBAAmB,CAACiD,KAAK,CAAC,IAAIJ,WAAW,CAACG,IAAI,EAAEC,KAAK,EAAE,IAAI,CAAC,EAAE;QAChE,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC,MAAM;IACL,OAAOJ,WAAW,CAACG,IAAI,EAAE5B,IAAI,EAAE,IAAI,CAAC;EACtC;AACF;AAEO,SAAS+B,uBAAuBA,CAErCC,QAAkB,EACT;EACT,MAAMC,IAAI,GAAG,IAAI,CAAClC,iBAAiB,CAAC,CAAC;EACrC,MAAMmC,KAAK,GAAGF,QAAQ,CAACjC,iBAAiB,CAAC,CAAC;EAE1C,IAAI,CAACnB,mBAAmB,CAACqD,IAAI,CAAC,IAAIjD,oBAAoB,CAACiD,IAAI,CAAC,EAAE;IAC5D,OAAOC,KAAK,CAAClC,IAAI,KAAKiC,IAAI,CAACjC,IAAI;EACjC;EACA,OAAO,KAAK;AACd;AAEO,SAASmC,aAAaA,CAAiBC,WAAmB,EAAW;EAC1E,MAAMpC,IAAI,GAAG,IAAI,CAACD,iBAAiB,CAAC,CAAC;EACrC,IAAIqC,WAAW,KAAK,OAAO,EAAE;IAE3B,IACE9C,aAAa,CAACU,IAAI,CAAC,IACnBnB,qBAAqB,CAACmB,IAAI,CAAC,IAC3BP,qBAAqB,CAACO,IAAI,CAAC,EAC3B;MACA,OAAO,IAAI;IACb;EACF;EACA,OACGf,uBAAuB,CAACe,IAAI,CAAC,IAC5Bd,YAAY,CAACc,IAAI,CAACqC,EAAE,EAAE;IACpBT,IAAI,EAAEQ;EACR,CAAC,CAAC,IACH5C,iBAAiB,CAACQ,IAAI,CAAC,IACtBd,YAAY,CAACc,IAAI,CAACsC,QAAQ,EAAE;IAC1BV,IAAI,EAAEQ;EACR,CAAC,CAAE;AAET", "ignoreList": []}
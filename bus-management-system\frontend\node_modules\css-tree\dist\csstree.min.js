!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).csstree=t()}(this,(function(){"use strict";function e(e){return{prev:null,next:null,data:e}}function t(e,t,n){var i;return null!==r?(i=r,r=r.cursor,i.prev=t,i.next=n,i.cursor=e.cursor):i={prev:t,next:n,cursor:e.cursor},e.cursor=i,i}function n(e){var t=e.cursor;e.cursor=t.cursor,t.prev=null,t.next=null,t.cursor=r,r=t}var r=null,i=function(){this.cursor=null,this.head=null,this.tail=null};i.createItem=e,i.prototype.createItem=e,i.prototype.updateCursors=function(e,t,n,r){for(var i=this.cursor;null!==i;)i.prev===e&&(i.prev=t),i.next===n&&(i.next=r),i=i.cursor},i.prototype.getSize=function(){for(var e=0,t=this.head;t;)e++,t=t.next;return e},i.prototype.fromArray=function(t){var n=null;this.head=null;for(var r=0;r<t.length;r++){var i=e(t[r]);null!==n?n.next=i:this.head=i,i.prev=n,n=i}return this.tail=n,this},i.prototype.toArray=function(){for(var e=this.head,t=[];e;)t.push(e.data),e=e.next;return t},i.prototype.toJSON=i.prototype.toArray,i.prototype.isEmpty=function(){return null===this.head},i.prototype.first=function(){return this.head&&this.head.data},i.prototype.last=function(){return this.tail&&this.tail.data},i.prototype.each=function(e,r){var i;void 0===r&&(r=this);for(var a=t(this,null,this.head);null!==a.next;)i=a.next,a.next=i.next,e.call(r,i.data,i,this);n(this)},i.prototype.forEach=i.prototype.each,i.prototype.eachRight=function(e,r){var i;void 0===r&&(r=this);for(var a=t(this,this.tail,null);null!==a.prev;)i=a.prev,a.prev=i.prev,e.call(r,i.data,i,this);n(this)},i.prototype.forEachRight=i.prototype.eachRight,i.prototype.nextUntil=function(e,r,i){if(null!==e){var a;void 0===i&&(i=this);for(var o=t(this,null,e);null!==o.next&&(a=o.next,o.next=a.next,!r.call(i,a.data,a,this)););n(this)}},i.prototype.prevUntil=function(e,r,i){if(null!==e){var a;void 0===i&&(i=this);for(var o=t(this,e,null);null!==o.prev&&(a=o.prev,o.prev=a.prev,!r.call(i,a.data,a,this)););n(this)}},i.prototype.some=function(e,t){var n=this.head;for(void 0===t&&(t=this);null!==n;){if(e.call(t,n.data,n,this))return!0;n=n.next}return!1},i.prototype.map=function(e,t){var n=new i,r=this.head;for(void 0===t&&(t=this);null!==r;)n.appendData(e.call(t,r.data,r,this)),r=r.next;return n},i.prototype.filter=function(e,t){var n=new i,r=this.head;for(void 0===t&&(t=this);null!==r;)e.call(t,r.data,r,this)&&n.appendData(r.data),r=r.next;return n},i.prototype.clear=function(){this.head=null,this.tail=null},i.prototype.copy=function(){for(var t=new i,n=this.head;null!==n;)t.insert(e(n.data)),n=n.next;return t},i.prototype.prepend=function(e){return this.updateCursors(null,e,this.head,e),null!==this.head?(this.head.prev=e,e.next=this.head):this.tail=e,this.head=e,this},i.prototype.prependData=function(t){return this.prepend(e(t))},i.prototype.append=function(e){return this.insert(e)},i.prototype.appendData=function(t){return this.insert(e(t))},i.prototype.insert=function(e,t){if(null!=t)if(this.updateCursors(t.prev,e,t,e),null===t.prev){if(this.head!==t)throw new Error("before doesn't belong to list");this.head=e,t.prev=e,e.next=t,this.updateCursors(null,e)}else t.prev.next=e,e.prev=t.prev,t.prev=e,e.next=t;else this.updateCursors(this.tail,e,null,e),null!==this.tail?(this.tail.next=e,e.prev=this.tail):this.head=e,this.tail=e;return this},i.prototype.insertData=function(t,n){return this.insert(e(t),n)},i.prototype.remove=function(e){if(this.updateCursors(e,e.prev,e,e.next),null!==e.prev)e.prev.next=e.next;else{if(this.head!==e)throw new Error("item doesn't belong to list");this.head=e.next}if(null!==e.next)e.next.prev=e.prev;else{if(this.tail!==e)throw new Error("item doesn't belong to list");this.tail=e.prev}return e.prev=null,e.next=null,e},i.prototype.push=function(t){this.insert(e(t))},i.prototype.pop=function(){if(null!==this.tail)return this.remove(this.tail)},i.prototype.unshift=function(t){this.prepend(e(t))},i.prototype.shift=function(){if(null!==this.head)return this.remove(this.head)},i.prototype.prependList=function(e){return this.insertList(e,this.head)},i.prototype.appendList=function(e){return this.insertList(e)},i.prototype.insertList=function(e,t){return null===e.head?this:(null!=t?(this.updateCursors(t.prev,e.tail,t,e.head),null!==t.prev?(t.prev.next=e.head,e.head.prev=t.prev):this.head=e.head,t.prev=e.tail,e.tail.next=t):(this.updateCursors(this.tail,e.tail,null,e.head),null!==this.tail?(this.tail.next=e.head,e.head.prev=this.tail):this.head=e.head,this.tail=e.tail),e.head=null,e.tail=null,this)},i.prototype.replace=function(e,t){"head"in t?this.insertList(t,e):this.insert(t,e),this.remove(e)};var a=i,o=function(e,t){var n=Object.create(SyntaxError.prototype),r=new Error;return n.name=e,n.message=t,Object.defineProperty(n,"stack",{get:function(){return(r.stack||"").replace(/^(.+\n){1,3}/,e+": "+t+"\n")}}),n},s=100,l=60,c="    ";function u(e,t){function n(e,t){return r.slice(e,t).map((function(t,n){for(var r=String(e+n+1);r.length<h;)r=" "+r;return r+" |"+t})).join("\n")}var r=e.source.split(/\r\n?|\n|\f/),i=e.line,a=e.column,o=Math.max(1,i-t)-1,u=Math.min(i+t,r.length+1),h=Math.max(4,String(u).length)+1,p=0;(a+=(c.length-1)*(r[i-1].substr(0,a-1).match(/\t/g)||[]).length)>s&&(p=a-l+3,a=l-2);for(var d=o;d<=u;d++)d>=0&&d<r.length&&(r[d]=r[d].replace(/\t/g,c),r[d]=(p>0&&r[d].length>p?"…":"")+r[d].substr(p,s-2)+(r[d].length>p+s-1?"…":""));return[n(o,i),new Array(a+h+2).join("-")+"^",n(i,u)].filter(Boolean).join("\n")}var h=function(e,t,n,r,i){var a=o("SyntaxError",e);return a.source=t,a.offset=n,a.line=r,a.column=i,a.sourceFragment=function(e){return u(a,isNaN(e)?0:e)},Object.defineProperty(a,"formattedMessage",{get:function(){return"Parse error: "+a.message+"\n"+u(a,2)}}),a.parseError={offset:n,line:r,column:i},a},p={EOF:0,Ident:1,Function:2,AtKeyword:3,Hash:4,String:5,BadString:6,Url:7,BadUrl:8,Delim:9,Number:10,Percentage:11,Dimension:12,WhiteSpace:13,CDO:14,CDC:15,Colon:16,Semicolon:17,Comma:18,LeftSquareBracket:19,RightSquareBracket:20,LeftParenthesis:21,RightParenthesis:22,LeftCurlyBracket:23,RightCurlyBracket:24,Comment:25},d=Object.keys(p).reduce((function(e,t){return e[p[t]]=t,e}),{}),m={TYPE:p,NAME:d},g=0;function f(e){return e>=48&&e<=57}function b(e){return e>=65&&e<=90}function y(e){return e>=97&&e<=122}function k(e){return b(e)||y(e)}function v(e){return e>=128}function x(e){return k(e)||v(e)||95===e}function w(e){return e>=0&&e<=8||11===e||e>=14&&e<=31||127===e}function S(e){return 10===e||13===e||12===e}function C(e){return S(e)||32===e||9===e}function z(e,t){return 92===e&&(!S(t)&&t!==g)}var A=new Array(128);T.Eof=128,T.WhiteSpace=130,T.Digit=131,T.NameStart=132,T.NonPrintable=133;for(var P=0;P<A.length;P++)switch(!0){case C(P):A[P]=T.WhiteSpace;break;case f(P):A[P]=T.Digit;break;case x(P):A[P]=T.NameStart;break;case w(P):A[P]=T.NonPrintable;break;default:A[P]=P||T.Eof}function T(e){return e<128?A[e]:T.NameStart}var L={isDigit:f,isHexDigit:function(e){return f(e)||e>=65&&e<=70||e>=97&&e<=102},isUppercaseLetter:b,isLowercaseLetter:y,isLetter:k,isNonAscii:v,isNameStart:x,isName:function(e){return x(e)||f(e)||45===e},isNonPrintable:w,isNewline:S,isWhiteSpace:C,isValidEscape:z,isIdentifierStart:function(e,t,n){return 45===e?x(t)||45===t||z(t,n):!!x(e)||92===e&&z(e,t)},isNumberStart:function(e,t,n){return 43===e||45===e?f(t)?2:46===t&&f(n)?3:0:46===e?f(t)?2:0:f(e)?1:0},isBOM:function(e){return 65279===e?1:65534===e?1:0},charCodeCategory:T},E=L.isDigit,O=L.isHexDigit,D=L.isUppercaseLetter,B=L.isName,I=L.isWhiteSpace,N=L.isValidEscape;function M(e,t){return t<e.length?e.charCodeAt(t):0}function R(e,t,n){return 13===n&&10===M(e,t+1)?2:1}function _(e,t,n){var r=e.charCodeAt(t);return D(r)&&(r|=32),r===n}function j(e,t){for(;t<e.length&&E(e.charCodeAt(t));t++);return t}function F(e,t){if(O(M(e,(t+=2)-1))){for(var n=Math.min(e.length,t+5);t<n&&O(M(e,t));t++);var r=M(e,t);I(r)&&(t+=R(e,t,r))}return t}var W={consumeEscaped:F,consumeName:function(e,t){for(;t<e.length;t++){var n=e.charCodeAt(t);if(!B(n)){if(!N(n,M(e,t+1)))break;t=F(e,t)-1}}return t},consumeNumber:function(e,t){var n=e.charCodeAt(t);if(43!==n&&45!==n||(n=e.charCodeAt(t+=1)),E(n)&&(t=j(e,t+1),n=e.charCodeAt(t)),46===n&&E(e.charCodeAt(t+1))&&(n=e.charCodeAt(t+=2),t=j(e,t)),_(e,t,101)){var r=0;45!==(n=e.charCodeAt(t+1))&&43!==n||(r=1,n=e.charCodeAt(t+2)),E(n)&&(t=j(e,t+1+r+1))}return t},consumeBadUrlRemnants:function(e,t){for(;t<e.length;t++){var n=e.charCodeAt(t);if(41===n){t++;break}N(n,M(e,t+1))&&(t=F(e,t))}return t},cmpChar:_,cmpStr:function(e,t,n,r){if(n-t!==r.length)return!1;if(t<0||n>e.length)return!1;for(var i=t;i<n;i++){var a=e.charCodeAt(i),o=r.charCodeAt(i-t);if(D(a)&&(a|=32),a!==o)return!1}return!0},getNewlineLength:R,findWhiteSpaceStart:function(e,t){for(;t>=0&&I(e.charCodeAt(t));t--);return t+1},findWhiteSpaceEnd:function(e,t){for(;t<e.length&&I(e.charCodeAt(t));t++);return t}},q=m.TYPE,Y=m.NAME,U=W.cmpStr,H=q.EOF,V=q.WhiteSpace,G=q.Comment,K=function(){this.offsetAndType=null,this.balance=null,this.reset()};K.prototype={reset:function(){this.eof=!1,this.tokenIndex=-1,this.tokenType=0,this.tokenStart=this.firstCharOffset,this.tokenEnd=this.firstCharOffset},lookupType:function(e){return(e+=this.tokenIndex)<this.tokenCount?this.offsetAndType[e]>>24:H},lookupOffset:function(e){return(e+=this.tokenIndex)<this.tokenCount?16777215&this.offsetAndType[e-1]:this.source.length},lookupValue:function(e,t){return(e+=this.tokenIndex)<this.tokenCount&&U(this.source,16777215&this.offsetAndType[e-1],16777215&this.offsetAndType[e],t)},getTokenStart:function(e){return e===this.tokenIndex?this.tokenStart:e>0?e<this.tokenCount?16777215&this.offsetAndType[e-1]:16777215&this.offsetAndType[this.tokenCount]:this.firstCharOffset},getRawLength:function(e,t){var n,r=e,i=16777215&this.offsetAndType[Math.max(r-1,0)];e:for(;r<this.tokenCount&&!((n=this.balance[r])<e);r++)switch(t(this.offsetAndType[r]>>24,this.source,i)){case 1:break e;case 2:r++;break e;default:i=16777215&this.offsetAndType[r],this.balance[n]===r&&(r=n)}return r-this.tokenIndex},isBalanceEdge:function(e){return this.balance[this.tokenIndex]<e},isDelim:function(e,t){return t?this.lookupType(t)===q.Delim&&this.source.charCodeAt(this.lookupOffset(t))===e:this.tokenType===q.Delim&&this.source.charCodeAt(this.tokenStart)===e},getTokenValue:function(){return this.source.substring(this.tokenStart,this.tokenEnd)},getTokenLength:function(){return this.tokenEnd-this.tokenStart},substrToCursor:function(e){return this.source.substring(e,this.tokenStart)},skipWS:function(){for(var e=this.tokenIndex,t=0;e<this.tokenCount&&this.offsetAndType[e]>>24===V;e++,t++);t>0&&this.skip(t)},skipSC:function(){for(;this.tokenType===V||this.tokenType===G;)this.next()},skip:function(e){var t=this.tokenIndex+e;t<this.tokenCount?(this.tokenIndex=t,this.tokenStart=16777215&this.offsetAndType[t-1],t=this.offsetAndType[t],this.tokenType=t>>24,this.tokenEnd=16777215&t):(this.tokenIndex=this.tokenCount,this.next())},next:function(){var e=this.tokenIndex+1;e<this.tokenCount?(this.tokenIndex=e,this.tokenStart=this.tokenEnd,e=this.offsetAndType[e],this.tokenType=e>>24,this.tokenEnd=16777215&e):(this.tokenIndex=this.tokenCount,this.eof=!0,this.tokenType=H,this.tokenStart=this.tokenEnd=this.source.length)},dump:function(){var e=this.firstCharOffset;return Array.prototype.slice.call(this.offsetAndType,0,this.tokenCount).map((function(t,n){var r=e,i=16777215&t;return e=i,{idx:n,type:Y[t>>24],chunk:this.source.substring(r,i),balance:this.balance[n]}}),this)}};var Q=K;function X(e){return e}function Z(e,t,n,r){var i,a;switch(e.type){case"Group":i=function(e,t,n,r){var i=" "===e.combinator||r?e.combinator:" "+e.combinator+" ",a=e.terms.map((function(e){return Z(e,t,n,r)})).join(i);return(e.explicit||n)&&(a=(r||","===a[0]?"[":"[ ")+a+(r?"]":" ]")),a}(e,t,n,r)+(e.disallowEmpty?"!":"");break;case"Multiplier":return Z(e.term,t,n,r)+t(0===(a=e).min&&0===a.max?"*":0===a.min&&1===a.max?"?":1===a.min&&0===a.max?a.comma?"#":"+":1===a.min&&1===a.max?"":(a.comma?"#":"")+(a.min===a.max?"{"+a.min+"}":"{"+a.min+","+(0!==a.max?a.max:"")+"}"),e);case"Type":i="<"+e.name+(e.opts?t(function(e){switch(e.type){case"Range":return" ["+(null===e.min?"-∞":e.min)+","+(null===e.max?"∞":e.max)+"]";default:throw new Error("Unknown node type `"+e.type+"`")}}(e.opts),e.opts):"")+">";break;case"Property":i="<'"+e.name+"'>";break;case"Keyword":i=e.name;break;case"AtKeyword":i="@"+e.name;break;case"Function":i=e.name+"(";break;case"String":case"Token":i=e.value;break;case"Comma":i=",";break;default:throw new Error("Unknown node type `"+e.type+"`")}return t(i,e)}var $=function(e,t){var n=X,r=!1,i=!1;return"function"==typeof t?n=t:t&&(r=Boolean(t.forceBraces),i=Boolean(t.compact),"function"==typeof t.decorate&&(n=t.decorate)),Z(e,n,r,i)};function J(e,t){var n=e&&e.loc&&e.loc[t];return n?{offset:n.offset,line:n.line,column:n.column}:null}var ee=function(e,t){var n=o("SyntaxReferenceError",e+(t?" `"+t+"`":""));return n.reference=t,n},te=function(e,t,n,r){var i=o("SyntaxMatchError",e),a=function(e){for(var t=e.tokens,n=e.longestMatch,r=n<t.length?t[n].node:null,i=-1,a=0,o="",s=0;s<t.length;s++)s===n&&(i=o.length),null!==r&&t[s].node===r&&(s<=n?a++:a=0),o+=t[s].value;return{node:r,css:o,mismatchOffset:-1===i?o.length:i,last:null===r||a>1}}(r),s=a.mismatchOffset||0,l=a.node||n,c=J(l,"end"),u=a.last?c:J(l,"start"),h=a.css;return i.rawMessage=e,i.syntax=t?$(t):"<generic>",i.css=h,i.mismatchOffset=s,i.loc={source:l&&l.loc&&l.loc.source||"<unknown>",start:u,end:c},i.line=u?u.line:void 0,i.column=u?u.column:void 0,i.offset=u?u.offset:void 0,i.message=e+"\n  syntax: "+i.syntax+"\n   value: "+(i.css||"<empty string>")+"\n  --------"+new Array(i.mismatchOffset+1).join("-")+"^",i},ne=Object.prototype.hasOwnProperty,re=Object.create(null),ie=Object.create(null),ae=45;function oe(e,t){return t=t||0,e.length-t>=2&&e.charCodeAt(t)===ae&&e.charCodeAt(t+1)===ae}function se(e,t){if(t=t||0,e.length-t>=3&&e.charCodeAt(t)===ae&&e.charCodeAt(t+1)!==ae){var n=e.indexOf("-",t+2);if(-1!==n)return e.substring(t,n+1)}return""}var le={keyword:function(e){if(ne.call(re,e))return re[e];var t=e.toLowerCase();if(ne.call(re,t))return re[e]=re[t];var n=oe(t,0),r=n?"":se(t,0);return re[e]=Object.freeze({basename:t.substr(r.length),name:t,vendor:r,prefix:r,custom:n})},property:function(e){if(ne.call(ie,e))return ie[e];var t=e,n=e[0];"/"===n?n="/"===e[1]?"//":"/":"_"!==n&&"*"!==n&&"$"!==n&&"#"!==n&&"+"!==n&&"&"!==n&&(n="");var r=oe(t,n.length);if(!r&&(t=t.toLowerCase(),ne.call(ie,t)))return ie[e]=ie[t];var i=r?"":se(t,n.length),a=t.substr(0,n.length+i.length);return ie[e]=Object.freeze({basename:t.substr(a.length),name:t.substr(n.length),hack:n,vendor:i,prefix:a,custom:r})},isCustomProperty:oe,vendorPrefix:se},ce="undefined"!=typeof Uint32Array?Uint32Array:Array,ue=function(e,t){return null===e||e.length<t?new ce(Math.max(t+1024,16384)):e},he=m.TYPE,pe=L.isNewline,de=L.isName,me=L.isValidEscape,ge=L.isNumberStart,fe=L.isIdentifierStart,be=L.charCodeCategory,ye=L.isBOM,ke=W.cmpStr,ve=W.getNewlineLength,xe=W.findWhiteSpaceEnd,we=W.consumeEscaped,Se=W.consumeName,Ce=W.consumeNumber,ze=W.consumeBadUrlRemnants,Ae=16777215,Pe=24;function Te(e,t){function n(t){return t<o?e.charCodeAt(t):0}function r(){return h=Ce(e,h),fe(n(h),n(h+1),n(h+2))?(f=he.Dimension,void(h=Se(e,h))):37===n(h)?(f=he.Percentage,void h++):void(f=he.Number)}function i(){const t=h;return h=Se(e,h),ke(e,t,h,"url")&&40===n(h)?34===n(h=xe(e,h+1))||39===n(h)?(f=he.Function,void(h=t+4)):void function(){for(f=he.Url,h=xe(e,h);h<e.length;h++){var t=e.charCodeAt(h);switch(be(t)){case 41:return void h++;case be.Eof:return;case be.WhiteSpace:return 41===n(h=xe(e,h))||h>=e.length?void(h<e.length&&h++):(h=ze(e,h),void(f=he.BadUrl));case 34:case 39:case 40:case be.NonPrintable:return h=ze(e,h),void(f=he.BadUrl);case 92:if(me(t,n(h+1))){h=we(e,h)-1;break}return h=ze(e,h),void(f=he.BadUrl)}}}():40===n(h)?(f=he.Function,void h++):void(f=he.Ident)}function a(t){for(t||(t=n(h++)),f=he.String;h<e.length;h++){var r=e.charCodeAt(h);switch(be(r)){case t:return void h++;case be.Eof:return;case be.WhiteSpace:if(pe(r))return h+=ve(e,h,r),void(f=he.BadString);break;case 92:if(h===e.length-1)break;var i=n(h+1);pe(i)?h+=ve(e,h+1,i):me(r,i)&&(h=we(e,h)-1)}}}t||(t=new Q);for(var o=(e=String(e||"")).length,s=ue(t.offsetAndType,o+1),l=ue(t.balance,o+1),c=0,u=ye(n(0)),h=u,p=0,d=0,m=0;h<o;){var g=e.charCodeAt(h),f=0;switch(l[c]=o,be(g)){case be.WhiteSpace:f=he.WhiteSpace,h=xe(e,h+1);break;case 34:a();break;case 35:de(n(h+1))||me(n(h+1),n(h+2))?(f=he.Hash,h=Se(e,h+1)):(f=he.Delim,h++);break;case 39:a();break;case 40:f=he.LeftParenthesis,h++;break;case 41:f=he.RightParenthesis,h++;break;case 43:ge(g,n(h+1),n(h+2))?r():(f=he.Delim,h++);break;case 44:f=he.Comma,h++;break;case 45:ge(g,n(h+1),n(h+2))?r():45===n(h+1)&&62===n(h+2)?(f=he.CDC,h+=3):fe(g,n(h+1),n(h+2))?i():(f=he.Delim,h++);break;case 46:ge(g,n(h+1),n(h+2))?r():(f=he.Delim,h++);break;case 47:42===n(h+1)?(f=he.Comment,1===(h=e.indexOf("*/",h+2)+2)&&(h=e.length)):(f=he.Delim,h++);break;case 58:f=he.Colon,h++;break;case 59:f=he.Semicolon,h++;break;case 60:33===n(h+1)&&45===n(h+2)&&45===n(h+3)?(f=he.CDO,h+=4):(f=he.Delim,h++);break;case 64:fe(n(h+1),n(h+2),n(h+3))?(f=he.AtKeyword,h=Se(e,h+1)):(f=he.Delim,h++);break;case 91:f=he.LeftSquareBracket,h++;break;case 92:me(g,n(h+1))?i():(f=he.Delim,h++);break;case 93:f=he.RightSquareBracket,h++;break;case 123:f=he.LeftCurlyBracket,h++;break;case 125:f=he.RightCurlyBracket,h++;break;case be.Digit:r();break;case be.NameStart:i();break;case be.Eof:break;default:f=he.Delim,h++}switch(f){case p:for(p=(d=l[m=d&Ae])>>Pe,l[c]=m,l[m++]=c;m<c;m++)l[m]===o&&(l[m]=c);break;case he.LeftParenthesis:case he.Function:l[c]=d,d=(p=he.RightParenthesis)<<Pe|c;break;case he.LeftSquareBracket:l[c]=d,d=(p=he.RightSquareBracket)<<Pe|c;break;case he.LeftCurlyBracket:l[c]=d,d=(p=he.RightCurlyBracket)<<Pe|c}s[c++]=f<<Pe|h}for(s[c]=he.EOF<<Pe|h,l[c]=o,l[o]=o;0!==d;)d=l[m=d&Ae],l[m]=o;return t.source=e,t.firstCharOffset=u,t.offsetAndType=s,t.tokenCount=c,t.balance=l,t.reset(),t.next(),t}Object.keys(m).forEach((function(e){Te[e]=m[e]})),Object.keys(L).forEach((function(e){Te[e]=L[e]})),Object.keys(W).forEach((function(e){Te[e]=W[e]}));var Le=Te,Ee=Le.isDigit,Oe=Le.cmpChar,De=Le.TYPE,Be=De.Delim,Ie=De.WhiteSpace,Ne=De.Comment,Me=De.Ident,Re=De.Number,_e=De.Dimension,je=43,Fe=45;function We(e,t){return null!==e&&e.type===Be&&e.value.charCodeAt(0)===t}function qe(e,t,n){for(;null!==e&&(e.type===Ie||e.type===Ne);)e=n(++t);return t}function Ye(e,t,n,r){if(!e)return 0;var i=e.value.charCodeAt(t);if(i===je||i===Fe){if(n)return 0;t++}for(;t<e.value.length;t++)if(!Ee(e.value.charCodeAt(t)))return 0;return r+1}function Ue(e,t,n){var r=!1,i=qe(e,t,n);if(null===(e=n(i)))return t;if(e.type!==Re){if(!We(e,je)&&!We(e,Fe))return t;if(r=!0,i=qe(n(++i),i,n),null===(e=n(i))&&e.type!==Re)return 0}if(!r){var a=e.value.charCodeAt(0);if(a!==je&&a!==Fe)return 0}return Ye(e,r?0:1,r,i)}var He=Le.isHexDigit,Ve=Le.cmpChar,Ge=Le.TYPE,Ke=Ge.Ident,Qe=Ge.Delim,Xe=Ge.Number,Ze=Ge.Dimension,$e=45,Je=63;function et(e,t){return null!==e&&e.type===Qe&&e.value.charCodeAt(0)===t}function tt(e,t){return e.value.charCodeAt(0)===t}function nt(e,t,n){for(var r=t,i=0;r<e.value.length;r++){var a=e.value.charCodeAt(r);if(a===$e&&n&&0!==i)return nt(e,t+i+1,!1)>0?6:0;if(!He(a))return 0;if(++i>6)return 0}return i}function rt(e,t,n){if(!e)return 0;for(;et(n(t),Je);){if(++e>6)return 0;t++}return t}var it=Le.isIdentifierStart,at=Le.isHexDigit,ot=Le.isDigit,st=Le.cmpStr,lt=Le.consumeNumber,ct=Le.TYPE,ut=["unset","initial","inherit"],ht=["calc(","-moz-calc(","-webkit-calc("];function pt(e,t){return t<e.length?e.charCodeAt(t):0}function dt(e,t){return st(e,0,e.length,t)}function mt(e,t){for(var n=0;n<t.length;n++)if(dt(e,t[n]))return!0;return!1}function gt(e,t){return t===e.length-2&&(92===e.charCodeAt(t)&&ot(e.charCodeAt(t+1)))}function ft(e,t,n){if(e&&"Range"===e.type){var r=Number(void 0!==n&&n!==t.length?t.substr(0,n):t);if(isNaN(r))return!0;if(null!==e.min&&r<e.min)return!0;if(null!==e.max&&r>e.max)return!0}return!1}function bt(e,t){var n=e.index,r=0;do{if(r++,e.balance<=n)break}while(e=t(r));return r}function yt(e){return function(t,n,r){return null===t?0:t.type===ct.Function&&mt(t.value,ht)?bt(t,n):e(t,n,r)}}function kt(e){return function(t){return null===t||t.type!==e?0:1}}function vt(e){return function(t,n,r){if(null===t||t.type!==ct.Dimension)return 0;var i=lt(t.value,0);if(null!==e){var a=t.value.indexOf("\\",i),o=-1!==a&&gt(t.value,a)?t.value.substring(i,a):t.value.substr(i);if(!1===e.hasOwnProperty(o.toLowerCase()))return 0}return ft(r,t.value,i)?0:1}}function xt(e){return"function"!=typeof e&&(e=function(){return 0}),function(t,n,r){return null!==t&&t.type===ct.Number&&0===Number(t.value)?1:e(t,n,r)}}var wt,St={"ident-token":kt(ct.Ident),"function-token":kt(ct.Function),"at-keyword-token":kt(ct.AtKeyword),"hash-token":kt(ct.Hash),"string-token":kt(ct.String),"bad-string-token":kt(ct.BadString),"url-token":kt(ct.Url),"bad-url-token":kt(ct.BadUrl),"delim-token":kt(ct.Delim),"number-token":kt(ct.Number),"percentage-token":kt(ct.Percentage),"dimension-token":kt(ct.Dimension),"whitespace-token":kt(ct.WhiteSpace),"CDO-token":kt(ct.CDO),"CDC-token":kt(ct.CDC),"colon-token":kt(ct.Colon),"semicolon-token":kt(ct.Semicolon),"comma-token":kt(ct.Comma),"[-token":kt(ct.LeftSquareBracket),"]-token":kt(ct.RightSquareBracket),"(-token":kt(ct.LeftParenthesis),")-token":kt(ct.RightParenthesis),"{-token":kt(ct.LeftCurlyBracket),"}-token":kt(ct.RightCurlyBracket),string:kt(ct.String),ident:kt(ct.Ident),"custom-ident":function(e){if(null===e||e.type!==ct.Ident)return 0;var t=e.value.toLowerCase();return mt(t,ut)?0:dt(t,"default")?0:1},"custom-property-name":function(e){return null===e||e.type!==ct.Ident?0:45!==pt(e.value,0)||45!==pt(e.value,1)?0:1},"hex-color":function(e){if(null===e||e.type!==ct.Hash)return 0;var t=e.value.length;if(4!==t&&5!==t&&7!==t&&9!==t)return 0;for(var n=1;n<t;n++)if(!at(e.value.charCodeAt(n)))return 0;return 1},"id-selector":function(e){return null===e||e.type!==ct.Hash?0:it(pt(e.value,1),pt(e.value,2),pt(e.value,3))?1:0},"an-plus-b":function(e,t){var n=0;if(!e)return 0;if(e.type===Re)return Ye(e,0,!1,n);if(e.type===Me&&e.value.charCodeAt(0)===Fe){if(!Oe(e.value,1,110))return 0;switch(e.value.length){case 2:return Ue(t(++n),n,t);case 3:return e.value.charCodeAt(2)!==Fe?0:(n=qe(t(++n),n,t),Ye(e=t(n),0,!0,n));default:return e.value.charCodeAt(2)!==Fe?0:Ye(e,3,!0,n)}}else if(e.type===Me||We(e,je)&&t(n+1).type===Me){if(e.type!==Me&&(e=t(++n)),null===e||!Oe(e.value,0,110))return 0;switch(e.value.length){case 1:return Ue(t(++n),n,t);case 2:return e.value.charCodeAt(1)!==Fe?0:(n=qe(t(++n),n,t),Ye(e=t(n),0,!0,n));default:return e.value.charCodeAt(1)!==Fe?0:Ye(e,2,!0,n)}}else if(e.type===_e){for(var r=e.value.charCodeAt(0),i=r===je||r===Fe?1:0,a=i;a<e.value.length&&Ee(e.value.charCodeAt(a));a++);return a===i?0:Oe(e.value,a,110)?a+1===e.value.length?Ue(t(++n),n,t):e.value.charCodeAt(a+1)!==Fe?0:a+2===e.value.length?(n=qe(t(++n),n,t),Ye(e=t(n),0,!0,n)):Ye(e,a+2,!0,n):0}return 0},urange:function(e,t){var n=0;if(null===e||e.type!==Ke||!Ve(e.value,0,117))return 0;if(null===(e=t(++n)))return 0;if(et(e,43))return null===(e=t(++n))?0:e.type===Ke?rt(nt(e,0,!0),++n,t):et(e,Je)?rt(1,++n,t):0;if(e.type===Xe){if(!tt(e,43))return 0;var r=nt(e,1,!0);return 0===r?0:null===(e=t(++n))?n:e.type===Ze||e.type===Xe?tt(e,$e)&&nt(e,1,!1)?n+1:0:rt(r,n,t)}return e.type===Ze&&tt(e,43)?rt(nt(e,1,!0),++n,t):0},"declaration-value":function(e,t){if(!e)return 0;var n=0,r=0,i=e.index;e:do{switch(e.type){case ct.BadString:case ct.BadUrl:break e;case ct.RightCurlyBracket:case ct.RightParenthesis:case ct.RightSquareBracket:if(e.balance>e.index||e.balance<i)break e;r--;break;case ct.Semicolon:if(0===r)break e;break;case ct.Delim:if("!"===e.value&&0===r)break e;break;case ct.Function:case ct.LeftParenthesis:case ct.LeftSquareBracket:case ct.LeftCurlyBracket:r++}if(n++,e.balance<=i)break}while(e=t(n));return n},"any-value":function(e,t){if(!e)return 0;var n=e.index,r=0;e:do{switch(e.type){case ct.BadString:case ct.BadUrl:break e;case ct.RightCurlyBracket:case ct.RightParenthesis:case ct.RightSquareBracket:if(e.balance>e.index||e.balance<n)break e}if(r++,e.balance<=n)break}while(e=t(r));return r},dimension:yt(vt(null)),angle:yt(vt({deg:!0,grad:!0,rad:!0,turn:!0})),decibel:yt(vt({db:!0})),frequency:yt(vt({hz:!0,khz:!0})),flex:yt(vt({fr:!0})),length:yt(xt(vt({px:!0,mm:!0,cm:!0,in:!0,pt:!0,pc:!0,q:!0,em:!0,ex:!0,ch:!0,rem:!0,vh:!0,vw:!0,vmin:!0,vmax:!0,vm:!0}))),resolution:yt(vt({dpi:!0,dpcm:!0,dppx:!0,x:!0})),semitones:yt(vt({st:!0})),time:yt(vt({s:!0,ms:!0})),percentage:yt((function(e,t,n){return null===e||e.type!==ct.Percentage?0:ft(n,e.value,e.value.length-1)?0:1})),zero:xt(),number:yt((function(e,t,n){if(null===e)return 0;var r=lt(e.value,0);return r===e.value.length||gt(e.value,r)?ft(n,e.value,r)?0:1:0})),integer:yt((function(e,t,n){if(null===e||e.type!==ct.Number)return 0;for(var r=43===e.value.charCodeAt(0)||45===e.value.charCodeAt(0)?1:0;r<e.value.length;r++)if(!ot(e.value.charCodeAt(r)))return 0;return ft(n,e.value,r)?0:1})),"-ms-legacy-expression":(wt="expression",wt+="(",function(e,t){return null!==e&&dt(e.value,wt)?bt(e,t):0})},Ct=function(e,t,n){var r=o("SyntaxError",e);return r.input=t,r.offset=n,r.rawMessage=e,r.message=r.rawMessage+"\n  "+r.input+"\n--"+new Array((r.offset||r.input.length)+1).join("-")+"^",r},zt=function(e){this.str=e,this.pos=0};zt.prototype={charCodeAt:function(e){return e<this.str.length?this.str.charCodeAt(e):0},charCode:function(){return this.charCodeAt(this.pos)},nextCharCode:function(){return this.charCodeAt(this.pos+1)},nextNonWsCode:function(e){return this.charCodeAt(this.findWsEnd(e))},findWsEnd:function(e){for(;e<this.str.length;e++){var t=this.str.charCodeAt(e);if(13!==t&&10!==t&&12!==t&&32!==t&&9!==t)break}return e},substringToPos:function(e){return this.str.substring(this.pos,this.pos=e)},eat:function(e){this.charCode()!==e&&this.error("Expect `"+String.fromCharCode(e)+"`"),this.pos++},peek:function(){return this.pos<this.str.length?this.str.charAt(this.pos++):""},error:function(e){throw new Ct(e,this.str,this.pos)}};var At=zt,Pt=9,Tt=10,Lt=12,Et=13,Ot=32,Dt=33,Bt=35,It=38,Nt=39,Mt=40,Rt=41,_t=42,jt=43,Ft=44,Wt=45,qt=60,Yt=62,Ut=63,Ht=64,Vt=91,Gt=93,Kt=123,Qt=124,Xt=125,Zt=8734,$t=function(e){for(var t="function"==typeof Uint32Array?new Uint32Array(128):new Array(128),n=0;n<128;n++)t[n]=e(String.fromCharCode(n))?1:0;return t}((function(e){return/[a-zA-Z0-9\-]/.test(e)})),Jt={" ":1,"&&":2,"||":3,"|":4};function en(e){return e.substringToPos(e.findWsEnd(e.pos))}function tn(e){for(var t=e.pos;t<e.str.length;t++){var n=e.str.charCodeAt(t);if(n>=128||0===$t[n])break}return e.pos===t&&e.error("Expect a keyword"),e.substringToPos(t)}function nn(e){for(var t=e.pos;t<e.str.length;t++){var n=e.str.charCodeAt(t);if(n<48||n>57)break}return e.pos===t&&e.error("Expect a number"),e.substringToPos(t)}function rn(e){var t=e.str.indexOf("'",e.pos+1);return-1===t&&(e.pos=e.str.length,e.error("Expect an apostrophe")),e.substringToPos(t+1)}function an(e){var t,n=null;return e.eat(Kt),t=nn(e),e.charCode()===Ft?(e.pos++,e.charCode()!==Xt&&(n=nn(e))):n=t,e.eat(Xt),{min:Number(t),max:n?Number(n):0}}function on(e,t){var n=function(e){var t=null,n=!1;switch(e.charCode()){case _t:e.pos++,t={min:0,max:0};break;case jt:e.pos++,t={min:1,max:0};break;case Ut:e.pos++,t={min:0,max:1};break;case Bt:e.pos++,n=!0,t=e.charCode()===Kt?an(e):{min:1,max:0};break;case Kt:t=an(e);break;default:return null}return{type:"Multiplier",comma:n,min:t.min,max:t.max,term:null}}(e);return null!==n?(n.term=t,n):t}function sn(e){var t=e.peek();return""===t?null:{type:"Token",value:t}}function ln(e){var t,n=null;return e.eat(qt),t=tn(e),e.charCode()===Mt&&e.nextCharCode()===Rt&&(e.pos+=2,t+="()"),e.charCodeAt(e.findWsEnd(e.pos))===Vt&&(en(e),n=function(e){var t=null,n=null,r=1;return e.eat(Vt),e.charCode()===Wt&&(e.peek(),r=-1),-1==r&&e.charCode()===Zt?e.peek():t=r*Number(nn(e)),en(e),e.eat(Ft),en(e),e.charCode()===Zt?e.peek():(r=1,e.charCode()===Wt&&(e.peek(),r=-1),n=r*Number(nn(e))),e.eat(Gt),null===t&&null===n?null:{type:"Range",min:t,max:n}}(e)),e.eat(Yt),on(e,{type:"Type",name:t,opts:n})}function cn(e,t){function n(e,t){return{type:"Group",terms:e,combinator:t,disallowEmpty:!1,explicit:!1}}for(t=Object.keys(t).sort((function(e,t){return Jt[e]-Jt[t]}));t.length>0;){for(var r=t.shift(),i=0,a=0;i<e.length;i++){var o=e[i];"Combinator"===o.type&&(o.value===r?(-1===a&&(a=i-1),e.splice(i,1),i--):(-1!==a&&i-a>1&&(e.splice(a,i-a,n(e.slice(a,i),r)),i=a+1),a=-1))}-1!==a&&t.length&&e.splice(a,i-a,n(e.slice(a,i),r))}return r}function un(e){for(var t,n=[],r={},i=null,a=e.pos;t=hn(e);)"Spaces"!==t.type&&("Combinator"===t.type?(null!==i&&"Combinator"!==i.type||(e.pos=a,e.error("Unexpected combinator")),r[t.value]=!0):null!==i&&"Combinator"!==i.type&&(r[" "]=!0,n.push({type:"Combinator",value:" "})),n.push(t),i=t,a=e.pos);return null!==i&&"Combinator"===i.type&&(e.pos-=a,e.error("Unexpected combinator")),{type:"Group",terms:n,combinator:cn(n,r)||" ",disallowEmpty:!1,explicit:!1}}function hn(e){var t=e.charCode();if(t<128&&1===$t[t])return function(e){var t;return t=tn(e),e.charCode()===Mt?(e.pos++,{type:"Function",name:t}):on(e,{type:"Keyword",name:t})}(e);switch(t){case Gt:break;case Vt:return on(e,function(e){var t;return e.eat(Vt),t=un(e),e.eat(Gt),t.explicit=!0,e.charCode()===Dt&&(e.pos++,t.disallowEmpty=!0),t}(e));case qt:return e.nextCharCode()===Nt?function(e){var t;return e.eat(qt),e.eat(Nt),t=tn(e),e.eat(Nt),e.eat(Yt),on(e,{type:"Property",name:t})}(e):ln(e);case Qt:return{type:"Combinator",value:e.substringToPos(e.nextCharCode()===Qt?e.pos+2:e.pos+1)};case It:return e.pos++,e.eat(It),{type:"Combinator",value:"&&"};case Ft:return e.pos++,{type:"Comma"};case Nt:return on(e,{type:"String",value:rn(e)});case Ot:case Pt:case Tt:case Et:case Lt:return{type:"Spaces",value:en(e)};case Ht:return(t=e.nextCharCode())<128&&1===$t[t]?(e.pos++,{type:"AtKeyword",name:tn(e)}):sn(e);case _t:case jt:case Ut:case Bt:case Dt:break;case Kt:if((t=e.nextCharCode())<48||t>57)return sn(e);break;default:return sn(e)}}function pn(e){var t=new At(e),n=un(t);return t.pos!==e.length&&t.error("Unexpected input"),1===n.terms.length&&"Group"===n.terms[0].type&&(n=n.terms[0]),n}pn("[a&&<b>#|<'c'>*||e() f{2} /,(% g#{1,2} h{2,})]!");var dn=pn,mn=function(){};function gn(e){return"function"==typeof e?e:mn}var fn=function(e,t,n){var r=mn,i=mn;if("function"==typeof t?r=t:t&&(r=gn(t.enter),i=gn(t.leave)),r===mn&&i===mn)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");!function e(t){switch(r.call(n,t),t.type){case"Group":t.terms.forEach(e);break;case"Multiplier":e(t.term);break;case"Type":case"Property":case"Keyword":case"AtKeyword":case"Function":case"String":case"Token":case"Comma":break;default:throw new Error("Unknown type: "+t.type)}i.call(n,t)}(e)},bn=new Q,yn={decorator:function(e){var t=null,n={len:0,node:null},r=[n],i="";return{children:e.children,node:function(n){var r=t;t=n,e.node.call(this,n),t=r},chunk:function(e){i+=e,n.node!==t?r.push({len:e.length,node:t}):n.len+=e.length},result:function(){return kn(i,r)}}}};function kn(e,t){var n=[],r=0,i=0,a=t?t[i].node:null;for(Le(e,bn);!bn.eof;){if(t)for(;i<t.length&&r+t[i].len<=bn.tokenStart;)r+=t[i++].len,a=t[i].node;n.push({type:bn.tokenType,value:bn.getTokenValue(),index:bn.tokenIndex,balance:bn.balance[bn.tokenIndex],node:a}),bn.next()}return n}var vn=function(e,t){return"string"==typeof e?kn(e,null):t.generate(e,yn)},xn={type:"Match"},wn={type:"Mismatch"},Sn={type:"DisallowEmpty"},Cn=40,zn=41;function An(e,t,n){return t===xn&&n===wn?e:e===xn&&t===xn&&n===xn?e:("If"===e.type&&e.else===wn&&t===xn&&(t=e.then,e=e.match),{type:"If",match:e,then:t,else:n})}function Pn(e){return e.length>2&&e.charCodeAt(e.length-2)===Cn&&e.charCodeAt(e.length-1)===zn}function Tn(e){return"Keyword"===e.type||"AtKeyword"===e.type||"Function"===e.type||"Type"===e.type&&Pn(e.name)}function Ln(e){if("function"==typeof e)return{type:"Generic",fn:e};switch(e.type){case"Group":var t=function e(t,n,r){switch(t){case" ":for(var i=xn,a=n.length-1;a>=0;a--){i=An(l=n[a],i,wn)}return i;case"|":i=wn;var o=null;for(a=n.length-1;a>=0;a--){if(Tn(l=n[a])&&(null===o&&a>0&&Tn(n[a-1])&&(i=An({type:"Enum",map:o=Object.create(null)},xn,i)),null!==o)){var s=(Pn(l.name)?l.name.slice(0,-1):l.name).toLowerCase();if(s in o==!1){o[s]=l;continue}}o=null,i=An(l,xn,i)}return i;case"&&":if(n.length>5)return{type:"MatchOnce",terms:n,all:!0};for(i=wn,a=n.length-1;a>=0;a--){var l=n[a];c=n.length>1?e(t,n.filter((function(e){return e!==l})),!1):xn,i=An(l,c,i)}return i;case"||":if(n.length>5)return{type:"MatchOnce",terms:n,all:!1};for(i=r?xn:wn,a=n.length-1;a>=0;a--){var c;l=n[a];c=n.length>1?e(t,n.filter((function(e){return e!==l})),!0):xn,i=An(l,c,i)}return i}}(e.combinator,e.terms.map(Ln),!1);return e.disallowEmpty&&(t=An(t,Sn,wn)),t;case"Multiplier":return function(e){var t=xn,n=Ln(e.term);if(0===e.max)n=An(n,Sn,wn),(t=An(n,null,wn)).then=An(xn,xn,t),e.comma&&(t.then.else=An({type:"Comma",syntax:e},t,wn));else for(var r=e.min||1;r<=e.max;r++)e.comma&&t!==xn&&(t=An({type:"Comma",syntax:e},t,wn)),t=An(n,An(xn,xn,t),wn);if(0===e.min)t=An(xn,xn,t);else for(r=0;r<e.min-1;r++)e.comma&&t!==xn&&(t=An({type:"Comma",syntax:e},t,wn)),t=An(n,t,wn);return t}(e);case"Type":case"Property":return{type:e.type,name:e.name,syntax:e};case"Keyword":return{type:e.type,name:e.name.toLowerCase(),syntax:e};case"AtKeyword":return{type:e.type,name:"@"+e.name.toLowerCase(),syntax:e};case"Function":return{type:e.type,name:e.name.toLowerCase()+"(",syntax:e};case"String":return 3===e.value.length?{type:"Token",value:e.value.charAt(1),syntax:e}:{type:e.type,value:e.value.substr(1,e.value.length-2).replace(/\\'/g,"'"),syntax:e};case"Token":return{type:e.type,value:e.value,syntax:e};case"Comma":return{type:e.type,syntax:e};default:throw new Error("Unknown node type:",e.type)}}var En={MATCH:xn,MISMATCH:wn,DISALLOW_EMPTY:Sn,buildMatchGraph:function(e,t){return"string"==typeof e&&(e=dn(e)),{type:"MatchGraph",match:Ln(e),syntax:t||null,source:e}}},On=Object.prototype.hasOwnProperty,Dn=En.MATCH,Bn=En.MISMATCH,In=En.DISALLOW_EMPTY,Nn=m.TYPE,Mn=0,Rn=1,_n=2,jn=3,Fn="Match",Wn="Mismatch",qn="Maximum iteration number exceeded (please fill an issue on https://github.com/csstree/csstree/issues)",Yn=15e3;function Un(e){for(var t=null,n=null,r=e;null!==r;)n=r.prev,r.prev=t,t=r,r=n;return t}function Hn(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);if(r>=65&&r<=90&&(r|=32),r!==t.charCodeAt(n))return!1}return!0}function Vn(e){return null===e||(e.type===Nn.Comma||e.type===Nn.Function||e.type===Nn.LeftParenthesis||e.type===Nn.LeftSquareBracket||e.type===Nn.LeftCurlyBracket||e.type===Nn.Delim)}function Gn(e){return null===e||(e.type===Nn.RightParenthesis||e.type===Nn.RightSquareBracket||e.type===Nn.RightCurlyBracket||e.type===Nn.Delim)}function Kn(e,t,n){function r(){do{f=++b<e.length?e[b]:null}while(null!==f&&(f.type===Nn.WhiteSpace||f.type===Nn.Comment))}function i(t){var n=b+t;return n<e.length?e[n]:null}function a(e,t){return{nextState:e,matchStack:k,syntaxStack:u,thenStack:h,tokenIndex:b,prev:t}}function o(e){h={nextState:e,matchStack:k,syntaxStack:u,prev:h}}function s(e){p=a(e,p)}function l(){k={type:Rn,syntax:t.syntax,token:f,prev:k},r(),d=null,b>y&&(y=b)}function c(){k=k.type===_n?k.prev:{type:jn,syntax:u.syntax,token:k.token,prev:k},u=u.prev}var u=null,h=null,p=null,d=null,m=0,g=null,f=null,b=-1,y=0,k={type:Mn,syntax:null,token:null,prev:null};for(r();null===g&&++m<Yn;)switch(t.type){case"Match":if(null===h){if(null!==f&&(b!==e.length-1||"\\0"!==f.value&&"\\9"!==f.value)){t=Bn;break}g=Fn;break}if((t=h.nextState)===In){if(h.matchStack===k){t=Bn;break}t=Dn}for(;h.syntaxStack!==u;)c();h=h.prev;break;case"Mismatch":if(null!==d&&!1!==d)(null===p||b>p.tokenIndex)&&(p=d,d=!1);else if(null===p){g=Wn;break}t=p.nextState,h=p.thenStack,u=p.syntaxStack,k=p.matchStack,b=p.tokenIndex,f=b<e.length?e[b]:null,p=p.prev;break;case"MatchGraph":t=t.match;break;case"If":t.else!==Bn&&s(t.else),t.then!==Dn&&o(t.then),t=t.match;break;case"MatchOnce":t={type:"MatchOnceBuffer",syntax:t,index:0,mask:0};break;case"MatchOnceBuffer":var v=t.syntax.terms;if(t.index===v.length){if(0===t.mask||t.syntax.all){t=Bn;break}t=Dn;break}if(t.mask===(1<<v.length)-1){t=Dn;break}for(;t.index<v.length;t.index++){var x=1<<t.index;if(0==(t.mask&x)){s(t),o({type:"AddMatchOnce",syntax:t.syntax,mask:t.mask|x}),t=v[t.index++];break}}break;case"AddMatchOnce":t={type:"MatchOnceBuffer",syntax:t.syntax,index:0,mask:t.mask};break;case"Enum":if(null!==f)if(-1!==(A=f.value.toLowerCase()).indexOf("\\")&&(A=A.replace(/\\[09].*$/,"")),On.call(t.map,A)){t=t.map[A];break}t=Bn;break;case"Generic":var w=null!==u?u.opts:null,S=b+Math.floor(t.fn(f,i,w));if(!isNaN(S)&&S>b){for(;b<S;)l();t=Dn}else t=Bn;break;case"Type":case"Property":var C="Type"===t.type?"types":"properties",z=On.call(n,C)?n[C][t.name]:null;if(!z||!z.match)throw new Error("Bad syntax reference: "+("Type"===t.type?"<"+t.name+">":"<'"+t.name+"'>"));if(!1!==d&&null!==f&&"Type"===t.type)if("custom-ident"===t.name&&f.type===Nn.Ident||"length"===t.name&&"0"===f.value){null===d&&(d=a(t,p)),t=Bn;break}u={syntax:t.syntax,opts:t.syntax.opts||null!==u&&u.opts||null,prev:u},k={type:_n,syntax:t.syntax,token:k.token,prev:k},t=z.match;break;case"Keyword":var A=t.name;if(null!==f){var P=f.value;if(-1!==P.indexOf("\\")&&(P=P.replace(/\\[09].*$/,"")),Hn(P,A)){l(),t=Dn;break}}t=Bn;break;case"AtKeyword":case"Function":if(null!==f&&Hn(f.value,t.name)){l(),t=Dn;break}t=Bn;break;case"Token":if(null!==f&&f.value===t.value){l(),t=Dn;break}t=Bn;break;case"Comma":null!==f&&f.type===Nn.Comma?Vn(k.token)?t=Bn:(l(),t=Gn(f)?Bn:Dn):t=Vn(k.token)||Gn(f)?Dn:Bn;break;case"String":var T="";for(S=b;S<e.length&&T.length<t.value.length;S++)T+=e[S].value;if(Hn(T,t.value)){for(;b<S;)l();t=Dn}else t=Bn;break;default:throw new Error("Unknown node type: "+t.type)}switch(m,g){case null:console.warn("[csstree-match] BREAK after "+Yn+" iterations"),g=qn,k=null;break;case Fn:for(;null!==u;)c();break;default:k=null}return{tokens:e,reason:g,iterations:m,match:k,longestMatch:y}}var Qn=function(e,t,n){var r=Kn(e,t,n||{});if(null===r.match)return r;var i=r.match,a=r.match={syntax:t.syntax||null,match:[]},o=[a];for(i=Un(i).prev;null!==i;){switch(i.type){case _n:a.match.push(a={syntax:i.syntax,match:[]}),o.push(a);break;case jn:o.pop(),a=o[o.length-1];break;default:a.match.push({syntax:i.syntax||null,token:i.token.value,node:i.token.node})}i=i.prev}return r};function Xn(e){function t(e){return null!==e&&("Type"===e.type||"Property"===e.type||"Keyword"===e.type)}var n=null;return null!==this.matched&&function r(i){if(Array.isArray(i.match)){for(var a=0;a<i.match.length;a++)if(r(i.match[a]))return t(i.syntax)&&n.unshift(i.syntax),!0}else if(i.node===e)return n=t(i.syntax)?[i.syntax]:[],!0;return!1}(this.matched),n}function Zn(e,t,n){var r=Xn.call(e,t);return null!==r&&r.some(n)}var $n={getTrace:Xn,isType:function(e,t){return Zn(this,e,(function(e){return"Type"===e.type&&e.name===t}))},isProperty:function(e,t){return Zn(this,e,(function(e){return"Property"===e.type&&e.name===t}))},isKeyword:function(e){return Zn(this,e,(function(e){return"Keyword"===e.type}))}};var Jn={matchFragments:function(e,t,n,r,i){var o=[];return null!==n.matched&&function n(s){if(null!==s.syntax&&s.syntax.type===r&&s.syntax.name===i){var l=function e(t){return"node"in t?t.node:e(t.match[0])}(s),c=function e(t){return"node"in t?t.node:e(t.match[t.match.length-1])}(s);e.syntax.walk(t,(function(e,t,n){if(e===l){var r=new a;do{if(r.appendData(t.data),t.data===c)break;t=t.next}while(null!==t);o.push({parent:n,nodes:r})}}))}Array.isArray(s.match)&&s.match.forEach(n)}(n.matched),o}},er=Object.prototype.hasOwnProperty;function tr(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&e>=0}function nr(e){return Boolean(e)&&tr(e.offset)&&tr(e.line)&&tr(e.column)}function rr(e,t){return function(n,r){if(!n||n.constructor!==Object)return r(n,"Type of node should be an Object");for(var i in n){var o=!0;if(!1!==er.call(n,i)){if("type"===i)n.type!==e&&r(n,"Wrong node type `"+n.type+"`, expected `"+e+"`");else if("loc"===i){if(null===n.loc)continue;if(n.loc&&n.loc.constructor===Object)if("string"!=typeof n.loc.source)i+=".source";else if(nr(n.loc.start)){if(nr(n.loc.end))continue;i+=".end"}else i+=".start";o=!1}else if(t.hasOwnProperty(i)){var s=0;for(o=!1;!o&&s<t[i].length;s++){var l=t[i][s];switch(l){case String:o="string"==typeof n[i];break;case Boolean:o="boolean"==typeof n[i];break;case null:o=null===n[i];break;default:"string"==typeof l?o=n[i]&&n[i].type===l:Array.isArray(l)&&(o=n[i]instanceof a)}}}else r(n,"Unknown field `"+i+"` for "+e+" node type");o||r(n,"Bad value for `"+e+"."+i+"`")}}for(var i in t)er.call(t,i)&&!1===er.call(n,i)&&r(n,"Field `"+e+"."+i+"` is missed")}}function ir(e,t){var n=t.structure,r={type:String,loc:!0},i={type:'"'+e+'"'};for(var a in n)if(!1!==er.call(n,a)){for(var o=[],s=r[a]=Array.isArray(n[a])?n[a].slice():[n[a]],l=0;l<s.length;l++){var c=s[l];if(c===String||c===Boolean)o.push(c.name);else if(null===c)o.push("null");else if("string"==typeof c)o.push("<"+c+">");else{if(!Array.isArray(c))throw new Error("Wrong value `"+c+"` in `"+e+"."+a+"` structure definition");o.push("List")}}i[a]=o.join(" | ")}return{docs:i,check:rr(e,r)}}var ar=ee,or=te,sr=En.buildMatchGraph,lr=Qn,cr=function(e){var t={};if(e.node)for(var n in e.node)if(er.call(e.node,n)){var r=e.node[n];if(!r.structure)throw new Error("Missed `structure` field in `"+n+"` node type definition");t[n]=ir(n,r)}return t},ur=sr("inherit | initial | unset"),hr=sr("inherit | initial | unset | <-ms-legacy-expression>");function pr(e,t,n){var r={};for(var i in e)e[i].syntax&&(r[i]=n?e[i].syntax:$(e[i].syntax,{compact:t}));return r}function dr(e,t,n){return{matched:e,iterations:n,error:t,getTrace:$n.getTrace,isType:$n.isType,isProperty:$n.isProperty,isKeyword:$n.isKeyword}}function mr(e,t,n,r){var i,a=vn(n,e.syntax);return function(e){for(var t=0;t<e.length;t++)if("var("===e[t].value.toLowerCase())return!0;return!1}(a)?dr(null,new Error("Matching for a tree with var() is not supported")):(r&&(i=lr(a,e.valueCommonSyntax,e)),r&&i.match||(i=lr(a,t.match,e)).match?dr(i.match,null,i.iterations):dr(null,new or(i.reason,t.syntax,n,i),i.iterations))}var gr=function(e,t,n){if(this.valueCommonSyntax=ur,this.syntax=t,this.generic=!1,this.properties={},this.types={},this.structure=n||cr(e),e){if(e.types)for(var r in e.types)this.addType_(r,e.types[r]);if(e.generic)for(var r in this.generic=!0,St)this.addType_(r,St[r]);if(e.properties)for(var r in e.properties)this.addProperty_(r,e.properties[r])}};gr.prototype={structure:{},checkStructure:function(e){function t(e,t){r.push({node:e,message:t})}var n=this.structure,r=[];return this.syntax.walk(e,(function(e){n.hasOwnProperty(e.type)?n[e.type].check(e,t):t(e,"Unknown node type `"+e.type+"`")})),!!r.length&&r},createDescriptor:function(e,t,n){var r={type:t,name:n},i={type:t,name:n,syntax:null,match:null};return"function"==typeof e?i.match=sr(e,r):("string"==typeof e?Object.defineProperty(i,"syntax",{get:function(){return Object.defineProperty(i,"syntax",{value:dn(e)}),i.syntax}}):i.syntax=e,Object.defineProperty(i,"match",{get:function(){return Object.defineProperty(i,"match",{value:sr(i.syntax,r)}),i.match}})),i},addProperty_:function(e,t){this.properties[e]=this.createDescriptor(t,"Property",e)},addType_:function(e,t){this.types[e]=this.createDescriptor(t,"Type",e),t===St["-ms-legacy-expression"]&&(this.valueCommonSyntax=hr)},matchDeclaration:function(e){return"Declaration"!==e.type?dr(null,new Error("Not a Declaration node")):this.matchProperty(e.property,e.value)},matchProperty:function(e,t){var n=le.property(e);if(n.custom)return dr(null,new Error("Lexer matching doesn't applicable for custom properties"));var r=n.vendor?this.getProperty(n.name)||this.getProperty(n.basename):this.getProperty(n.name);return r?mr(this,r,t,!0):dr(null,new ar("Unknown property",e))},matchType:function(e,t){var n=this.getType(e);return n?mr(this,n,t,!1):dr(null,new ar("Unknown type",e))},match:function(e,t){return"string"==typeof e||e&&e.type?("string"!=typeof e&&e.match||(e=this.createDescriptor(e,"Type","anonymous")),mr(this,e,t,!1)):dr(null,new ar("Bad syntax"))},findValueFragments:function(e,t,n,r){return Jn.matchFragments(this,t,this.matchProperty(e,t),n,r)},findDeclarationValueFragments:function(e,t,n){return Jn.matchFragments(this,e.value,this.matchDeclaration(e),t,n)},findAllFragments:function(e,t,n){var r=[];return this.syntax.walk(e,{visit:"Declaration",enter:function(e){r.push.apply(r,this.findDeclarationValueFragments(e,t,n))}.bind(this)}),r},getProperty:function(e){return this.properties.hasOwnProperty(e)?this.properties[e]:null},getType:function(e){return this.types.hasOwnProperty(e)?this.types[e]:null},validate:function(){function e(r,i,a,o){if(a.hasOwnProperty(i))return a[i];a[i]=!1,null!==o.syntax&&fn(o.syntax,(function(o){if("Type"===o.type||"Property"===o.type){var s="Type"===o.type?r.types:r.properties,l="Type"===o.type?t:n;s.hasOwnProperty(o.name)&&!e(r,o.name,l,s[o.name])||(a[i]=!0)}}),this)}var t={},n={};for(var r in this.types)e(this,r,t,this.types[r]);for(var r in this.properties)e(this,r,n,this.properties[r]);return t=Object.keys(t).filter((function(e){return t[e]})),n=Object.keys(n).filter((function(e){return n[e]})),t.length||n.length?{types:t,properties:n}:null},dump:function(e,t){return{generic:this.generic,types:pr(this.types,!t,e),properties:pr(this.properties,!t,e)}},toString:function(){return JSON.stringify(this.dump())}};var fr=gr,br={SyntaxError:Ct,parse:dn,generate:$,walk:fn},yr=Le.isBOM,kr=10,vr=12,xr=13;var wr=function(){this.lines=null,this.columns=null,this.linesAndColumnsComputed=!1};wr.prototype={setSource:function(e,t,n,r){this.source=e,this.startOffset=void 0===t?0:t,this.startLine=void 0===n?1:n,this.startColumn=void 0===r?1:r,this.linesAndColumnsComputed=!1},ensureLinesAndColumnsComputed:function(){this.linesAndColumnsComputed||(!function(e,t){for(var n=t.length,r=ue(e.lines,n),i=e.startLine,a=ue(e.columns,n),o=e.startColumn,s=t.length>0?yr(t.charCodeAt(0)):0;s<n;s++){var l=t.charCodeAt(s);r[s]=i,a[s]=o++,l!==kr&&l!==xr&&l!==vr||(l===xr&&s+1<n&&t.charCodeAt(s+1)===kr&&(r[++s]=i,a[s]=o),i++,o=1)}r[s]=i,a[s]=o,e.lines=r,e.columns=a}(this,this.source),this.linesAndColumnsComputed=!0)},getLocation:function(e,t){return this.ensureLinesAndColumnsComputed(),{source:t,offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]}},getLocationRange:function(e,t,n){return this.ensureLinesAndColumnsComputed(),{source:n,start:{offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]},end:{offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]}}}};var Sr=wr,Cr=Le.TYPE,zr=Cr.WhiteSpace,Ar=Cr.Comment,Pr=function(e){var t=this.createList(),n=null,r={recognizer:e,space:null,ignoreWS:!1,ignoreWSAfter:!1};for(this.scanner.skipSC();!this.scanner.eof;){switch(this.scanner.tokenType){case Ar:this.scanner.next();continue;case zr:r.ignoreWS?this.scanner.next():r.space=this.WhiteSpace();continue}if(void 0===(n=e.getNode.call(this,r)))break;null!==r.space&&(t.push(r.space),r.space=null),t.push(n),r.ignoreWSAfter?(r.ignoreWSAfter=!1,r.ignoreWS=!0):r.ignoreWS=!1}return t},Tr=W.findWhiteSpaceStart,Lr=function(){},Er=m.TYPE,Or=m.NAME,Dr=Er.WhiteSpace,Br=Er.Ident,Ir=Er.Function,Nr=Er.Url,Mr=Er.Hash,Rr=Er.Percentage,_r=Er.Number;function jr(e){return function(){return this[e]()}}var Fr=function(e){var t={scanner:new Q,locationMap:new Sr,filename:"<unknown>",needPositions:!1,onParseError:Lr,onParseErrorThrow:!1,parseAtrulePrelude:!0,parseRulePrelude:!0,parseValue:!0,parseCustomProperty:!1,readSequence:Pr,createList:function(){return new a},createSingleNodeList:function(e){return(new a).appendData(e)},getFirstListNode:function(e){return e&&e.first()},getLastListNode:function(e){return e.last()},parseWithFallback:function(e,t){var n=this.scanner.tokenIndex;try{return e.call(this)}catch(e){if(this.onParseErrorThrow)throw e;var r=t.call(this,n);return this.onParseErrorThrow=!0,this.onParseError(e,r),this.onParseErrorThrow=!1,r}},lookupNonWSType:function(e){do{var t=this.scanner.lookupType(e++);if(t!==Dr)return t}while(0!==t);return 0},eat:function(e){if(this.scanner.tokenType!==e){var t=this.scanner.tokenStart,n=Or[e]+" is expected";switch(e){case Br:this.scanner.tokenType===Ir||this.scanner.tokenType===Nr?(t=this.scanner.tokenEnd-1,n="Identifier is expected but function found"):n="Identifier is expected";break;case Mr:this.scanner.isDelim(35)&&(this.scanner.next(),t++,n="Name is expected");break;case Rr:this.scanner.tokenType===_r&&(t=this.scanner.tokenEnd,n="Percent sign is expected");break;default:this.scanner.source.charCodeAt(this.scanner.tokenStart)===e&&(t+=1)}this.error(n,t)}this.scanner.next()},consume:function(e){var t=this.scanner.getTokenValue();return this.eat(e),t},consumeFunctionName:function(){var e=this.scanner.source.substring(this.scanner.tokenStart,this.scanner.tokenEnd-1);return this.eat(Ir),e},getLocation:function(e,t){return this.needPositions?this.locationMap.getLocationRange(e,t,this.filename):null},getLocationFromList:function(e){if(this.needPositions){var t=this.getFirstListNode(e),n=this.getLastListNode(e);return this.locationMap.getLocationRange(null!==t?t.loc.start.offset-this.locationMap.startOffset:this.scanner.tokenStart,null!==n?n.loc.end.offset-this.locationMap.startOffset:this.scanner.tokenStart,this.filename)}return null},error:function(e,t){var n=void 0!==t&&t<this.scanner.source.length?this.locationMap.getLocation(t):this.scanner.eof?this.locationMap.getLocation(Tr(this.scanner.source,this.scanner.source.length-1)):this.locationMap.getLocation(this.scanner.tokenStart);throw new h(e||"Unexpected input",this.scanner.source,n.offset,n.line,n.column)}};for(var n in e=function(e){var t={context:{},scope:{},atrule:{},pseudo:{}};if(e.parseContext)for(var n in e.parseContext)switch(typeof e.parseContext[n]){case"function":t.context[n]=e.parseContext[n];break;case"string":t.context[n]=jr(e.parseContext[n])}if(e.scope)for(var n in e.scope)t.scope[n]=e.scope[n];if(e.atrule)for(var n in e.atrule){var r=e.atrule[n];r.parse&&(t.atrule[n]=r.parse)}if(e.pseudo)for(var n in e.pseudo){var i=e.pseudo[n];i.parse&&(t.pseudo[n]=i.parse)}if(e.node)for(var n in e.node)t[n]=e.node[n].parse;return t}(e||{}))t[n]=e[n];return function(e,n){var r,i=(n=n||{}).context||"default";if(Le(e,t.scanner),t.locationMap.setSource(e,n.offset,n.line,n.column),t.filename=n.filename||"<unknown>",t.needPositions=Boolean(n.positions),t.onParseError="function"==typeof n.onParseError?n.onParseError:Lr,t.onParseErrorThrow=!1,t.parseAtrulePrelude=!("parseAtrulePrelude"in n)||Boolean(n.parseAtrulePrelude),t.parseRulePrelude=!("parseRulePrelude"in n)||Boolean(n.parseRulePrelude),t.parseValue=!("parseValue"in n)||Boolean(n.parseValue),t.parseCustomProperty="parseCustomProperty"in n&&Boolean(n.parseCustomProperty),!t.context.hasOwnProperty(i))throw new Error("Unknown context `"+i+"`");return r=t.context[i].call(t,n),t.scanner.eof||t.error(),r}},Wr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),qr=function(e){if(0<=e&&e<Wr.length)return Wr[e];throw new TypeError("Must be between 0 and 63: "+e)};var Yr=function(e){var t,n="",r=function(e){return e<0?1+(-e<<1):0+(e<<1)}(e);do{t=31&r,(r>>>=5)>0&&(t|=32),n+=qr(t)}while(r>0);return n};var Ur=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e,t){t.getArg=function(e,t,n){if(t in e)return e[t];if(3===arguments.length)return n;throw new Error('"'+t+'" is a required argument.')};var n=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,r=/^data:.+\,.+$/;function i(e){var t=e.match(n);return t?{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}:null}function a(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}function o(e){var n=e,r=i(e);if(r){if(!r.path)return e;n=r.path}for(var o,s=t.isAbsolute(n),l=n.split(/\/+/),c=0,u=l.length-1;u>=0;u--)"."===(o=l[u])?l.splice(u,1):".."===o?c++:c>0&&(""===o?(l.splice(u+1,c),c=0):(l.splice(u,2),c--));return""===(n=l.join("/"))&&(n=s?"/":"."),r?(r.path=n,a(r)):n}function s(e,t){""===e&&(e="."),""===t&&(t=".");var n=i(t),s=i(e);if(s&&(e=s.path||"/"),n&&!n.scheme)return s&&(n.scheme=s.scheme),a(n);if(n||t.match(r))return t;if(s&&!s.host&&!s.path)return s.host=t,a(s);var l="/"===t.charAt(0)?t:o(e.replace(/\/+$/,"")+"/"+t);return s?(s.path=l,a(s)):l}t.urlParse=i,t.urlGenerate=a,t.normalize=o,t.join=s,t.isAbsolute=function(e){return"/"===e.charAt(0)||n.test(e)},t.relative=function(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var n=0;0!==t.indexOf(e+"/");){var r=e.lastIndexOf("/");if(r<0)return t;if((e=e.slice(0,r)).match(/^([^\/]+:\/)?\/*$/))return t;++n}return Array(n+1).join("../")+t.substr(e.length+1)};var l=!("__proto__"in Object.create(null));function c(e){return e}function u(e){if(!e)return!1;var t=e.length;if(t<9)return!1;if(95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var n=t-10;n>=0;n--)if(36!==e.charCodeAt(n))return!1;return!0}function h(e,t){return e===t?0:null===e?1:null===t?-1:e>t?1:-1}t.toSetString=l?c:function(e){return u(e)?"$"+e:e},t.fromSetString=l?c:function(e){return u(e)?e.slice(1):e},t.compareByOriginalPositions=function(e,t,n){var r=h(e.source,t.source);return 0!==r?r:0!==(r=e.originalLine-t.originalLine)?r:0!==(r=e.originalColumn-t.originalColumn)||n?r:0!==(r=e.generatedColumn-t.generatedColumn)?r:0!==(r=e.generatedLine-t.generatedLine)?r:h(e.name,t.name)},t.compareByGeneratedPositionsDeflated=function(e,t,n){var r=e.generatedLine-t.generatedLine;return 0!==r?r:0!==(r=e.generatedColumn-t.generatedColumn)||n?r:0!==(r=h(e.source,t.source))?r:0!==(r=e.originalLine-t.originalLine)?r:0!==(r=e.originalColumn-t.originalColumn)?r:h(e.name,t.name)},t.compareByGeneratedPositionsInflated=function(e,t){var n=e.generatedLine-t.generatedLine;return 0!==n?n:0!==(n=e.generatedColumn-t.generatedColumn)?n:0!==(n=h(e.source,t.source))?n:0!==(n=e.originalLine-t.originalLine)?n:0!==(n=e.originalColumn-t.originalColumn)?n:h(e.name,t.name)},t.parseSourceMapInput=function(e){return JSON.parse(e.replace(/^\)]}'[^\n]*\n/,""))},t.computeSourceURL=function(e,t,n){if(t=t||"",e&&("/"!==e[e.length-1]&&"/"!==t[0]&&(e+="/"),t=e+t),n){var r=i(n);if(!r)throw new Error("sourceMapURL could not be parsed");if(r.path){var l=r.path.lastIndexOf("/");l>=0&&(r.path=r.path.substring(0,l+1))}t=s(a(r),t)}return o(t)}})),Hr=(Ur.getArg,Ur.urlParse,Ur.urlGenerate,Ur.normalize,Ur.join,Ur.isAbsolute,Ur.relative,Ur.toSetString,Ur.fromSetString,Ur.compareByOriginalPositions,Ur.compareByGeneratedPositionsDeflated,Ur.compareByGeneratedPositionsInflated,Ur.parseSourceMapInput,Ur.computeSourceURL,Object.prototype.hasOwnProperty),Vr="undefined"!=typeof Map;function Gr(){this._array=[],this._set=Vr?new Map:Object.create(null)}Gr.fromArray=function(e,t){for(var n=new Gr,r=0,i=e.length;r<i;r++)n.add(e[r],t);return n},Gr.prototype.size=function(){return Vr?this._set.size:Object.getOwnPropertyNames(this._set).length},Gr.prototype.add=function(e,t){var n=Vr?e:Ur.toSetString(e),r=Vr?this.has(e):Hr.call(this._set,n),i=this._array.length;r&&!t||this._array.push(e),r||(Vr?this._set.set(e,i):this._set[n]=i)},Gr.prototype.has=function(e){if(Vr)return this._set.has(e);var t=Ur.toSetString(e);return Hr.call(this._set,t)},Gr.prototype.indexOf=function(e){if(Vr){var t=this._set.get(e);if(t>=0)return t}else{var n=Ur.toSetString(e);if(Hr.call(this._set,n))return this._set[n]}throw new Error('"'+e+'" is not in the set.')},Gr.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},Gr.prototype.toArray=function(){return this._array.slice()};var Kr={ArraySet:Gr};function Qr(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}Qr.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},Qr.prototype.add=function(e){var t,n,r,i,a,o;t=this._last,n=e,r=t.generatedLine,i=n.generatedLine,a=t.generatedColumn,o=n.generatedColumn,i>r||i==r&&o>=a||Ur.compareByGeneratedPositionsInflated(t,n)<=0?(this._last=e,this._array.push(e)):(this._sorted=!1,this._array.push(e))},Qr.prototype.toArray=function(){return this._sorted||(this._array.sort(Ur.compareByGeneratedPositionsInflated),this._sorted=!0),this._array};var Xr=Kr.ArraySet,Zr={MappingList:Qr}.MappingList;function $r(e){e||(e={}),this._file=Ur.getArg(e,"file",null),this._sourceRoot=Ur.getArg(e,"sourceRoot",null),this._skipValidation=Ur.getArg(e,"skipValidation",!1),this._sources=new Xr,this._names=new Xr,this._mappings=new Zr,this._sourcesContents=null}$r.prototype._version=3,$r.fromSourceMap=function(e){var t=e.sourceRoot,n=new $r({file:e.file,sourceRoot:t});return e.eachMapping((function(e){var r={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(r.source=e.source,null!=t&&(r.source=Ur.relative(t,r.source)),r.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(r.name=e.name)),n.addMapping(r)})),e.sources.forEach((function(r){var i=r;null!==t&&(i=Ur.relative(t,r)),n._sources.has(i)||n._sources.add(i);var a=e.sourceContentFor(r);null!=a&&n.setSourceContent(r,a)})),n},$r.prototype.addMapping=function(e){var t=Ur.getArg(e,"generated"),n=Ur.getArg(e,"original",null),r=Ur.getArg(e,"source",null),i=Ur.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,n,r,i),null!=r&&(r=String(r),this._sources.has(r)||this._sources.add(r)),null!=i&&(i=String(i),this._names.has(i)||this._names.add(i)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=n&&n.line,originalColumn:null!=n&&n.column,source:r,name:i})},$r.prototype.setSourceContent=function(e,t){var n=e;null!=this._sourceRoot&&(n=Ur.relative(this._sourceRoot,n)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[Ur.toSetString(n)]=t):this._sourcesContents&&(delete this._sourcesContents[Ur.toSetString(n)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},$r.prototype.applySourceMap=function(e,t,n){var r=t;if(null==t){if(null==e.file)throw new Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');r=e.file}var i=this._sourceRoot;null!=i&&(r=Ur.relative(i,r));var a=new Xr,o=new Xr;this._mappings.unsortedForEach((function(t){if(t.source===r&&null!=t.originalLine){var s=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=s.source&&(t.source=s.source,null!=n&&(t.source=Ur.join(n,t.source)),null!=i&&(t.source=Ur.relative(i,t.source)),t.originalLine=s.line,t.originalColumn=s.column,null!=s.name&&(t.name=s.name))}var l=t.source;null==l||a.has(l)||a.add(l);var c=t.name;null==c||o.has(c)||o.add(c)}),this),this._sources=a,this._names=o,e.sources.forEach((function(t){var r=e.sourceContentFor(t);null!=r&&(null!=n&&(t=Ur.join(n,t)),null!=i&&(t=Ur.relative(i,t)),this.setSourceContent(t,r))}),this)},$r.prototype._validateMapping=function(e,t,n,r){if(t&&"number"!=typeof t.line&&"number"!=typeof t.column)throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0)||t||n||r)&&!(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&n))throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:n,original:t,name:r}))},$r.prototype._serializeMappings=function(){for(var e,t,n,r,i=0,a=1,o=0,s=0,l=0,c=0,u="",h=this._mappings.toArray(),p=0,d=h.length;p<d;p++){if(e="",(t=h[p]).generatedLine!==a)for(i=0;t.generatedLine!==a;)e+=";",a++;else if(p>0){if(!Ur.compareByGeneratedPositionsInflated(t,h[p-1]))continue;e+=","}e+=Yr(t.generatedColumn-i),i=t.generatedColumn,null!=t.source&&(r=this._sources.indexOf(t.source),e+=Yr(r-c),c=r,e+=Yr(t.originalLine-1-s),s=t.originalLine-1,e+=Yr(t.originalColumn-o),o=t.originalColumn,null!=t.name&&(n=this._names.indexOf(t.name),e+=Yr(n-l),l=n)),u+=e}return u},$r.prototype._generateSourcesContent=function(e,t){return e.map((function(e){if(!this._sourcesContents)return null;null!=t&&(e=Ur.relative(t,e));var n=Ur.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,n)?this._sourcesContents[n]:null}),this)},$r.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},$r.prototype.toString=function(){return JSON.stringify(this.toJSON())};var Jr={SourceMapGenerator:$r}.SourceMapGenerator,ei={Atrule:!0,Selector:!0,Declaration:!0},ti=Object.prototype.hasOwnProperty;function ni(e,t){var n=e.children,r=null;"function"!=typeof t?n.forEach(this.node,this):n.forEach((function(e){null!==r&&t.call(this,r),this.node(e),r=e}),this)}var ri=function(e){function t(e){if(!ti.call(n,e.type))throw new Error("Unknown node type: "+e.type);n[e.type].call(this,e)}var n={};if(e.node)for(var r in e.node)n[r]=e.node[r].generate;return function(e,n){var r="",i={children:ni,node:t,chunk:function(e){r+=e},result:function(){return r}};return n&&("function"==typeof n.decorator&&(i=n.decorator(i)),n.sourceMap&&(i=function(e){var t=new Jr,n=1,r=0,i={line:1,column:0},a={line:0,column:0},o=!1,s={line:1,column:0},l={generated:s},c=e.node;e.node=function(e){if(e.loc&&e.loc.start&&ei.hasOwnProperty(e.type)){var u=e.loc.start.line,h=e.loc.start.column-1;a.line===u&&a.column===h||(a.line=u,a.column=h,i.line=n,i.column=r,o&&(o=!1,i.line===s.line&&i.column===s.column||t.addMapping(l)),o=!0,t.addMapping({source:e.loc.source,original:a,generated:i}))}c.call(this,e),o&&ei.hasOwnProperty(e.type)&&(s.line=n,s.column=r)};var u=e.chunk;e.chunk=function(e){for(var t=0;t<e.length;t++)10===e.charCodeAt(t)?(n++,r=0):r++;u(e)};var h=e.result;return e.result=function(){return o&&t.addMapping(l),{css:h(),map:t}},e}(i))),i.node(e),i.result()}},ii=function(e){return{fromPlainObject:function(t){return e(t,{enter:function(e){e.children&&e.children instanceof a==!1&&(e.children=(new a).fromArray(e.children))}}),t},toPlainObject:function(t){return e(t,{leave:function(e){e.children&&e.children instanceof a&&(e.children=e.children.toArray())}}),t}}},ai=Object.prototype.hasOwnProperty,oi=function(){};function si(e){return"function"==typeof e?e:oi}function li(e,t){return function(n,r,i){n.type===t&&e.call(this,n,r,i)}}function ci(e,t){var n=t.structure,r=[];for(var i in n)if(!1!==ai.call(n,i)){var a=n[i],o={name:i,type:!1,nullable:!1};Array.isArray(n[i])||(a=[n[i]]);for(var s=0;s<a.length;s++){var l=a[s];null===l?o.nullable=!0:"string"==typeof l?o.type="node":Array.isArray(l)&&(o.type="list")}o.type&&r.push(o)}return r.length?{context:t.walkContext,fields:r}:null}function ui(e,t){var n=e.fields.slice(),r=e.context,i="string"==typeof r;return t&&n.reverse(),function(e,a,o){var s;i&&(s=a[r],a[r]=e);for(var l=0;l<n.length;l++){var c=n[l],u=e[c.name];c.nullable&&!u||("list"===c.type?t?u.forEachRight(o):u.forEach(o):o(u))}i&&(a[r]=s)}}function hi(e){return{Atrule:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block},Rule:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block},Declaration:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block}}}var pi=function(e){var t=function(e){var t={};for(var n in e.node)if(ai.call(e.node,n)){var r=e.node[n];if(!r.structure)throw new Error("Missed `structure` field in `"+n+"` node type definition");t[n]=ci(0,r)}return t}(e),n={},r={};for(var i in t)ai.call(t,i)&&null!==t[i]&&(n[i]=ui(t[i],!1),r[i]=ui(t[i],!0));var a=hi(n),o=hi(r),s=function(e,i){var s=oi,l=oi,c=n,u={root:e,stylesheet:null,atrule:null,atrulePrelude:null,rule:null,selector:null,block:null,declaration:null,function:null};if("function"==typeof i)s=i;else if(i&&(s=si(i.enter),l=si(i.leave),i.reverse&&(c=r),i.visit)){if(a.hasOwnProperty(i.visit))c=i.reverse?o[i.visit]:a[i.visit];else if(!t.hasOwnProperty(i.visit))throw new Error("Bad value `"+i.visit+"` for `visit` option (should be: "+Object.keys(t).join(", ")+")");s=li(s,i.visit),l=li(l,i.visit)}if(s===oi&&l===oi)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");if(i.reverse){var h=s;s=l,l=h}!function e(t,n,r){s.call(u,t,n,r),c.hasOwnProperty(t.type)&&c[t.type](t,u,e),l.call(u,t,n,r)}(e)};return s.find=function(e,t){var n=null;return s(e,(function(e,r,i){null===n&&t.call(this,e,r,i)&&(n=e)})),n},s.findLast=function(e,t){var n=null;return s(e,{reverse:!0,enter:function(e,r,i){null===n&&t.call(this,e,r,i)&&(n=e)}}),n},s.findAll=function(e,t){var n=[];return s(e,(function(e,r,i){t.call(this,e,r,i)&&n.push(e)})),n},s},di=function e(t){var n={};for(var r in t){var i=t[r];i&&(Array.isArray(i)||i instanceof a?i=i.map(e):i.constructor===Object&&(i=e(i))),n[r]=i}return n},mi=Object.prototype.hasOwnProperty,gi={generic:!0,types:{},properties:{},parseContext:{},scope:{},atrule:["parse"],pseudo:["parse"],node:["name","structure","parse","generate","walkContext"]};function fi(e){return e&&e.constructor===Object}function bi(e){if(fi(e)){var t={};for(var n in e)mi.call(e,n)&&(t[n]=e[n]);return t}return e}function yi(e,t){for(var n in t)mi.call(t,n)&&(fi(e[n])?yi(e[n],bi(t[n])):e[n]=bi(t[n]))}var ki=function(e,t){return function e(t,n,r){for(var i in r)if(!1!==mi.call(r,i))if(!0===r[i])i in n&&mi.call(n,i)&&(t[i]=bi(n[i]));else if(r[i]){if(fi(r[i]))yi(a={},t[i]),yi(a,n[i]),t[i]=a;else if(Array.isArray(r[i])){var a={},o=r[i].reduce((function(e,t){return e[t]=!0,e}),{});for(var s in t[i])mi.call(t[i],s)&&(a[s]={},t[i]&&t[i][s]&&e(a[s],t[i][s],o));for(var s in n[i])mi.call(n[i],s)&&(a[s]||(a[s]={}),n[i]&&n[i][s]&&e(a[s],n[i][s],o));t[i]=a}}return t}(e,t,gi)};function vi(e,t){for(var n in t)e[n]=t[n];return e}var xi=function(e){return function e(t){var n=Fr(t),r=pi(t),i=ri(t),o=ii(r),s={List:a,SyntaxError:h,TokenStream:Q,Lexer:fr,vendorPrefix:le.vendorPrefix,keyword:le.keyword,property:le.property,isCustomProperty:le.isCustomProperty,definitionSyntax:br,lexer:null,createLexer:function(e){return new fr(e,s,s.lexer.structure)},tokenize:Le,parse:n,walk:r,generate:i,find:r.find,findLast:r.findLast,findAll:r.findAll,clone:di,fromPlainObject:o.fromPlainObject,toPlainObject:o.toPlainObject,createSyntax:function(t){return e(ki({},t))},fork:function(n){var r=ki({},t);return e("function"==typeof n?n(r,vi):ki(r,n))}};return s.lexer=new fr({generic:!0,types:t.types,properties:t.properties,node:t.node},s),s}(ki({},e))},wi={"absolute-size":"xx-small|x-small|small|medium|large|x-large|xx-large","alpha-value":"<number>|<percentage>","angle-percentage":"<angle>|<percentage>","angular-color-hint":"<angle-percentage>","angular-color-stop":"<color>&&<color-stop-angle>?","angular-color-stop-list":"[<angular-color-stop> [, <angular-color-hint>]?]# , <angular-color-stop>","animateable-feature":"scroll-position|contents|<custom-ident>",attachment:"scroll|fixed|local","attr()":"attr( <attr-name> <type-or-unit>? [, <attr-fallback>]? )","attr-matcher":"['~'|'|'|'^'|'$'|'*']? '='","attr-modifier":"i|s","attribute-selector":"'[' <wq-name> ']'|'[' <wq-name> <attr-matcher> [<string-token>|<ident-token>] <attr-modifier>? ']'","auto-repeat":"repeat( [auto-fill|auto-fit] , [<line-names>? <fixed-size>]+ <line-names>? )","auto-track-list":"[<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>? <auto-repeat> [<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>?","baseline-position":"[first|last]? baseline","basic-shape":"<inset()>|<circle()>|<ellipse()>|<polygon()>","bg-image":"none|<image>","bg-layer":"<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","bg-position":"[[left|center|right|top|bottom|<length-percentage>]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]|[center|[left|right] <length-percentage>?]&&[center|[top|bottom] <length-percentage>?]]","bg-size":"[<length-percentage>|auto]{1,2}|cover|contain","blur()":"blur( <length> )","blend-mode":"normal|multiply|screen|overlay|darken|lighten|color-dodge|color-burn|hard-light|soft-light|difference|exclusion|hue|saturation|color|luminosity",box:"border-box|padding-box|content-box","brightness()":"brightness( <number-percentage> )","calc()":"calc( <calc-sum> )","calc-sum":"<calc-product> [['+'|'-'] <calc-product>]*","calc-product":"<calc-value> ['*' <calc-value>|'/' <number>]*","calc-value":"<number>|<dimension>|<percentage>|( <calc-sum> )","cf-final-image":"<image>|<color>","cf-mixing-image":"<percentage>?&&<image>","circle()":"circle( [<shape-radius>]? [at <position>]? )","clamp()":"clamp( <calc-sum>#{3} )","class-selector":"'.' <ident-token>","clip-source":"<url>",color:"<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hex-color>|<named-color>|currentcolor|<deprecated-system-color>","color-stop":"<color-stop-length>|<color-stop-angle>","color-stop-angle":"<angle-percentage>{1,2}","color-stop-length":"<length-percentage>{1,2}","color-stop-list":"[<linear-color-stop> [, <linear-color-hint>]?]# , <linear-color-stop>",combinator:"'>'|'+'|'~'|['||']","common-lig-values":"[common-ligatures|no-common-ligatures]",compat:"searchfield|textarea|push-button|button-bevel|slider-horizontal|checkbox|radio|square-button|menulist|menulist-button|listbox|meter|progress-bar","composite-style":"clear|copy|source-over|source-in|source-out|source-atop|destination-over|destination-in|destination-out|destination-atop|xor","compositing-operator":"add|subtract|intersect|exclude","compound-selector":"[<type-selector>? <subclass-selector>* [<pseudo-element-selector> <pseudo-class-selector>*]*]!","compound-selector-list":"<compound-selector>#","complex-selector":"<compound-selector> [<combinator>? <compound-selector>]*","complex-selector-list":"<complex-selector>#","conic-gradient()":"conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )","contextual-alt-values":"[contextual|no-contextual]","content-distribution":"space-between|space-around|space-evenly|stretch","content-list":"[<string>|contents|<url>|<quote>|<attr()>|counter( <ident> , <'list-style-type'>? )]+","content-position":"center|start|end|flex-start|flex-end","content-replacement":"<image>","contrast()":"contrast( [<number-percentage>] )","counter()":"counter( <custom-ident> , [<counter-style>|none]? )","counter-style":"<counter-style-name>|symbols( )","counter-style-name":"<custom-ident>","counters()":"counters( <custom-ident> , <string> , [<counter-style>|none]? )","cross-fade()":"cross-fade( <cf-mixing-image> , <cf-final-image>? )","cubic-bezier-timing-function":"ease|ease-in|ease-out|ease-in-out|cubic-bezier( <number> , <number> , <number> , <number> )","deprecated-system-color":"ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText","discretionary-lig-values":"[discretionary-ligatures|no-discretionary-ligatures]","display-box":"contents|none","display-inside":"flow|flow-root|table|flex|grid|ruby","display-internal":"table-row-group|table-header-group|table-footer-group|table-row|table-cell|table-column-group|table-column|table-caption|ruby-base|ruby-text|ruby-base-container|ruby-text-container","display-legacy":"inline-block|inline-list-item|inline-table|inline-flex|inline-grid","display-listitem":"<display-outside>?&&[flow|flow-root]?&&list-item","display-outside":"block|inline|run-in","drop-shadow()":"drop-shadow( <length>{2,3} <color>? )","east-asian-variant-values":"[jis78|jis83|jis90|jis04|simplified|traditional]","east-asian-width-values":"[full-width|proportional-width]","element()":"element( <id-selector> )","ellipse()":"ellipse( [<shape-radius>{2}]? [at <position>]? )","ending-shape":"circle|ellipse","env()":"env( <custom-ident> , <declaration-value>? )","explicit-track-list":"[<line-names>? <track-size>]+ <line-names>?","family-name":"<string>|<custom-ident>+","feature-tag-value":"<string> [<integer>|on|off]?","feature-type":"@stylistic|@historical-forms|@styleset|@character-variant|@swash|@ornaments|@annotation","feature-value-block":"<feature-type> '{' <feature-value-declaration-list> '}'","feature-value-block-list":"<feature-value-block>+","feature-value-declaration":"<custom-ident> : <integer>+ ;","feature-value-declaration-list":"<feature-value-declaration>","feature-value-name":"<custom-ident>","fill-rule":"nonzero|evenodd","filter-function":"<blur()>|<brightness()>|<contrast()>|<drop-shadow()>|<grayscale()>|<hue-rotate()>|<invert()>|<opacity()>|<saturate()>|<sepia()>","filter-function-list":"[<filter-function>|<url>]+","final-bg-layer":"<'background-color'>||<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","fit-content()":"fit-content( [<length>|<percentage>] )","fixed-breadth":"<length-percentage>","fixed-repeat":"repeat( [<positive-integer>] , [<line-names>? <fixed-size>]+ <line-names>? )","fixed-size":"<fixed-breadth>|minmax( <fixed-breadth> , <track-breadth> )|minmax( <inflexible-breadth> , <fixed-breadth> )","font-stretch-absolute":"normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded|<percentage>","font-variant-css21":"[normal|small-caps]","font-weight-absolute":"normal|bold|<number>","frequency-percentage":"<frequency>|<percentage>","general-enclosed":"[<function-token> <any-value> )]|( <ident> <any-value> )","generic-family":"serif|sans-serif|cursive|fantasy|monospace|-apple-system","generic-name":"serif|sans-serif|cursive|fantasy|monospace","geometry-box":"<shape-box>|fill-box|stroke-box|view-box",gradient:"<linear-gradient()>|<repeating-linear-gradient()>|<radial-gradient()>|<repeating-radial-gradient()>|<conic-gradient()>|<-legacy-gradient>","grayscale()":"grayscale( <number-percentage> )","grid-line":"auto|<custom-ident>|[<integer>&&<custom-ident>?]|[span&&[<integer>||<custom-ident>]]","historical-lig-values":"[historical-ligatures|no-historical-ligatures]","hsl()":"hsl( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsl( <hue> , <percentage> , <percentage> , <alpha-value>? )","hsla()":"hsla( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsla( <hue> , <percentage> , <percentage> , <alpha-value>? )",hue:"<number>|<angle>","hue-rotate()":"hue-rotate( <angle> )",image:"<url>|<image()>|<image-set()>|<element()>|<cross-fade()>|<gradient>","image()":"image( <image-tags>? [<image-src>? , <color>?]! )","image-set()":"image-set( <image-set-option># )","image-set-option":"[<image>|<string>] <resolution>","image-src":"<url>|<string>","image-tags":"ltr|rtl","inflexible-breadth":"<length>|<percentage>|min-content|max-content|auto","inset()":"inset( <length-percentage>{1,4} [round <'border-radius'>]? )","invert()":"invert( <number-percentage> )","keyframes-name":"<custom-ident>|<string>","keyframe-block":"<keyframe-selector># { <declaration-list> }","keyframe-block-list":"<keyframe-block>+","keyframe-selector":"from|to|<percentage>","leader()":"leader( <leader-type> )","leader-type":"dotted|solid|space|<string>","length-percentage":"<length>|<percentage>","line-names":"'[' <custom-ident>* ']'","line-name-list":"[<line-names>|<name-repeat>]+","line-style":"none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset","line-width":"<length>|thin|medium|thick","linear-color-hint":"<length-percentage>","linear-color-stop":"<color> <color-stop-length>?","linear-gradient()":"linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","mask-layer":"<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||<geometry-box>||[<geometry-box>|no-clip]||<compositing-operator>||<masking-mode>","mask-position":"[<length-percentage>|left|center|right] [<length-percentage>|top|center|bottom]?","mask-reference":"none|<image>|<mask-source>","mask-source":"<url>","masking-mode":"alpha|luminance|match-source","matrix()":"matrix( <number>#{6} )","matrix3d()":"matrix3d( <number>#{16} )","max()":"max( <calc-sum># )","media-and":"<media-in-parens> [and <media-in-parens>]+","media-condition":"<media-not>|<media-and>|<media-or>|<media-in-parens>","media-condition-without-or":"<media-not>|<media-and>|<media-in-parens>","media-feature":"( [<mf-plain>|<mf-boolean>|<mf-range>] )","media-in-parens":"( <media-condition> )|<media-feature>|<general-enclosed>","media-not":"not <media-in-parens>","media-or":"<media-in-parens> [or <media-in-parens>]+","media-query":"<media-condition>|[not|only]? <media-type> [and <media-condition-without-or>]?","media-query-list":"<media-query>#","media-type":"<ident>","mf-boolean":"<mf-name>","mf-name":"<ident>","mf-plain":"<mf-name> : <mf-value>","mf-range":"<mf-name> ['<'|'>']? '='? <mf-value>|<mf-value> ['<'|'>']? '='? <mf-name>|<mf-value> '<' '='? <mf-name> '<' '='? <mf-value>|<mf-value> '>' '='? <mf-name> '>' '='? <mf-value>","mf-value":"<number>|<dimension>|<ident>|<ratio>","min()":"min( <calc-sum># )","minmax()":"minmax( [<length>|<percentage>|<flex>|min-content|max-content|auto] , [<length>|<percentage>|<flex>|min-content|max-content|auto] )","named-color":"transparent|aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|rebeccapurple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen|<-non-standard-color>","namespace-prefix":"<ident>","ns-prefix":"[<ident-token>|'*']? '|'","number-percentage":"<number>|<percentage>","numeric-figure-values":"[lining-nums|oldstyle-nums]","numeric-fraction-values":"[diagonal-fractions|stacked-fractions]","numeric-spacing-values":"[proportional-nums|tabular-nums]",nth:"<an-plus-b>|even|odd","opacity()":"opacity( [<number-percentage>] )","overflow-position":"unsafe|safe","outline-radius":"<length>|<percentage>","page-body":"<declaration>? [; <page-body>]?|<page-margin-box> <page-body>","page-margin-box":"<page-margin-box-type> '{' <declaration-list> '}'","page-margin-box-type":"@top-left-corner|@top-left|@top-center|@top-right|@top-right-corner|@bottom-left-corner|@bottom-left|@bottom-center|@bottom-right|@bottom-right-corner|@left-top|@left-middle|@left-bottom|@right-top|@right-middle|@right-bottom","page-selector-list":"[<page-selector>#]?","page-selector":"<pseudo-page>+|<ident> <pseudo-page>*","perspective()":"perspective( <length> )","polygon()":"polygon( <fill-rule>? , [<length-percentage> <length-percentage>]# )",position:"[[left|center|right]||[top|center|bottom]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]?|[[left|right] <length-percentage>]&&[[top|bottom] <length-percentage>]]","pseudo-class-selector":"':' <ident-token>|':' <function-token> <any-value> ')'","pseudo-element-selector":"':' <pseudo-class-selector>","pseudo-page":": [left|right|first|blank]",quote:"open-quote|close-quote|no-open-quote|no-close-quote","radial-gradient()":"radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","relative-selector":"<combinator>? <complex-selector>","relative-selector-list":"<relative-selector>#","relative-size":"larger|smaller","repeat-style":"repeat-x|repeat-y|[repeat|space|round|no-repeat]{1,2}","repeating-linear-gradient()":"repeating-linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","repeating-radial-gradient()":"repeating-radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","rgb()":"rgb( <percentage>{3} [/ <alpha-value>]? )|rgb( <number>{3} [/ <alpha-value>]? )|rgb( <percentage>#{3} , <alpha-value>? )|rgb( <number>#{3} , <alpha-value>? )","rgba()":"rgba( <percentage>{3} [/ <alpha-value>]? )|rgba( <number>{3} [/ <alpha-value>]? )|rgba( <percentage>#{3} , <alpha-value>? )|rgba( <number>#{3} , <alpha-value>? )","rotate()":"rotate( [<angle>|<zero>] )","rotate3d()":"rotate3d( <number> , <number> , <number> , [<angle>|<zero>] )","rotateX()":"rotateX( [<angle>|<zero>] )","rotateY()":"rotateY( [<angle>|<zero>] )","rotateZ()":"rotateZ( [<angle>|<zero>] )","saturate()":"saturate( <number-percentage> )","scale()":"scale( <number> , <number>? )","scale3d()":"scale3d( <number> , <number> , <number> )","scaleX()":"scaleX( <number> )","scaleY()":"scaleY( <number> )","scaleZ()":"scaleZ( <number> )","self-position":"center|start|end|self-start|self-end|flex-start|flex-end","shape-radius":"<length-percentage>|closest-side|farthest-side","skew()":"skew( [<angle>|<zero>] , [<angle>|<zero>]? )","skewX()":"skewX( [<angle>|<zero>] )","skewY()":"skewY( [<angle>|<zero>] )","sepia()":"sepia( <number-percentage> )",shadow:"inset?&&<length>{2,4}&&<color>?","shadow-t":"[<length>{2,3}&&<color>?]",shape:"rect( <top> , <right> , <bottom> , <left> )|rect( <top> <right> <bottom> <left> )","shape-box":"<box>|margin-box","side-or-corner":"[left|right]||[top|bottom]","single-animation":"<time>||<timing-function>||<time>||<single-animation-iteration-count>||<single-animation-direction>||<single-animation-fill-mode>||<single-animation-play-state>||[none|<keyframes-name>]","single-animation-direction":"normal|reverse|alternate|alternate-reverse","single-animation-fill-mode":"none|forwards|backwards|both","single-animation-iteration-count":"infinite|<number>","single-animation-play-state":"running|paused","single-transition":"[none|<single-transition-property>]||<time>||<timing-function>||<time>","single-transition-property":"all|<custom-ident>",size:"closest-side|farthest-side|closest-corner|farthest-corner|<length>|<length-percentage>{2}","step-position":"jump-start|jump-end|jump-none|jump-both|start|end","step-timing-function":"step-start|step-end|steps( <integer> [, <step-position>]? )","subclass-selector":"<id-selector>|<class-selector>|<attribute-selector>|<pseudo-class-selector>","supports-condition":"not <supports-in-parens>|<supports-in-parens> [and <supports-in-parens>]*|<supports-in-parens> [or <supports-in-parens>]*","supports-in-parens":"( <supports-condition> )|<supports-feature>|<general-enclosed>","supports-feature":"<supports-decl>|<supports-selector-fn>","supports-decl":"( <declaration> )","supports-selector-fn":"selector( <complex-selector> )",symbol:"<string>|<image>|<custom-ident>",target:"<target-counter()>|<target-counters()>|<target-text()>","target-counter()":"target-counter( [<string>|<url>] , <custom-ident> , <counter-style>? )","target-counters()":"target-counters( [<string>|<url>] , <custom-ident> , <string> , <counter-style>? )","target-text()":"target-text( [<string>|<url>] , [content|before|after|first-letter]? )","time-percentage":"<time>|<percentage>","timing-function":"linear|<cubic-bezier-timing-function>|<step-timing-function>","track-breadth":"<length-percentage>|<flex>|min-content|max-content|auto","track-list":"[<line-names>? [<track-size>|<track-repeat>]]+ <line-names>?","track-repeat":"repeat( [<positive-integer>] , [<line-names>? <track-size>]+ <line-names>? )","track-size":"<track-breadth>|minmax( <inflexible-breadth> , <track-breadth> )|fit-content( [<length>|<percentage>] )","transform-function":"<matrix()>|<translate()>|<translateX()>|<translateY()>|<scale()>|<scaleX()>|<scaleY()>|<rotate()>|<skew()>|<skewX()>|<skewY()>|<matrix3d()>|<translate3d()>|<translateZ()>|<scale3d()>|<scaleZ()>|<rotate3d()>|<rotateX()>|<rotateY()>|<rotateZ()>|<perspective()>","transform-list":"<transform-function>+","translate()":"translate( <length-percentage> , <length-percentage>? )","translate3d()":"translate3d( <length-percentage> , <length-percentage> , <length> )","translateX()":"translateX( <length-percentage> )","translateY()":"translateY( <length-percentage> )","translateZ()":"translateZ( <length> )","type-or-unit":"string|color|url|integer|number|length|angle|time|frequency|cap|ch|em|ex|ic|lh|rlh|rem|vb|vi|vw|vh|vmin|vmax|mm|Q|cm|in|pt|pc|px|deg|grad|rad|turn|ms|s|Hz|kHz|%","type-selector":"<wq-name>|<ns-prefix>? '*'","var()":"var( <custom-property-name> , <declaration-value>? )","viewport-length":"auto|<length-percentage>","wq-name":"<ns-prefix>? <ident-token>","-legacy-gradient":"<-webkit-gradient()>|<-legacy-linear-gradient>|<-legacy-repeating-linear-gradient>|<-legacy-radial-gradient>|<-legacy-repeating-radial-gradient>","-legacy-linear-gradient":"-moz-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-repeating-linear-gradient":"-moz-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-linear-gradient-arguments":"[<angle>|<side-or-corner>]? , <color-stop-list>","-legacy-radial-gradient":"-moz-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-repeating-radial-gradient":"-moz-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-radial-gradient-arguments":"[<position> ,]? [[[<-legacy-radial-gradient-shape>||<-legacy-radial-gradient-size>]|[<length>|<percentage>]{2}] ,]? <color-stop-list>","-legacy-radial-gradient-size":"closest-side|closest-corner|farthest-side|farthest-corner|contain|cover","-legacy-radial-gradient-shape":"circle|ellipse","-non-standard-font":"-apple-system-body|-apple-system-headline|-apple-system-subheadline|-apple-system-caption1|-apple-system-caption2|-apple-system-footnote|-apple-system-short-body|-apple-system-short-headline|-apple-system-short-subheadline|-apple-system-short-caption1|-apple-system-short-footnote|-apple-system-tall-body","-non-standard-color":"-moz-ButtonDefault|-moz-ButtonHoverFace|-moz-ButtonHoverText|-moz-CellHighlight|-moz-CellHighlightText|-moz-Combobox|-moz-ComboboxText|-moz-Dialog|-moz-DialogText|-moz-dragtargetzone|-moz-EvenTreeRow|-moz-Field|-moz-FieldText|-moz-html-CellHighlight|-moz-html-CellHighlightText|-moz-mac-accentdarkestshadow|-moz-mac-accentdarkshadow|-moz-mac-accentface|-moz-mac-accentlightesthighlight|-moz-mac-accentlightshadow|-moz-mac-accentregularhighlight|-moz-mac-accentregularshadow|-moz-mac-chrome-active|-moz-mac-chrome-inactive|-moz-mac-focusring|-moz-mac-menuselect|-moz-mac-menushadow|-moz-mac-menutextselect|-moz-MenuHover|-moz-MenuHoverText|-moz-MenuBarText|-moz-MenuBarHoverText|-moz-nativehyperlinktext|-moz-OddTreeRow|-moz-win-communicationstext|-moz-win-mediatext|-moz-activehyperlinktext|-moz-default-background-color|-moz-default-color|-moz-hyperlinktext|-moz-visitedhyperlinktext|-webkit-activelink|-webkit-focus-ring-color|-webkit-link|-webkit-text","-non-standard-image-rendering":"optimize-contrast|-moz-crisp-edges|-o-crisp-edges|-webkit-optimize-contrast","-non-standard-overflow":"-moz-scrollbars-none|-moz-scrollbars-horizontal|-moz-scrollbars-vertical|-moz-hidden-unscrollable","-non-standard-width":"min-intrinsic|intrinsic|-moz-min-content|-moz-max-content|-webkit-min-content|-webkit-max-content","-webkit-gradient()":"-webkit-gradient( <-webkit-gradient-type> , <-webkit-gradient-point> [, <-webkit-gradient-point>|, <-webkit-gradient-radius> , <-webkit-gradient-point>] [, <-webkit-gradient-radius>]? [, <-webkit-gradient-color-stop>]* )","-webkit-gradient-color-stop":"from( <color> )|color-stop( [<number-zero-one>|<percentage>] , <color> )|to( <color> )","-webkit-gradient-point":"[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]","-webkit-gradient-radius":"<length>|<percentage>","-webkit-gradient-type":"linear|radial","-webkit-mask-box-repeat":"repeat|stretch|round","-webkit-mask-clip-style":"border|border-box|padding|padding-box|content|content-box|text","-ms-filter-function-list":"<-ms-filter-function>+","-ms-filter-function":"<-ms-filter-function-progid>|<-ms-filter-function-legacy>","-ms-filter-function-progid":"'progid:' [<ident-token> '.']* [<ident-token>|<function-token> <any-value>? )]","-ms-filter-function-legacy":"<ident-token>|<function-token> <any-value>? )","-ms-filter":"<string>",age:"child|young|old","attr-name":"<wq-name>","attr-fallback":"<any-value>","border-radius":"<length-percentage>{1,2}",bottom:"<length>|auto","generic-voice":"[<age>? <gender> <integer>?]",gender:"male|female|neutral",left:"<length>|auto","mask-image":"<mask-reference>#","name-repeat":"repeat( [<positive-integer>|auto-fill] , <line-names>+ )",paint:"none|<color>|<url> [none|<color>]?|context-fill|context-stroke","path()":"path( <string> )",ratio:"<integer> / <integer>",right:"<length>|auto","svg-length":"<percentage>|<length>|<number>","svg-writing-mode":"lr-tb|rl-tb|tb-rl|lr|rl|tb",top:"<length>|auto",x:"<number>",y:"<number>",declaration:"<ident-token> : <declaration-value>? ['!' important]?","declaration-list":"[<declaration>? ';']* <declaration>?",url:"url( <string> <url-modifier>* )|<url-token>","url-modifier":"<ident>|<function-token> <any-value> )","number-zero-one":"<number [0,1]>","number-one-or-greater":"<number [1,∞]>","positive-integer":"<integer [0,∞]>"},Si={"--*":"<declaration-value>","-ms-accelerator":"false|true","-ms-block-progression":"tb|rl|bt|lr","-ms-content-zoom-chaining":"none|chained","-ms-content-zooming":"none|zoom","-ms-content-zoom-limit":"<'-ms-content-zoom-limit-min'> <'-ms-content-zoom-limit-max'>","-ms-content-zoom-limit-max":"<percentage>","-ms-content-zoom-limit-min":"<percentage>","-ms-content-zoom-snap":"<'-ms-content-zoom-snap-type'>||<'-ms-content-zoom-snap-points'>","-ms-content-zoom-snap-points":"snapInterval( <percentage> , <percentage> )|snapList( <percentage># )","-ms-content-zoom-snap-type":"none|proximity|mandatory","-ms-filter":"<string>","-ms-flow-from":"[none|<custom-ident>]#","-ms-flow-into":"[none|<custom-ident>]#","-ms-high-contrast-adjust":"auto|none","-ms-hyphenate-limit-chars":"auto|<integer>{1,3}","-ms-hyphenate-limit-lines":"no-limit|<integer>","-ms-hyphenate-limit-zone":"<percentage>|<length>","-ms-ime-align":"auto|after","-ms-overflow-style":"auto|none|scrollbar|-ms-autohiding-scrollbar","-ms-scrollbar-3dlight-color":"<color>","-ms-scrollbar-arrow-color":"<color>","-ms-scrollbar-base-color":"<color>","-ms-scrollbar-darkshadow-color":"<color>","-ms-scrollbar-face-color":"<color>","-ms-scrollbar-highlight-color":"<color>","-ms-scrollbar-shadow-color":"<color>","-ms-scrollbar-track-color":"<color>","-ms-scroll-chaining":"chained|none","-ms-scroll-limit":"<'-ms-scroll-limit-x-min'> <'-ms-scroll-limit-y-min'> <'-ms-scroll-limit-x-max'> <'-ms-scroll-limit-y-max'>","-ms-scroll-limit-x-max":"auto|<length>","-ms-scroll-limit-x-min":"<length>","-ms-scroll-limit-y-max":"auto|<length>","-ms-scroll-limit-y-min":"<length>","-ms-scroll-rails":"none|railed","-ms-scroll-snap-points-x":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-points-y":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-type":"none|proximity|mandatory","-ms-scroll-snap-x":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-x'>","-ms-scroll-snap-y":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-y'>","-ms-scroll-translation":"none|vertical-to-horizontal","-ms-text-autospace":"none|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space","-ms-touch-select":"grippers|none","-ms-user-select":"none|element|text","-ms-wrap-flow":"auto|both|start|end|maximum|clear","-ms-wrap-margin":"<length>","-ms-wrap-through":"wrap|none","-moz-appearance":"none|button|button-arrow-down|button-arrow-next|button-arrow-previous|button-arrow-up|button-bevel|button-focus|caret|checkbox|checkbox-container|checkbox-label|checkmenuitem|dualbutton|groupbox|listbox|listitem|menuarrow|menubar|menucheckbox|menuimage|menuitem|menuitemtext|menulist|menulist-button|menulist-text|menulist-textfield|menupopup|menuradio|menuseparator|meterbar|meterchunk|progressbar|progressbar-vertical|progresschunk|progresschunk-vertical|radio|radio-container|radio-label|radiomenuitem|range|range-thumb|resizer|resizerpanel|scale-horizontal|scalethumbend|scalethumb-horizontal|scalethumbstart|scalethumbtick|scalethumb-vertical|scale-vertical|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|separator|sheet|spinner|spinner-downbutton|spinner-textfield|spinner-upbutton|splitter|statusbar|statusbarpanel|tab|tabpanel|tabpanels|tab-scroll-arrow-back|tab-scroll-arrow-forward|textfield|textfield-multiline|toolbar|toolbarbutton|toolbarbutton-dropdown|toolbargripper|toolbox|tooltip|treeheader|treeheadercell|treeheadersortarrow|treeitem|treeline|treetwisty|treetwistyopen|treeview|-moz-mac-unified-toolbar|-moz-win-borderless-glass|-moz-win-browsertabbar-toolbox|-moz-win-communicationstext|-moz-win-communications-toolbox|-moz-win-exclude-glass|-moz-win-glass|-moz-win-mediatext|-moz-win-media-toolbox|-moz-window-button-box|-moz-window-button-box-maximized|-moz-window-button-close|-moz-window-button-maximize|-moz-window-button-minimize|-moz-window-button-restore|-moz-window-frame-bottom|-moz-window-frame-left|-moz-window-frame-right|-moz-window-titlebar|-moz-window-titlebar-maximized","-moz-binding":"<url>|none","-moz-border-bottom-colors":"<color>+|none","-moz-border-left-colors":"<color>+|none","-moz-border-right-colors":"<color>+|none","-moz-border-top-colors":"<color>+|none","-moz-context-properties":"none|[fill|fill-opacity|stroke|stroke-opacity]#","-moz-float-edge":"border-box|content-box|margin-box|padding-box","-moz-force-broken-image-icon":"<integer>","-moz-image-region":"<shape>|auto","-moz-orient":"inline|block|horizontal|vertical","-moz-outline-radius":"<outline-radius>{1,4} [/ <outline-radius>{1,4}]?","-moz-outline-radius-bottomleft":"<outline-radius>","-moz-outline-radius-bottomright":"<outline-radius>","-moz-outline-radius-topleft":"<outline-radius>","-moz-outline-radius-topright":"<outline-radius>","-moz-stack-sizing":"ignore|stretch-to-fit","-moz-text-blink":"none|blink","-moz-user-focus":"ignore|normal|select-after|select-before|select-menu|select-same|select-all|none","-moz-user-input":"auto|none|enabled|disabled","-moz-user-modify":"read-only|read-write|write-only","-moz-window-dragging":"drag|no-drag","-moz-window-shadow":"default|menu|tooltip|sheet|none","-webkit-appearance":"none|button|button-bevel|caps-lock-indicator|caret|checkbox|default-button|listbox|listitem|media-fullscreen-button|media-mute-button|media-play-button|media-seek-back-button|media-seek-forward-button|media-slider|media-sliderthumb|menulist|menulist-button|menulist-text|menulist-textfield|push-button|radio|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbargripper-horizontal|scrollbargripper-vertical|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|searchfield-cancel-button|searchfield-decoration|searchfield-results-button|searchfield-results-decoration|slider-horizontal|slider-vertical|sliderthumb-horizontal|sliderthumb-vertical|square-button|textarea|textfield","-webkit-border-before":"<'border-width'>||<'border-style'>||<'color'>","-webkit-border-before-color":"<'color'>","-webkit-border-before-style":"<'border-style'>","-webkit-border-before-width":"<'border-width'>","-webkit-box-reflect":"[above|below|right|left]? <length>? <image>?","-webkit-line-clamp":"none|<integer>","-webkit-mask":"[<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||[<box>|border|padding|content|text]||[<box>|border|padding|content]]#","-webkit-mask-attachment":"<attachment>#","-webkit-mask-clip":"[<box>|border|padding|content|text]#","-webkit-mask-composite":"<composite-style>#","-webkit-mask-image":"<mask-reference>#","-webkit-mask-origin":"[<box>|border|padding|content]#","-webkit-mask-position":"<position>#","-webkit-mask-position-x":"[<length-percentage>|left|center|right]#","-webkit-mask-position-y":"[<length-percentage>|top|center|bottom]#","-webkit-mask-repeat":"<repeat-style>#","-webkit-mask-repeat-x":"repeat|no-repeat|space|round","-webkit-mask-repeat-y":"repeat|no-repeat|space|round","-webkit-mask-size":"<bg-size>#","-webkit-overflow-scrolling":"auto|touch","-webkit-tap-highlight-color":"<color>","-webkit-text-fill-color":"<color>","-webkit-text-stroke":"<length>||<color>","-webkit-text-stroke-color":"<color>","-webkit-text-stroke-width":"<length>","-webkit-touch-callout":"default|none","-webkit-user-modify":"read-only|read-write|read-write-plaintext-only","align-content":"normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>","align-items":"normal|stretch|<baseline-position>|[<overflow-position>? <self-position>]","align-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? <self-position>",all:"initial|inherit|unset|revert",animation:"<single-animation>#","animation-delay":"<time>#","animation-direction":"<single-animation-direction>#","animation-duration":"<time>#","animation-fill-mode":"<single-animation-fill-mode>#","animation-iteration-count":"<single-animation-iteration-count>#","animation-name":"[none|<keyframes-name>]#","animation-play-state":"<single-animation-play-state>#","animation-timing-function":"<timing-function>#",appearance:"none|auto|button|textfield|<compat>",azimuth:"<angle>|[[left-side|far-left|left|center-left|center|center-right|right|far-right|right-side]||behind]|leftwards|rightwards","backdrop-filter":"none|<filter-function-list>","backface-visibility":"visible|hidden",background:"[<bg-layer> ,]* <final-bg-layer>","background-attachment":"<attachment>#","background-blend-mode":"<blend-mode>#","background-clip":"<box>#","background-color":"<color>","background-image":"<bg-image>#","background-origin":"<box>#","background-position":"<bg-position>#","background-position-x":"[center|[left|right|x-start|x-end]? <length-percentage>?]#","background-position-y":"[center|[top|bottom|y-start|y-end]? <length-percentage>?]#","background-repeat":"<repeat-style>#","background-size":"<bg-size>#","block-overflow":"clip|ellipsis|<string>","block-size":"<'width'>",border:"<line-width>||<line-style>||<color>","border-block":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-block-color":"<'border-top-color'>{1,2}","border-block-style":"<'border-top-style'>","border-block-width":"<'border-top-width'>","border-block-end":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-block-end-color":"<'border-top-color'>","border-block-end-style":"<'border-top-style'>","border-block-end-width":"<'border-top-width'>","border-block-start":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-block-start-color":"<'border-top-color'>","border-block-start-style":"<'border-top-style'>","border-block-start-width":"<'border-top-width'>","border-bottom":"<line-width>||<line-style>||<color>","border-bottom-color":"<'border-top-color'>","border-bottom-left-radius":"<length-percentage>{1,2}","border-bottom-right-radius":"<length-percentage>{1,2}","border-bottom-style":"<line-style>","border-bottom-width":"<line-width>","border-collapse":"collapse|separate","border-color":"<color>{1,4}","border-end-end-radius":"<length-percentage>{1,2}","border-end-start-radius":"<length-percentage>{1,2}","border-image":"<'border-image-source'>||<'border-image-slice'> [/ <'border-image-width'>|/ <'border-image-width'>? / <'border-image-outset'>]?||<'border-image-repeat'>","border-image-outset":"[<length>|<number>]{1,4}","border-image-repeat":"[stretch|repeat|round|space]{1,2}","border-image-slice":"<number-percentage>{1,4}&&fill?","border-image-source":"none|<image>","border-image-width":"[<length-percentage>|<number>|auto]{1,4}","border-inline":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-inline-end":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-inline-color":"<'border-top-color'>{1,2}","border-inline-style":"<'border-top-style'>","border-inline-width":"<'border-top-width'>","border-inline-end-color":"<'border-top-color'>","border-inline-end-style":"<'border-top-style'>","border-inline-end-width":"<'border-top-width'>","border-inline-start":"<'border-top-width'>||<'border-top-style'>||<'color'>","border-inline-start-color":"<'border-top-color'>","border-inline-start-style":"<'border-top-style'>","border-inline-start-width":"<'border-top-width'>","border-left":"<line-width>||<line-style>||<color>","border-left-color":"<color>","border-left-style":"<line-style>","border-left-width":"<line-width>","border-radius":"<length-percentage>{1,4} [/ <length-percentage>{1,4}]?","border-right":"<line-width>||<line-style>||<color>","border-right-color":"<color>","border-right-style":"<line-style>","border-right-width":"<line-width>","border-spacing":"<length> <length>?","border-start-end-radius":"<length-percentage>{1,2}","border-start-start-radius":"<length-percentage>{1,2}","border-style":"<line-style>{1,4}","border-top":"<line-width>||<line-style>||<color>","border-top-color":"<color>","border-top-left-radius":"<length-percentage>{1,2}","border-top-right-radius":"<length-percentage>{1,2}","border-top-style":"<line-style>","border-top-width":"<line-width>","border-width":"<line-width>{1,4}",bottom:"<length>|<percentage>|auto","box-align":"start|center|end|baseline|stretch","box-decoration-break":"slice|clone","box-direction":"normal|reverse|inherit","box-flex":"<number>","box-flex-group":"<integer>","box-lines":"single|multiple","box-ordinal-group":"<integer>","box-orient":"horizontal|vertical|inline-axis|block-axis|inherit","box-pack":"start|center|end|justify","box-shadow":"none|<shadow>#","box-sizing":"content-box|border-box","break-after":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-before":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-inside":"auto|avoid|avoid-page|avoid-column|avoid-region","caption-side":"top|bottom|block-start|block-end|inline-start|inline-end","caret-color":"auto|<color>",clear:"none|left|right|both|inline-start|inline-end",clip:"<shape>|auto","clip-path":"<clip-source>|[<basic-shape>||<geometry-box>]|none",color:"<color>","color-adjust":"economy|exact","column-count":"<integer>|auto","column-fill":"auto|balance|balance-all","column-gap":"normal|<length-percentage>","column-rule":"<'column-rule-width'>||<'column-rule-style'>||<'column-rule-color'>","column-rule-color":"<color>","column-rule-style":"<'border-style'>","column-rule-width":"<'border-width'>","column-span":"none|all","column-width":"<length>|auto",columns:"<'column-width'>||<'column-count'>",contain:"none|strict|content|[size||layout||style||paint]",content:"normal|none|[<content-replacement>|<content-list>] [/ <string>]?","counter-increment":"[<custom-ident> <integer>?]+|none","counter-reset":"[<custom-ident> <integer>?]+|none","counter-set":"[<custom-ident> <integer>?]+|none",cursor:"[[<url> [<x> <y>]? ,]* [auto|default|none|context-menu|help|pointer|progress|wait|cell|crosshair|text|vertical-text|alias|copy|move|no-drop|not-allowed|e-resize|n-resize|ne-resize|nw-resize|s-resize|se-resize|sw-resize|w-resize|ew-resize|ns-resize|nesw-resize|nwse-resize|col-resize|row-resize|all-scroll|zoom-in|zoom-out|grab|grabbing|hand|-webkit-grab|-webkit-grabbing|-webkit-zoom-in|-webkit-zoom-out|-moz-grab|-moz-grabbing|-moz-zoom-in|-moz-zoom-out]]",direction:"ltr|rtl",display:"none|inline|block|list-item|inline-list-item|inline-block|inline-table|table|table-cell|table-column|table-column-group|table-footer-group|table-header-group|table-row|table-row-group|flex|inline-flex|grid|inline-grid|run-in|ruby|ruby-base|ruby-text|ruby-base-container|ruby-text-container|contents|-ms-flexbox|-ms-inline-flexbox|-ms-grid|-ms-inline-grid|-webkit-flex|-webkit-inline-flex|-webkit-box|-webkit-inline-box|-moz-inline-stack|-moz-box|-moz-inline-box","empty-cells":"show|hide",filter:"none|<filter-function-list>|<-ms-filter-function-list>",flex:"none|[<'flex-grow'> <'flex-shrink'>?||<'flex-basis'>]","flex-basis":"content|<'width'>","flex-direction":"row|row-reverse|column|column-reverse","flex-flow":"<'flex-direction'>||<'flex-wrap'>","flex-grow":"<number>","flex-shrink":"<number>","flex-wrap":"nowrap|wrap|wrap-reverse",float:"left|right|none|inline-start|inline-end",font:"[[<'font-style'>||<font-variant-css21>||<'font-weight'>||<'font-stretch'>]? <'font-size'> [/ <'line-height'>]? <'font-family'>]|caption|icon|menu|message-box|small-caption|status-bar","font-family":"[<family-name>|<generic-family>]#","font-feature-settings":"normal|<feature-tag-value>#","font-kerning":"auto|normal|none","font-language-override":"normal|<string>","font-optical-sizing":"auto|none","font-variation-settings":"normal|[<string> <number>]#","font-size":"<absolute-size>|<relative-size>|<length-percentage>","font-size-adjust":"none|<number>","font-stretch":"<font-stretch-absolute>","font-style":"normal|italic|oblique <angle>?","font-synthesis":"none|[weight||style]","font-variant":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>||stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )||[small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps]||<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero||<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-alternates":"normal|[stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )]","font-variant-caps":"normal|small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps","font-variant-east-asian":"normal|[<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-ligatures":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>]","font-variant-numeric":"normal|[<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero]","font-variant-position":"normal|sub|super","font-weight":"<font-weight-absolute>|bolder|lighter",gap:"<'row-gap'> <'column-gap'>?",grid:"<'grid-template'>|<'grid-template-rows'> / [auto-flow&&dense?] <'grid-auto-columns'>?|[auto-flow&&dense?] <'grid-auto-rows'>? / <'grid-template-columns'>","grid-area":"<grid-line> [/ <grid-line>]{0,3}","grid-auto-columns":"<track-size>+","grid-auto-flow":"[row|column]||dense","grid-auto-rows":"<track-size>+","grid-column":"<grid-line> [/ <grid-line>]?","grid-column-end":"<grid-line>","grid-column-gap":"<length-percentage>","grid-column-start":"<grid-line>","grid-gap":"<'grid-row-gap'> <'grid-column-gap'>?","grid-row":"<grid-line> [/ <grid-line>]?","grid-row-end":"<grid-line>","grid-row-gap":"<length-percentage>","grid-row-start":"<grid-line>","grid-template":"none|[<'grid-template-rows'> / <'grid-template-columns'>]|[<line-names>? <string> <track-size>? <line-names>?]+ [/ <explicit-track-list>]?","grid-template-areas":"none|<string>+","grid-template-columns":"none|<track-list>|<auto-track-list>","grid-template-rows":"none|<track-list>|<auto-track-list>","hanging-punctuation":"none|[first||[force-end|allow-end]||last]",height:"[<length>|<percentage>]&&[border-box|content-box]?|available|min-content|max-content|fit-content|auto",hyphens:"none|manual|auto","image-orientation":"from-image|<angle>|[<angle>? flip]","image-rendering":"auto|crisp-edges|pixelated|optimizeSpeed|optimizeQuality|<-non-standard-image-rendering>","image-resolution":"[from-image||<resolution>]&&snap?","ime-mode":"auto|normal|active|inactive|disabled","initial-letter":"normal|[<number> <integer>?]","initial-letter-align":"[auto|alphabetic|hanging|ideographic]","inline-size":"<'width'>",inset:"<'top'>{1,4}","inset-block":"<'top'>{1,2}","inset-block-end":"<'top'>","inset-block-start":"<'top'>","inset-inline":"<'top'>{1,2}","inset-inline-end":"<'top'>","inset-inline-start":"<'top'>",isolation:"auto|isolate","justify-content":"normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]","justify-items":"normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]|legacy|legacy&&[left|right|center]","justify-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]",left:"<length>|<percentage>|auto","letter-spacing":"normal|<length-percentage>","line-break":"auto|loose|normal|strict","line-clamp":"none|<integer>","line-height":"normal|<number>|<length>|<percentage>","line-height-step":"<length>","list-style":"<'list-style-type'>||<'list-style-position'>||<'list-style-image'>","list-style-image":"<url>|none","list-style-position":"inside|outside","list-style-type":"<counter-style>|<string>|none",margin:"[<length>|<percentage>|auto]{1,4}","margin-block":"<'margin-left'>{1,2}","margin-block-end":"<'margin-left'>","margin-block-start":"<'margin-left'>","margin-bottom":"<length>|<percentage>|auto","margin-inline":"<'margin-left'>{1,2}","margin-inline-end":"<'margin-left'>","margin-inline-start":"<'margin-left'>","margin-left":"<length>|<percentage>|auto","margin-right":"<length>|<percentage>|auto","margin-top":"<length>|<percentage>|auto",mask:"<mask-layer>#","mask-border":"<'mask-border-source'>||<'mask-border-slice'> [/ <'mask-border-width'>? [/ <'mask-border-outset'>]?]?||<'mask-border-repeat'>||<'mask-border-mode'>","mask-border-mode":"luminance|alpha","mask-border-outset":"[<length>|<number>]{1,4}","mask-border-repeat":"[stretch|repeat|round|space]{1,2}","mask-border-slice":"<number-percentage>{1,4} fill?","mask-border-source":"none|<image>","mask-border-width":"[<length-percentage>|<number>|auto]{1,4}","mask-clip":"[<geometry-box>|no-clip]#","mask-composite":"<compositing-operator>#","mask-image":"<mask-reference>#","mask-mode":"<masking-mode>#","mask-origin":"<geometry-box>#","mask-position":"<position>#","mask-repeat":"<repeat-style>#","mask-size":"<bg-size>#","mask-type":"luminance|alpha","max-block-size":"<'max-width'>","max-height":"<length>|<percentage>|none|max-content|min-content|fit-content|fill-available","max-inline-size":"<'max-width'>","max-lines":"none|<integer>","max-width":"<length>|<percentage>|none|max-content|min-content|fit-content|fill-available|<-non-standard-width>","min-block-size":"<'min-width'>","min-height":"<length>|<percentage>|auto|max-content|min-content|fit-content|fill-available","min-inline-size":"<'min-width'>","min-width":"<length>|<percentage>|auto|max-content|min-content|fit-content|fill-available|<-non-standard-width>","mix-blend-mode":"<blend-mode>","object-fit":"fill|contain|cover|none|scale-down","object-position":"<position>",offset:"[<'offset-position'>? [<'offset-path'> [<'offset-distance'>||<'offset-rotate'>]?]?]! [/ <'offset-anchor'>]?","offset-anchor":"auto|<position>","offset-distance":"<length-percentage>","offset-path":"none|ray( [<angle>&&<size>?&&contain?] )|<path()>|<url>|[<basic-shape>||<geometry-box>]","offset-position":"auto|<position>","offset-rotate":"[auto|reverse]||<angle>",opacity:"<number-zero-one>",order:"<integer>",orphans:"<integer>",outline:"[<'outline-color'>||<'outline-style'>||<'outline-width'>]","outline-color":"<color>|invert","outline-offset":"<length>","outline-style":"auto|<'border-style'>","outline-width":"<line-width>",overflow:"[visible|hidden|clip|scroll|auto]{1,2}|<-non-standard-overflow>","overflow-anchor":"auto|none","overflow-block":"visible|hidden|clip|scroll|auto","overflow-clip-box":"padding-box|content-box","overflow-inline":"visible|hidden|clip|scroll|auto","overflow-wrap":"normal|break-word|anywhere","overflow-x":"visible|hidden|clip|scroll|auto","overflow-y":"visible|hidden|clip|scroll|auto","overscroll-behavior":"[contain|none|auto]{1,2}","overscroll-behavior-x":"contain|none|auto","overscroll-behavior-y":"contain|none|auto",padding:"[<length>|<percentage>]{1,4}","padding-block":"<'padding-left'>{1,2}","padding-block-end":"<'padding-left'>","padding-block-start":"<'padding-left'>","padding-bottom":"<length>|<percentage>","padding-inline":"<'padding-left'>{1,2}","padding-inline-end":"<'padding-left'>","padding-inline-start":"<'padding-left'>","padding-left":"<length>|<percentage>","padding-right":"<length>|<percentage>","padding-top":"<length>|<percentage>","page-break-after":"auto|always|avoid|left|right|recto|verso","page-break-before":"auto|always|avoid|left|right|recto|verso","page-break-inside":"auto|avoid","paint-order":"normal|[fill||stroke||markers]",perspective:"none|<length>","perspective-origin":"<position>","place-content":"<'align-content'> <'justify-content'>?","place-items":"<'align-items'> <'justify-items'>?","place-self":"<'align-self'> <'justify-self'>?","pointer-events":"auto|none|visiblePainted|visibleFill|visibleStroke|visible|painted|fill|stroke|all|inherit",position:"static|relative|absolute|sticky|fixed|-webkit-sticky",quotes:"none|[<string> <string>]+",resize:"none|both|horizontal|vertical|block|inline",right:"<length>|<percentage>|auto",rotate:"none|<angle>|[x|y|z|<number>{3}]&&<angle>","row-gap":"normal|<length-percentage>","ruby-align":"start|center|space-between|space-around","ruby-merge":"separate|collapse|auto","ruby-position":"over|under|inter-character",scale:"none|<number>{1,3}","scrollbar-color":"auto|dark|light|<color>{2}","scrollbar-width":"auto|thin|none","scroll-behavior":"auto|smooth","scroll-margin":"<length>{1,4}","scroll-margin-block":"<length>{1,2}","scroll-margin-block-start":"<length>","scroll-margin-block-end":"<length>","scroll-margin-bottom":"<length>","scroll-margin-inline":"<length>{1,2}","scroll-margin-inline-start":"<length>","scroll-margin-inline-end":"<length>","scroll-margin-left":"<length>","scroll-margin-right":"<length>","scroll-margin-top":"<length>","scroll-padding":"[auto|<length-percentage>]{1,4}","scroll-padding-block":"[auto|<length-percentage>]{1,2}","scroll-padding-block-start":"auto|<length-percentage>","scroll-padding-block-end":"auto|<length-percentage>","scroll-padding-bottom":"auto|<length-percentage>","scroll-padding-inline":"[auto|<length-percentage>]{1,2}","scroll-padding-inline-start":"auto|<length-percentage>","scroll-padding-inline-end":"auto|<length-percentage>","scroll-padding-left":"auto|<length-percentage>","scroll-padding-right":"auto|<length-percentage>","scroll-padding-top":"auto|<length-percentage>","scroll-snap-align":"[none|start|end|center]{1,2}","scroll-snap-coordinate":"none|<position>#","scroll-snap-destination":"<position>","scroll-snap-points-x":"none|repeat( <length-percentage> )","scroll-snap-points-y":"none|repeat( <length-percentage> )","scroll-snap-stop":"normal|always","scroll-snap-type":"none|[x|y|block|inline|both] [mandatory|proximity]?","scroll-snap-type-x":"none|mandatory|proximity","scroll-snap-type-y":"none|mandatory|proximity","shape-image-threshold":"<number>","shape-margin":"<length-percentage>","shape-outside":"none|<shape-box>||<basic-shape>|<image>","tab-size":"<integer>|<length>","table-layout":"auto|fixed","text-align":"start|end|left|right|center|justify|match-parent","text-align-last":"auto|start|end|left|right|center|justify","text-combine-upright":"none|all|[digits <integer>?]","text-decoration":"<'text-decoration-line'>||<'text-decoration-style'>||<'text-decoration-color'>","text-decoration-color":"<color>","text-decoration-line":"none|[underline||overline||line-through||blink]","text-decoration-skip":"none|[objects||[spaces|[leading-spaces||trailing-spaces]]||edges||box-decoration]","text-decoration-skip-ink":"auto|none","text-decoration-style":"solid|double|dotted|dashed|wavy","text-emphasis":"<'text-emphasis-style'>||<'text-emphasis-color'>","text-emphasis-color":"<color>","text-emphasis-position":"[over|under]&&[right|left]","text-emphasis-style":"none|[[filled|open]||[dot|circle|double-circle|triangle|sesame]]|<string>","text-indent":"<length-percentage>&&hanging?&&each-line?","text-justify":"auto|inter-character|inter-word|none","text-orientation":"mixed|upright|sideways","text-overflow":"[clip|ellipsis|<string>]{1,2}","text-rendering":"auto|optimizeSpeed|optimizeLegibility|geometricPrecision","text-shadow":"none|<shadow-t>#","text-size-adjust":"none|auto|<percentage>","text-transform":"none|capitalize|uppercase|lowercase|full-width|full-size-kana","text-underline-position":"auto|[under||[left|right]]",top:"<length>|<percentage>|auto","touch-action":"auto|none|[[pan-x|pan-left|pan-right]||[pan-y|pan-up|pan-down]||pinch-zoom]|manipulation",transform:"none|<transform-list>","transform-box":"border-box|fill-box|view-box","transform-origin":"[<length-percentage>|left|center|right|top|bottom]|[[<length-percentage>|left|center|right]&&[<length-percentage>|top|center|bottom]] <length>?","transform-style":"flat|preserve-3d",transition:"<single-transition>#","transition-delay":"<time>#","transition-duration":"<time>#","transition-property":"none|<single-transition-property>#","transition-timing-function":"<timing-function>#",translate:"none|<length-percentage> [<length-percentage> <length>?]?","unicode-bidi":"normal|embed|isolate|bidi-override|isolate-override|plaintext|-moz-isolate|-moz-isolate-override|-moz-plaintext|-webkit-isolate","user-select":"auto|text|none|contain|all","vertical-align":"baseline|sub|super|text-top|text-bottom|middle|top|bottom|<percentage>|<length>",visibility:"visible|hidden|collapse","white-space":"normal|pre|nowrap|pre-wrap|pre-line",widows:"<integer>",width:"[<length>|<percentage>]&&[border-box|content-box]?|available|min-content|max-content|fit-content|auto","will-change":"auto|<animateable-feature>#","word-break":"normal|break-all|keep-all|break-word","word-spacing":"normal|<length-percentage>","word-wrap":"normal|break-word","writing-mode":"horizontal-tb|vertical-rl|vertical-lr|sideways-rl|sideways-lr|<svg-writing-mode>","z-index":"auto|<integer>",zoom:"normal|reset|<number>|<percentage>","-moz-background-clip":"padding|border","-moz-border-radius-bottomleft":"<'border-bottom-left-radius'>","-moz-border-radius-bottomright":"<'border-bottom-right-radius'>","-moz-border-radius-topleft":"<'border-top-left-radius'>","-moz-border-radius-topright":"<'border-bottom-right-radius'>","-moz-osx-font-smoothing":"auto|grayscale","-moz-user-select":"none|text|all|-moz-none","-ms-flex-align":"start|end|center|baseline|stretch","-ms-flex-item-align":"auto|start|end|center|baseline|stretch","-ms-flex-line-pack":"start|end|center|justify|distribute|stretch","-ms-flex-negative":"<'flex-shrink'>","-ms-flex-pack":"start|end|center|justify|distribute","-ms-flex-order":"<integer>","-ms-flex-positive":"<'flex-grow'>","-ms-flex-preferred-size":"<'flex-basis'>","-ms-interpolation-mode":"nearest-neighbor|bicubic","-ms-grid-column-align":"start|end|center|stretch","-ms-grid-row-align":"start|end|center|stretch","-webkit-background-clip":"[<box>|border|padding|content|text]#","-webkit-column-break-after":"always|auto|avoid","-webkit-column-break-before":"always|auto|avoid","-webkit-column-break-inside":"always|auto|avoid","-webkit-font-smoothing":"auto|none|antialiased|subpixel-antialiased","-webkit-mask-box-image":"[<url>|<gradient>|none] [<length-percentage>{4} <-webkit-mask-box-repeat>{2}]?","-webkit-print-color-adjust":"economy|exact","-webkit-text-security":"none|circle|disc|square","-webkit-user-drag":"none|element|auto","-webkit-user-select":"auto|none|text|all","alignment-baseline":"auto|baseline|before-edge|text-before-edge|middle|central|after-edge|text-after-edge|ideographic|alphabetic|hanging|mathematical","baseline-shift":"baseline|sub|super|<svg-length>",behavior:"<url>+","clip-rule":"nonzero|evenodd",cue:"<'cue-before'> <'cue-after'>?","cue-after":"<url> <decibel>?|none","cue-before":"<url> <decibel>?|none","dominant-baseline":"auto|use-script|no-change|reset-size|ideographic|alphabetic|hanging|mathematical|central|middle|text-after-edge|text-before-edge",fill:"<paint>","fill-opacity":"<number-zero-one>","fill-rule":"nonzero|evenodd","glyph-orientation-horizontal":"<angle>","glyph-orientation-vertical":"<angle>",kerning:"auto|<svg-length>",marker:"none|<url>","marker-end":"none|<url>","marker-mid":"none|<url>","marker-start":"none|<url>",pause:"<'pause-before'> <'pause-after'>?","pause-after":"<time>|none|x-weak|weak|medium|strong|x-strong","pause-before":"<time>|none|x-weak|weak|medium|strong|x-strong",rest:"<'rest-before'> <'rest-after'>?","rest-after":"<time>|none|x-weak|weak|medium|strong|x-strong","rest-before":"<time>|none|x-weak|weak|medium|strong|x-strong","shape-rendering":"auto|optimizeSpeed|crispEdges|geometricPrecision",src:"[<url> [format( <string># )]?|local( <family-name> )]#",speak:"auto|none|normal","speak-as":"normal|spell-out||digits||[literal-punctuation|no-punctuation]",stroke:"<paint>","stroke-dasharray":"none|[<svg-length>+]#","stroke-dashoffset":"<svg-length>","stroke-linecap":"butt|round|square","stroke-linejoin":"miter|round|bevel","stroke-miterlimit":"<number-one-or-greater>","stroke-opacity":"<number-zero-one>","stroke-width":"<svg-length>","text-anchor":"start|middle|end","unicode-range":"<urange>#","voice-balance":"<number>|left|center|right|leftwards|rightwards","voice-duration":"auto|<time>","voice-family":"[[<family-name>|<generic-voice>] ,]* [<family-name>|<generic-voice>]|preserve","voice-pitch":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-range":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-rate":"[normal|x-slow|slow|medium|fast|x-fast]||<percentage>","voice-stress":"normal|strong|moderate|none|reduced","voice-volume":"silent|[[x-soft|soft|medium|loud|x-loud]||<decibel>]"},Ci={generic:!0,types:wi,properties:Si},zi=Object.freeze({__proto__:null,generic:!0,types:wi,properties:Si,default:Ci}),Ai=Le.cmpChar,Pi=Le.isDigit,Ti=Le.TYPE,Li=Ti.WhiteSpace,Ei=Ti.Comment,Oi=Ti.Ident,Di=Ti.Number,Bi=Ti.Dimension,Ii=43,Ni=45,Mi=110,Ri=!0;function _i(e,t){var n=this.scanner.tokenStart+e,r=this.scanner.source.charCodeAt(n);for(r!==Ii&&r!==Ni||(t&&this.error("Number sign is not allowed"),n++);n<this.scanner.tokenEnd;n++)Pi(this.scanner.source.charCodeAt(n))||this.error("Integer is expected",n)}function ji(e){return _i.call(this,0,e)}function Fi(e,t){if(!Ai(this.scanner.source,this.scanner.tokenStart+e,t)){var n="";switch(t){case Mi:n="N is expected";break;case Ni:n="HyphenMinus is expected"}this.error(n,this.scanner.tokenStart+e)}}function Wi(){for(var e=0,t=0,n=this.scanner.tokenType;n===Li||n===Ei;)n=this.scanner.lookupType(++e);if(n!==Di){if(!this.scanner.isDelim(Ii,e)&&!this.scanner.isDelim(Ni,e))return null;t=this.scanner.isDelim(Ii,e)?Ii:Ni;do{n=this.scanner.lookupType(++e)}while(n===Li||n===Ei);n!==Di&&(this.scanner.skip(e),ji.call(this,Ri))}return e>0&&this.scanner.skip(e),0===t&&(n=this.scanner.source.charCodeAt(this.scanner.tokenStart))!==Ii&&n!==Ni&&this.error("Number sign is expected"),ji.call(this,0!==t),t===Ni?"-"+this.consume(Di):this.consume(Di)}var qi={name:"AnPlusB",structure:{a:[String,null],b:[String,null]},parse:function(){var e=this.scanner.tokenStart,t=null,n=null;if(this.scanner.tokenType===Di)ji.call(this,!1),n=this.consume(Di);else if(this.scanner.tokenType===Oi&&Ai(this.scanner.source,this.scanner.tokenStart,Ni))switch(t="-1",Fi.call(this,1,Mi),this.scanner.getTokenLength()){case 2:this.scanner.next(),n=Wi.call(this);break;case 3:Fi.call(this,2,Ni),this.scanner.next(),this.scanner.skipSC(),ji.call(this,Ri),n="-"+this.consume(Di);break;default:Fi.call(this,2,Ni),_i.call(this,3,Ri),this.scanner.next(),n=this.scanner.substrToCursor(e+2)}else if(this.scanner.tokenType===Oi||this.scanner.isDelim(Ii)&&this.scanner.lookupType(1)===Oi){var r=0;switch(t="1",this.scanner.isDelim(Ii)&&(r=1,this.scanner.next()),Fi.call(this,0,Mi),this.scanner.getTokenLength()){case 1:this.scanner.next(),n=Wi.call(this);break;case 2:Fi.call(this,1,Ni),this.scanner.next(),this.scanner.skipSC(),ji.call(this,Ri),n="-"+this.consume(Di);break;default:Fi.call(this,1,Ni),_i.call(this,2,Ri),this.scanner.next(),n=this.scanner.substrToCursor(e+r+1)}}else if(this.scanner.tokenType===Bi){for(var i=this.scanner.source.charCodeAt(this.scanner.tokenStart),a=(r=i===Ii||i===Ni,this.scanner.tokenStart+r);a<this.scanner.tokenEnd&&Pi(this.scanner.source.charCodeAt(a));a++);a===this.scanner.tokenStart+r&&this.error("Integer is expected",this.scanner.tokenStart+r),Fi.call(this,a-this.scanner.tokenStart,Mi),t=this.scanner.source.substring(e,a),a+1===this.scanner.tokenEnd?(this.scanner.next(),n=Wi.call(this)):(Fi.call(this,a-this.scanner.tokenStart+1,Ni),a+2===this.scanner.tokenEnd?(this.scanner.next(),this.scanner.skipSC(),ji.call(this,Ri),n="-"+this.consume(Di)):(_i.call(this,a-this.scanner.tokenStart+2,Ri),this.scanner.next(),n=this.scanner.substrToCursor(a+1)))}else this.error();return null!==t&&t.charCodeAt(0)===Ii&&(t=t.substr(1)),null!==n&&n.charCodeAt(0)===Ii&&(n=n.substr(1)),{type:"AnPlusB",loc:this.getLocation(e,this.scanner.tokenStart),a:t,b:n}},generate:function(e){var t=null!==e.a&&void 0!==e.a,n=null!==e.b&&void 0!==e.b;t?(this.chunk("+1"===e.a?"+n":"1"===e.a?"n":"-1"===e.a?"-n":e.a+"n"),n&&("-"===(n=String(e.b)).charAt(0)||"+"===n.charAt(0)?(this.chunk(n.charAt(0)),this.chunk(n.substr(1))):(this.chunk("+"),this.chunk(n)))):this.chunk(String(e.b))}},Yi=Le.TYPE,Ui=Yi.WhiteSpace,Hi=Yi.Semicolon,Vi=Yi.LeftCurlyBracket,Gi=Yi.Delim,Ki=33;function Qi(){return this.scanner.tokenIndex>0&&this.scanner.lookupType(-1)===Ui?this.scanner.tokenIndex>1?this.scanner.getTokenStart(this.scanner.tokenIndex-1):this.scanner.firstCharOffset:this.scanner.tokenStart}function Xi(){return 0}var Zi={name:"Raw",structure:{value:String},parse:function(e,t,n){var r,i=this.scanner.getTokenStart(e);return this.scanner.skip(this.scanner.getRawLength(e,t||Xi)),r=n&&this.scanner.tokenStart>i?Qi.call(this):this.scanner.tokenStart,{type:"Raw",loc:this.getLocation(i,r),value:this.scanner.source.substring(i,r)}},generate:function(e){this.chunk(e.value)},mode:{default:Xi,leftCurlyBracket:function(e){return e===Vi?1:0},leftCurlyBracketOrSemicolon:function(e){return e===Vi||e===Hi?1:0},exclamationMarkOrSemicolon:function(e,t,n){return e===Gi&&t.charCodeAt(n)===Ki?1:e===Hi?1:0},semicolonIncluded:function(e){return e===Hi?2:0}}},$i=Le.TYPE,Ji=Zi.mode,ea=$i.AtKeyword,ta=$i.Semicolon,na=$i.LeftCurlyBracket,ra=$i.RightCurlyBracket;function ia(e){return this.Raw(e,Ji.leftCurlyBracketOrSemicolon,!0)}function aa(){for(var e,t=1;e=this.scanner.lookupType(t);t++){if(e===ra)return!0;if(e===na||e===ea)return!1}return!1}var oa={name:"Atrule",structure:{name:String,prelude:["AtrulePrelude","Raw",null],block:["Block",null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null,i=null;switch(this.eat(ea),t=(e=this.scanner.substrToCursor(n+1)).toLowerCase(),this.scanner.skipSC(),!1===this.scanner.eof&&this.scanner.tokenType!==na&&this.scanner.tokenType!==ta&&(this.parseAtrulePrelude?"AtrulePrelude"===(r=this.parseWithFallback(this.AtrulePrelude.bind(this,e),ia)).type&&null===r.children.head&&(r=null):r=ia.call(this,this.scanner.tokenIndex),this.scanner.skipSC()),this.scanner.tokenType){case ta:this.scanner.next();break;case na:i=this.atrule.hasOwnProperty(t)&&"function"==typeof this.atrule[t].block?this.atrule[t].block.call(this):this.Block(aa.call(this))}return{type:"Atrule",loc:this.getLocation(n,this.scanner.tokenStart),name:e,prelude:r,block:i}},generate:function(e){this.chunk("@"),this.chunk(e.name),null!==e.prelude&&(this.chunk(" "),this.node(e.prelude)),e.block?this.node(e.block):this.chunk(";")},walkContext:"atrule"},sa=Le.TYPE,la=sa.Semicolon,ca=sa.LeftCurlyBracket,ua={name:"AtrulePrelude",structure:{children:[[]]},parse:function(e){var t=null;return null!==e&&(e=e.toLowerCase()),this.scanner.skipSC(),t=this.atrule.hasOwnProperty(e)&&"function"==typeof this.atrule[e].prelude?this.atrule[e].prelude.call(this):this.readSequence(this.scope.AtrulePrelude),this.scanner.skipSC(),!0!==this.scanner.eof&&this.scanner.tokenType!==ca&&this.scanner.tokenType!==la&&this.error("Semicolon or block is expected"),null===t&&(t=this.createList()),{type:"AtrulePrelude",loc:this.getLocationFromList(t),children:t}},generate:function(e){this.children(e)},walkContext:"atrulePrelude"},ha=Le.TYPE,pa=ha.Ident,da=ha.String,ma=ha.Colon,ga=ha.LeftSquareBracket,fa=ha.RightSquareBracket,ba=36,ya=42,ka=61,va=94,xa=124,wa=126;function Sa(){this.scanner.eof&&this.error("Unexpected end of input");var e=this.scanner.tokenStart,t=!1,n=!0;return this.scanner.isDelim(ya)?(t=!0,n=!1,this.scanner.next()):this.scanner.isDelim(xa)||this.eat(pa),this.scanner.isDelim(xa)?this.scanner.source.charCodeAt(this.scanner.tokenStart+1)!==ka?(this.scanner.next(),this.eat(pa)):t&&this.error("Identifier is expected",this.scanner.tokenEnd):t&&this.error("Vertical line is expected"),n&&this.scanner.tokenType===ma&&(this.scanner.next(),this.eat(pa)),{type:"Identifier",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}}function Ca(){var e=this.scanner.tokenStart,t=this.scanner.source.charCodeAt(e);return t!==ka&&t!==wa&&t!==va&&t!==ba&&t!==ya&&t!==xa&&this.error("Attribute selector (=, ~=, ^=, $=, *=, |=) is expected"),this.scanner.next(),t!==ka&&(this.scanner.isDelim(ka)||this.error("Equal sign is expected"),this.scanner.next()),this.scanner.substrToCursor(e)}var za={name:"AttributeSelector",structure:{name:"Identifier",matcher:[String,null],value:["String","Identifier",null],flags:[String,null]},parse:function(){var e,t=this.scanner.tokenStart,n=null,r=null,i=null;return this.eat(ga),this.scanner.skipSC(),e=Sa.call(this),this.scanner.skipSC(),this.scanner.tokenType!==fa&&(this.scanner.tokenType!==pa&&(n=Ca.call(this),this.scanner.skipSC(),r=this.scanner.tokenType===da?this.String():this.Identifier(),this.scanner.skipSC()),this.scanner.tokenType===pa&&(i=this.scanner.getTokenValue(),this.scanner.next(),this.scanner.skipSC())),this.eat(fa),{type:"AttributeSelector",loc:this.getLocation(t,this.scanner.tokenStart),name:e,matcher:n,value:r,flags:i}},generate:function(e){var t=" ";this.chunk("["),this.node(e.name),null!==e.matcher&&(this.chunk(e.matcher),null!==e.value&&(this.node(e.value),"String"===e.value.type&&(t=""))),null!==e.flags&&(this.chunk(t),this.chunk(e.flags)),this.chunk("]")}},Aa=Le.TYPE,Pa=Zi.mode,Ta=Aa.WhiteSpace,La=Aa.Comment,Ea=Aa.Semicolon,Oa=Aa.AtKeyword,Da=Aa.LeftCurlyBracket,Ba=Aa.RightCurlyBracket;function Ia(e){return this.Raw(e,null,!0)}function Na(){return this.parseWithFallback(this.Rule,Ia)}function Ma(e){return this.Raw(e,Pa.semicolonIncluded,!0)}function Ra(){if(this.scanner.tokenType===Ea)return Ma.call(this,this.scanner.tokenIndex);var e=this.parseWithFallback(this.Declaration,Ma);return this.scanner.tokenType===Ea&&this.scanner.next(),e}var _a={name:"Block",structure:{children:[["Atrule","Rule","Declaration"]]},parse:function(e){var t=e?Ra:Na,n=this.scanner.tokenStart,r=this.createList();this.eat(Da);e:for(;!this.scanner.eof;)switch(this.scanner.tokenType){case Ba:break e;case Ta:case La:this.scanner.next();break;case Oa:r.push(this.parseWithFallback(this.Atrule,Ia));break;default:r.push(t.call(this))}return this.scanner.eof||this.eat(Ba),{type:"Block",loc:this.getLocation(n,this.scanner.tokenStart),children:r}},generate:function(e){this.chunk("{"),this.children(e,(function(e){"Declaration"===e.type&&this.chunk(";")})),this.chunk("}")},walkContext:"block"},ja=Le.TYPE,Fa=ja.LeftSquareBracket,Wa=ja.RightSquareBracket,qa={name:"Brackets",structure:{children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart;return this.eat(Fa),n=e.call(this,t),this.scanner.eof||this.eat(Wa),{type:"Brackets",loc:this.getLocation(r,this.scanner.tokenStart),children:n}},generate:function(e){this.chunk("["),this.children(e),this.chunk("]")}},Ya=Le.TYPE.CDC,Ua={name:"CDC",structure:[],parse:function(){var e=this.scanner.tokenStart;return this.eat(Ya),{type:"CDC",loc:this.getLocation(e,this.scanner.tokenStart)}},generate:function(){this.chunk("--\x3e")}},Ha=Le.TYPE.CDO,Va={name:"CDO",structure:[],parse:function(){var e=this.scanner.tokenStart;return this.eat(Ha),{type:"CDO",loc:this.getLocation(e,this.scanner.tokenStart)}},generate:function(){this.chunk("\x3c!--")}},Ga=Le.TYPE.Ident,Ka={name:"ClassSelector",structure:{name:String},parse:function(){return this.scanner.isDelim(46)||this.error("Full stop is expected"),this.scanner.next(),{type:"ClassSelector",loc:this.getLocation(this.scanner.tokenStart-1,this.scanner.tokenEnd),name:this.consume(Ga)}},generate:function(e){this.chunk("."),this.chunk(e.name)}},Qa=Le.TYPE.Ident,Xa={name:"Combinator",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;switch(this.scanner.source.charCodeAt(this.scanner.tokenStart)){case 62:case 43:case 126:this.scanner.next();break;case 47:this.scanner.next(),this.scanner.tokenType===Qa&&!1!==this.scanner.lookupValue(0,"deep")||this.error("Identifier `deep` is expected"),this.scanner.next(),this.scanner.isDelim(47)||this.error("Solidus is expected"),this.scanner.next();break;default:this.error("Combinator is expected")}return{type:"Combinator",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.name)}},Za=Le.TYPE.Comment,$a={name:"Comment",structure:{value:String},parse:function(){var e=this.scanner.tokenStart,t=this.scanner.tokenEnd;return this.eat(Za),t-e+2>=2&&42===this.scanner.source.charCodeAt(t-2)&&47===this.scanner.source.charCodeAt(t-1)&&(t-=2),{type:"Comment",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e+2,t)}},generate:function(e){this.chunk("/*"),this.chunk(e.value),this.chunk("*/")}},Ja=le.isCustomProperty,eo=Le.TYPE,to=Zi.mode,no=eo.Ident,ro=eo.Hash,io=eo.Colon,ao=eo.Semicolon,oo=eo.Delim,so=33,lo=35,co=36,uo=38,ho=42,po=43,mo=47;function go(e){return this.Raw(e,to.exclamationMarkOrSemicolon,!0)}function fo(e){return this.Raw(e,to.exclamationMarkOrSemicolon,!1)}function bo(){var e=this.scanner.tokenIndex,t=this.Value();return"Raw"!==t.type&&!1===this.scanner.eof&&this.scanner.tokenType!==ao&&!1===this.scanner.isDelim(so)&&!1===this.scanner.isBalanceEdge(e)&&this.error(),t}var yo={name:"Declaration",structure:{important:[Boolean,String],property:String,value:["Value","Raw"]},parse:function(){var e,t=this.scanner.tokenStart,n=this.scanner.tokenIndex,r=ko.call(this),i=Ja(r),a=i?this.parseCustomProperty:this.parseValue,o=i?fo:go,s=!1;return this.scanner.skipSC(),this.eat(io),i||this.scanner.skipSC(),e=a?this.parseWithFallback(bo,o):o.call(this,this.scanner.tokenIndex),this.scanner.isDelim(so)&&(s=vo.call(this),this.scanner.skipSC()),!1===this.scanner.eof&&this.scanner.tokenType!==ao&&!1===this.scanner.isBalanceEdge(n)&&this.error(),{type:"Declaration",loc:this.getLocation(t,this.scanner.tokenStart),important:s,property:r,value:e}},generate:function(e){this.chunk(e.property),this.chunk(":"),this.node(e.value),e.important&&this.chunk(!0===e.important?"!important":"!"+e.important)},walkContext:"declaration"};function ko(){var e=this.scanner.tokenStart;if(this.scanner.tokenType===oo)switch(this.scanner.source.charCodeAt(this.scanner.tokenStart)){case ho:case co:case po:case lo:case uo:this.scanner.next();break;case mo:this.scanner.next(),this.scanner.isDelim(mo)&&this.scanner.next()}return this.scanner.tokenType===ro?this.eat(ro):this.eat(no),this.scanner.substrToCursor(e)}function vo(){this.eat(oo),this.scanner.skipSC();var e=this.consume(no);return"important"===e||e}var xo=Le.TYPE,wo=Zi.mode,So=xo.WhiteSpace,Co=xo.Comment,zo=xo.Semicolon;function Ao(e){return this.Raw(e,wo.semicolonIncluded,!0)}var Po={name:"DeclarationList",structure:{children:[["Declaration"]]},parse:function(){for(var e=this.createList();!this.scanner.eof;)switch(this.scanner.tokenType){case So:case Co:case zo:this.scanner.next();break;default:e.push(this.parseWithFallback(this.Declaration,Ao))}return{type:"DeclarationList",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e,(function(e){"Declaration"===e.type&&this.chunk(";")}))}},To=W.consumeNumber,Lo=Le.TYPE.Dimension,Eo={name:"Dimension",structure:{value:String,unit:String},parse:function(){var e=this.scanner.tokenStart,t=To(this.scanner.source,e);return this.eat(Lo),{type:"Dimension",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e,t),unit:this.scanner.source.substring(t,this.scanner.tokenStart)}},generate:function(e){this.chunk(e.value),this.chunk(e.unit)}},Oo=Le.TYPE.RightParenthesis,Do={name:"Function",structure:{name:String,children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart,i=this.consumeFunctionName(),a=i.toLowerCase();return n=t.hasOwnProperty(a)?t[a].call(this,t):e.call(this,t),this.scanner.eof||this.eat(Oo),{type:"Function",loc:this.getLocation(r,this.scanner.tokenStart),name:i,children:n}},generate:function(e){this.chunk(e.name),this.chunk("("),this.children(e),this.chunk(")")},walkContext:"function"},Bo=Le.TYPE.Hash,Io={name:"HexColor",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return this.eat(Bo),{type:"HexColor",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e+1)}},generate:function(e){this.chunk("#"),this.chunk(e.value)}},No=Le.TYPE.Ident,Mo={name:"Identifier",structure:{name:String},parse:function(){return{type:"Identifier",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),name:this.consume(No)}},generate:function(e){this.chunk(e.name)}},Ro=Le.TYPE.Hash,_o={name:"IdSelector",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;return this.eat(Ro),{type:"IdSelector",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e+1)}},generate:function(e){this.chunk("#"),this.chunk(e.name)}},jo=Le.TYPE,Fo=jo.Ident,Wo=jo.Number,qo=jo.Dimension,Yo=jo.LeftParenthesis,Uo=jo.RightParenthesis,Ho=jo.Colon,Vo=jo.Delim,Go={name:"MediaFeature",structure:{name:String,value:["Identifier","Number","Dimension","Ratio",null]},parse:function(){var e,t=this.scanner.tokenStart,n=null;if(this.eat(Yo),this.scanner.skipSC(),e=this.consume(Fo),this.scanner.skipSC(),this.scanner.tokenType!==Uo){switch(this.eat(Ho),this.scanner.skipSC(),this.scanner.tokenType){case Wo:n=this.lookupNonWSType(1)===Vo?this.Ratio():this.Number();break;case qo:n=this.Dimension();break;case Fo:n=this.Identifier();break;default:this.error("Number, dimension, ratio or identifier is expected")}this.scanner.skipSC()}return this.eat(Uo),{type:"MediaFeature",loc:this.getLocation(t,this.scanner.tokenStart),name:e,value:n}},generate:function(e){this.chunk("("),this.chunk(e.name),null!==e.value&&(this.chunk(":"),this.node(e.value)),this.chunk(")")}},Ko=Le.TYPE,Qo=Ko.WhiteSpace,Xo=Ko.Comment,Zo=Ko.Ident,$o=Ko.LeftParenthesis,Jo={name:"MediaQuery",structure:{children:[["Identifier","MediaFeature","WhiteSpace"]]},parse:function(){this.scanner.skipSC();var e=this.createList(),t=null,n=null;e:for(;!this.scanner.eof;){switch(this.scanner.tokenType){case Xo:this.scanner.next();continue;case Qo:n=this.WhiteSpace();continue;case Zo:t=this.Identifier();break;case $o:t=this.MediaFeature();break;default:break e}null!==n&&(e.push(n),n=null),e.push(t)}return null===t&&this.error("Identifier or parenthesis is expected"),{type:"MediaQuery",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e)}},es=Le.TYPE.Comma,ts={name:"MediaQueryList",structure:{children:[["MediaQuery"]]},parse:function(e){var t=this.createList();for(this.scanner.skipSC();!this.scanner.eof&&(t.push(this.MediaQuery(e)),this.scanner.tokenType===es);)this.scanner.next();return{type:"MediaQueryList",loc:this.getLocationFromList(t),children:t}},generate:function(e){this.children(e,(function(){this.chunk(",")}))}},ns=Le.TYPE.Number,rs={name:"Number",structure:{value:String},parse:function(){return{type:"Number",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),value:this.consume(ns)}},generate:function(e){this.chunk(e.value)}},is={name:"Operator",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return this.scanner.next(),{type:"Operator",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.value)}},as=Le.TYPE,os=as.LeftParenthesis,ss=as.RightParenthesis,ls={name:"Parentheses",structure:{children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart;return this.eat(os),n=e.call(this,t),this.scanner.eof||this.eat(ss),{type:"Parentheses",loc:this.getLocation(r,this.scanner.tokenStart),children:n}},generate:function(e){this.chunk("("),this.children(e),this.chunk(")")}},cs=W.consumeNumber,us=Le.TYPE.Percentage,hs={name:"Percentage",structure:{value:String},parse:function(){var e=this.scanner.tokenStart,t=cs(this.scanner.source,e);return this.eat(us),{type:"Percentage",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e,t)}},generate:function(e){this.chunk(e.value),this.chunk("%")}},ps=Le.TYPE,ds=ps.Ident,ms=ps.Function,gs=ps.Colon,fs=ps.RightParenthesis,bs={name:"PseudoClassSelector",structure:{name:String,children:[["Raw"],null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null;return this.eat(gs),this.scanner.tokenType===ms?(t=(e=this.consumeFunctionName()).toLowerCase(),this.pseudo.hasOwnProperty(t)?(this.scanner.skipSC(),r=this.pseudo[t].call(this),this.scanner.skipSC()):(r=this.createList()).push(this.Raw(this.scanner.tokenIndex,null,!1)),this.eat(fs)):e=this.consume(ds),{type:"PseudoClassSelector",loc:this.getLocation(n,this.scanner.tokenStart),name:e,children:r}},generate:function(e){this.chunk(":"),this.chunk(e.name),null!==e.children&&(this.chunk("("),this.children(e),this.chunk(")"))},walkContext:"function"},ys=Le.TYPE,ks=ys.Ident,vs=ys.Function,xs=ys.Colon,ws=ys.RightParenthesis,Ss={name:"PseudoElementSelector",structure:{name:String,children:[["Raw"],null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null;return this.eat(xs),this.eat(xs),this.scanner.tokenType===vs?(t=(e=this.consumeFunctionName()).toLowerCase(),this.pseudo.hasOwnProperty(t)?(this.scanner.skipSC(),r=this.pseudo[t].call(this),this.scanner.skipSC()):(r=this.createList()).push(this.Raw(this.scanner.tokenIndex,null,!1)),this.eat(ws)):e=this.consume(ks),{type:"PseudoElementSelector",loc:this.getLocation(n,this.scanner.tokenStart),name:e,children:r}},generate:function(e){this.chunk("::"),this.chunk(e.name),null!==e.children&&(this.chunk("("),this.children(e),this.chunk(")"))},walkContext:"function"},Cs=Le.isDigit,zs=Le.TYPE,As=zs.Number,Ps=zs.Delim,Ts=46;function Ls(){this.scanner.skipWS();for(var e=this.consume(As),t=0;t<e.length;t++){var n=e.charCodeAt(t);Cs(n)||n===Ts||this.error("Unsigned number is expected",this.scanner.tokenStart-e.length+t)}return 0===Number(e)&&this.error("Zero number is not allowed",this.scanner.tokenStart-e.length),e}var Es={name:"Ratio",structure:{left:String,right:String},parse:function(){var e,t=this.scanner.tokenStart,n=Ls.call(this);return this.scanner.skipWS(),this.scanner.isDelim(47)||this.error("Solidus is expected"),this.eat(Ps),e=Ls.call(this),{type:"Ratio",loc:this.getLocation(t,this.scanner.tokenStart),left:n,right:e}},generate:function(e){this.chunk(e.left),this.chunk("/"),this.chunk(e.right)}},Os=Le.TYPE,Ds=Zi.mode,Bs=Os.LeftCurlyBracket;function Is(e){return this.Raw(e,Ds.leftCurlyBracket,!0)}function Ns(){var e=this.SelectorList();return"Raw"!==e.type&&!1===this.scanner.eof&&this.scanner.tokenType!==Bs&&this.error(),e}var Ms={name:"Rule",structure:{prelude:["SelectorList","Raw"],block:["Block"]},parse:function(){var e,t,n=this.scanner.tokenIndex,r=this.scanner.tokenStart;return e=this.parseRulePrelude?this.parseWithFallback(Ns,Is):Is.call(this,n),t=this.Block(!0),{type:"Rule",loc:this.getLocation(r,this.scanner.tokenStart),prelude:e,block:t}},generate:function(e){this.node(e.prelude),this.node(e.block)},walkContext:"rule"},Rs=Le.TYPE.Comma,_s={name:"SelectorList",structure:{children:[["Selector","Raw"]]},parse:function(){for(var e=this.createList();!this.scanner.eof&&(e.push(this.Selector()),this.scanner.tokenType===Rs);)this.scanner.next();return{type:"SelectorList",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e,(function(){this.chunk(",")}))},walkContext:"selector"},js=Le.TYPE.String,Fs={name:"String",structure:{value:String},parse:function(){return{type:"String",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),value:this.consume(js)}},generate:function(e){this.chunk(e.value)}},Ws=Le.TYPE,qs=Ws.WhiteSpace,Ys=Ws.Comment,Us=Ws.AtKeyword,Hs=Ws.CDO,Vs=Ws.CDC;function Gs(e){return this.Raw(e,null,!1)}var Ks={name:"StyleSheet",structure:{children:[["Comment","CDO","CDC","Atrule","Rule","Raw"]]},parse:function(){for(var e,t=this.scanner.tokenStart,n=this.createList();!this.scanner.eof;){switch(this.scanner.tokenType){case qs:this.scanner.next();continue;case Ys:if(33!==this.scanner.source.charCodeAt(this.scanner.tokenStart+2)){this.scanner.next();continue}e=this.Comment();break;case Hs:e=this.CDO();break;case Vs:e=this.CDC();break;case Us:e=this.parseWithFallback(this.Atrule,Gs);break;default:e=this.parseWithFallback(this.Rule,Gs)}n.push(e)}return{type:"StyleSheet",loc:this.getLocation(t,this.scanner.tokenStart),children:n}},generate:function(e){this.children(e)},walkContext:"stylesheet"},Qs=Le.TYPE.Ident,Xs=42;function Zs(){this.scanner.tokenType!==Qs&&!1===this.scanner.isDelim(Xs)&&this.error("Identifier or asterisk is expected"),this.scanner.next()}var $s={name:"TypeSelector",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;return this.scanner.isDelim(124)?(this.scanner.next(),Zs.call(this)):(Zs.call(this),this.scanner.isDelim(124)&&(this.scanner.next(),Zs.call(this))),{type:"TypeSelector",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.name)}},Js=Le.isHexDigit,el=Le.cmpChar,tl=Le.TYPE,nl=Le.NAME,rl=tl.Ident,il=tl.Number,al=tl.Dimension,ol=43,sl=45,ll=63;function cl(e,t){for(var n=this.scanner.tokenStart+e,r=0;n<this.scanner.tokenEnd;n++){var i=this.scanner.source.charCodeAt(n);if(i===sl&&t&&0!==r)return 0===cl.call(this,e+r+1,!1)&&this.error(),-1;Js(i)||this.error(t&&0!==r?"HyphenMinus"+(r<6?" or hex digit":"")+" is expected":r<6?"Hex digit is expected":"Unexpected input",n),++r>6&&this.error("Too many hex digits",n)}return this.scanner.next(),r}function ul(e){for(var t=0;this.scanner.isDelim(ll);)++t>e&&this.error("Too many question marks"),this.scanner.next()}function hl(e){this.scanner.source.charCodeAt(this.scanner.tokenStart)!==e&&this.error(nl[e]+" is expected")}function pl(){var e=0;return this.scanner.isDelim(ol)?(this.scanner.next(),this.scanner.tokenType===rl?void((e=cl.call(this,0,!0))>0&&ul.call(this,6-e)):this.scanner.isDelim(ll)?(this.scanner.next(),void ul.call(this,5)):void this.error("Hex digit or question mark is expected")):this.scanner.tokenType===il?(hl.call(this,ol),e=cl.call(this,1,!0),this.scanner.isDelim(ll)?void ul.call(this,6-e):this.scanner.tokenType===al||this.scanner.tokenType===il?(hl.call(this,sl),void cl.call(this,1,!1)):void 0):this.scanner.tokenType===al?(hl.call(this,ol),void((e=cl.call(this,1,!0))>0&&ul.call(this,6-e))):void this.error()}var dl,ml={name:"UnicodeRange",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return el(this.scanner.source,e,117)||this.error("U is expected"),el(this.scanner.source,e+1,ol)||this.error("Plus sign is expected"),this.scanner.next(),pl.call(this),{type:"UnicodeRange",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.value)}},gl=Le.isWhiteSpace,fl=Le.cmpStr,bl=Le.TYPE,yl=bl.Function,kl=bl.Url,vl=bl.RightParenthesis,xl={name:"Url",structure:{value:["String","Raw"]},parse:function(){var e,t=this.scanner.tokenStart;switch(this.scanner.tokenType){case kl:for(var n=t+4,r=this.scanner.tokenEnd-1;n<r&&gl(this.scanner.source.charCodeAt(n));)n++;for(;n<r&&gl(this.scanner.source.charCodeAt(r-1));)r--;e={type:"Raw",loc:this.getLocation(n,r),value:this.scanner.source.substring(n,r)},this.eat(kl);break;case yl:fl(this.scanner.source,this.scanner.tokenStart,this.scanner.tokenEnd,"url(")||this.error("Function name must be `url`"),this.eat(yl),this.scanner.skipSC(),e=this.String(),this.scanner.skipSC(),this.eat(vl);break;default:this.error("Url or Function is expected")}return{type:"Url",loc:this.getLocation(t,this.scanner.tokenStart),value:e}},generate:function(e){this.chunk("url"),this.chunk("("),this.node(e.value),this.chunk(")")}},wl=Le.TYPE.WhiteSpace,Sl=Object.freeze({type:"WhiteSpace",loc:null,value:" "}),Cl={AnPlusB:qi,Atrule:oa,AtrulePrelude:ua,AttributeSelector:za,Block:_a,Brackets:qa,CDC:Ua,CDO:Va,ClassSelector:Ka,Combinator:Xa,Comment:$a,Declaration:yo,DeclarationList:Po,Dimension:Eo,Function:Do,HexColor:Io,Identifier:Mo,IdSelector:_o,MediaFeature:Go,MediaQuery:Jo,MediaQueryList:ts,Nth:{name:"Nth",structure:{nth:["AnPlusB","Identifier"],selector:["SelectorList",null]},parse:function(e){this.scanner.skipSC();var t,n=this.scanner.tokenStart,r=n,i=null;return t=this.scanner.lookupValue(0,"odd")||this.scanner.lookupValue(0,"even")?this.Identifier():this.AnPlusB(),this.scanner.skipSC(),e&&this.scanner.lookupValue(0,"of")?(this.scanner.next(),i=this.SelectorList(),this.needPositions&&(r=this.getLastListNode(i.children).loc.end.offset)):this.needPositions&&(r=t.loc.end.offset),{type:"Nth",loc:this.getLocation(n,r),nth:t,selector:i}},generate:function(e){this.node(e.nth),null!==e.selector&&(this.chunk(" of "),this.node(e.selector))}},Number:rs,Operator:is,Parentheses:ls,Percentage:hs,PseudoClassSelector:bs,PseudoElementSelector:Ss,Ratio:Es,Raw:Zi,Rule:Ms,Selector:{name:"Selector",structure:{children:[["TypeSelector","IdSelector","ClassSelector","AttributeSelector","PseudoClassSelector","PseudoElementSelector","Combinator","WhiteSpace"]]},parse:function(){var e=this.readSequence(this.scope.Selector);return null===this.getFirstListNode(e)&&this.error("Selector is expected"),{type:"Selector",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e)}},SelectorList:_s,String:Fs,StyleSheet:Ks,TypeSelector:$s,UnicodeRange:ml,Url:xl,Value:{name:"Value",structure:{children:[[]]},parse:function(){var e=this.scanner.tokenStart,t=this.readSequence(this.scope.Value);return{type:"Value",loc:this.getLocation(e,this.scanner.tokenStart),children:t}},generate:function(e){this.children(e)}},WhiteSpace:{name:"WhiteSpace",structure:{value:String},parse:function(){return this.eat(wl),Sl},generate:function(e){this.chunk(e.value)}}},zl=(dl=zi)&&dl.default||dl,Al={generic:!0,types:zl.types,properties:zl.properties,node:Cl},Pl=Le.cmpChar,Tl=Le.cmpStr,Ll=Le.TYPE,El=Ll.Ident,Ol=Ll.String,Dl=Ll.Number,Bl=Ll.Function,Il=Ll.Url,Nl=Ll.Hash,Ml=Ll.Dimension,Rl=Ll.Percentage,_l=Ll.LeftParenthesis,jl=Ll.LeftSquareBracket,Fl=Ll.Comma,Wl=Ll.Delim,ql=function(e){switch(this.scanner.tokenType){case Nl:return this.HexColor();case Fl:return e.space=null,e.ignoreWSAfter=!0,this.Operator();case _l:return this.Parentheses(this.readSequence,e.recognizer);case jl:return this.Brackets(this.readSequence,e.recognizer);case Ol:return this.String();case Ml:return this.Dimension();case Rl:return this.Percentage();case Dl:return this.Number();case Bl:return Tl(this.scanner.source,this.scanner.tokenStart,this.scanner.tokenEnd,"url(")?this.Url():this.Function(this.readSequence,e.recognizer);case Il:return this.Url();case El:return Pl(this.scanner.source,this.scanner.tokenStart,117)&&Pl(this.scanner.source,this.scanner.tokenStart+1,43)?this.UnicodeRange():this.Identifier();case Wl:var t=this.scanner.source.charCodeAt(this.scanner.tokenStart);if(47===t||42===t||43===t||45===t)return this.Operator();35===t&&this.error("Hex or identifier is expected",this.scanner.tokenStart+1)}},Yl={getNode:ql},Ul=Le.TYPE,Hl=Ul.Delim,Vl=Ul.Ident,Gl=Ul.Dimension,Kl=Ul.Percentage,Ql=Ul.Number,Xl=Ul.Hash,Zl=Ul.Colon,$l=Ul.LeftSquareBracket,Jl=35,ec=42,tc=43,nc=47,rc=46,ic=62,ac=124,oc=126;var sc={getNode:function(e){switch(this.scanner.tokenType){case $l:return this.AttributeSelector();case Xl:return this.IdSelector();case Zl:return this.scanner.lookupType(1)===Zl?this.PseudoElementSelector():this.PseudoClassSelector();case Vl:return this.TypeSelector();case Ql:case Kl:return this.Percentage();case Gl:this.scanner.source.charCodeAt(this.scanner.tokenStart)===rc&&this.error("Identifier is expected",this.scanner.tokenStart+1);break;case Hl:switch(this.scanner.source.charCodeAt(this.scanner.tokenStart)){case tc:case ic:case oc:return e.space=null,e.ignoreWSAfter=!0,this.Combinator();case nc:return this.Combinator();case rc:return this.ClassSelector();case ec:case ac:return this.TypeSelector();case Jl:return this.IdSelector()}}}},lc=function(){this.scanner.skipSC();var e=this.createSingleNodeList(this.IdSelector());return this.scanner.skipSC(),e},cc=Le.TYPE,uc=Zi.mode,hc=cc.Comma,pc={AtrulePrelude:Yl,Selector:sc,Value:{getNode:ql,"-moz-element":lc,element:lc,expression:function(){return this.createSingleNodeList(this.Raw(this.scanner.tokenIndex,null,!1))},var:function(){var e=this.createList();return this.scanner.skipSC(),e.push(this.Identifier()),this.scanner.skipSC(),this.scanner.tokenType===hc&&(e.push(this.Operator()),e.push(this.parseCustomProperty?this.Value(null):this.Raw(this.scanner.tokenIndex,uc.exclamationMarkOrSemicolon,!1))),e}}},dc=Le.TYPE,mc=dc.String,gc=dc.Ident,fc=dc.Url,bc=dc.Function,yc=dc.LeftParenthesis,kc={parse:{prelude:function(){var e=this.createList();switch(this.scanner.skipSC(),this.scanner.tokenType){case mc:e.push(this.String());break;case fc:case bc:e.push(this.Url());break;default:this.error("String or url() is expected")}return this.lookupNonWSType(0)!==gc&&this.lookupNonWSType(0)!==yc||(e.push(this.WhiteSpace()),e.push(this.MediaQueryList())),e},block:null}},vc=Le.TYPE,xc=vc.WhiteSpace,wc=vc.Comment,Sc=vc.Ident,Cc=vc.Function,zc=vc.Colon,Ac=vc.LeftParenthesis;function Pc(){return this.createSingleNodeList(this.Raw(this.scanner.tokenIndex,null,!1))}function Tc(){return this.scanner.skipSC(),this.scanner.tokenType===Sc&&this.lookupNonWSType(1)===zc?this.createSingleNodeList(this.Declaration()):Lc.call(this)}function Lc(){var e,t=this.createList(),n=null;this.scanner.skipSC();e:for(;!this.scanner.eof;){switch(this.scanner.tokenType){case xc:n=this.WhiteSpace();continue;case wc:this.scanner.next();continue;case Cc:e=this.Function(Pc,this.scope.AtrulePrelude);break;case Sc:e=this.Identifier();break;case Ac:e=this.Parentheses(Tc,this.scope.AtrulePrelude);break;default:break e}null!==n&&(t.push(n),n=null),t.push(e)}return t}var Ec={parse:function(){return this.createSingleNodeList(this.SelectorList())}},Oc={parse:function(){return this.createSingleNodeList(this.Nth(!0))}},Dc={parse:function(){return this.createSingleNodeList(this.Nth(!1))}};return xi(function(){for(var e={},t=0;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}(Al,{parseContext:{default:"StyleSheet",stylesheet:"StyleSheet",atrule:"Atrule",atrulePrelude:function(e){return this.AtrulePrelude(e.atrule?String(e.atrule):null)},mediaQueryList:"MediaQueryList",mediaQuery:"MediaQuery",rule:"Rule",selectorList:"SelectorList",selector:"Selector",block:function(){return this.Block(!0)},declarationList:"DeclarationList",declaration:"Declaration",value:"Value"},scope:pc,atrule:{"font-face":{parse:{prelude:null,block:function(){return this.Block(!0)}}},import:kc,media:{parse:{prelude:function(){return this.createSingleNodeList(this.MediaQueryList())},block:function(){return this.Block(!1)}}},page:{parse:{prelude:function(){return this.createSingleNodeList(this.SelectorList())},block:function(){return this.Block(!0)}}},supports:{parse:{prelude:function(){var e=Lc.call(this);return null===this.getFirstListNode(e)&&this.error("Condition is expected"),e},block:function(){return this.Block(!1)}}}},pseudo:{dir:{parse:function(){return this.createSingleNodeList(this.Identifier())}},has:{parse:function(){return this.createSingleNodeList(this.SelectorList())}},lang:{parse:function(){return this.createSingleNodeList(this.Identifier())}},matches:Ec,not:Ec,"nth-child":Oc,"nth-last-child":Oc,"nth-last-of-type":Dc,"nth-of-type":Dc,slotted:{parse:function(){return this.createSingleNodeList(this.Selector())}}},node:Cl},{node:Cl}))}));
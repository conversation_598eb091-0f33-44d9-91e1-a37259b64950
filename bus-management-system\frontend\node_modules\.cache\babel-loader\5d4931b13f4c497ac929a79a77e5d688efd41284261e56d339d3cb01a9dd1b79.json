{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FolderOutput = createLucideIcon(\"FolderOutput\", [[\"path\", {\n  d: \"M2 7.5V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H20a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H2\",\n  key: \"jm8npq\"\n}], [\"path\", {\n  d: \"M2 13h10\",\n  key: \"pgb2dq\"\n}], [\"path\", {\n  d: \"m5 10-3 3 3 3\",\n  key: \"1r8ie0\"\n}]]);\nexport { FolderOutput as default };", "map": {"version": 3, "names": ["FolderOutput", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\folder-output.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FolderOutput\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiA3LjVWNWMwLTEuMS45LTIgMi0yaDMuOTNhMiAyIDAgMCAxIDEuNjYuOWwuODIgMS4yYTIgMiAwIDAgMCAxLjY2LjlIMjBhMiAyIDAgMCAxIDIgMnYxMGEyIDIgMCAwIDEtMiAySDIiIC8+CiAgPHBhdGggZD0iTTIgMTNoMTAiIC8+CiAgPHBhdGggZD0ibTUgMTAtMyAzIDMgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/folder-output\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FolderOutput = createLucideIcon('FolderOutput', [\n  [\n    'path',\n    {\n      d: 'M2 7.5V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H20a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H2',\n      key: 'jm8npq',\n    },\n  ],\n  ['path', { d: 'M2 13h10', key: 'pgb2dq' }],\n  ['path', { d: 'm5 10-3 3 3 3', key: '1r8ie0' }],\n]);\n\nexport default FolderOutput;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Mic2 = createLucideIcon(\"Mic2\", [[\"path\", {\n  d: \"m12 8-9.04 9.06a2.82 2.82 0 1 0 3.98 3.98L16 12\",\n  key: \"zoua8r\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"7\",\n  r: \"5\",\n  key: \"1fomce\"\n}]]);\nexport { Mic2 as default };", "map": {"version": 3, "names": ["Mic2", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\mic-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mic2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgOC05LjA0IDkuMDZhMi44MiAyLjgyIDAgMSAwIDMuOTggMy45OEwxNiAxMiIgLz4KICA8Y2lyY2xlIGN4PSIxNyIgY3k9IjciIHI9IjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/mic-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mic2 = createLucideIcon('Mic2', [\n  [\n    'path',\n    { d: 'm12 8-9.04 9.06a2.82 2.82 0 1 0 3.98 3.98L16 12', key: 'zoua8r' },\n  ],\n  ['circle', { cx: '17', cy: '7', r: '5', key: '1fomce' }],\n]);\n\nexport default Mic2;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CACE,QACA;EAAEC,CAAA,EAAG,iDAAmD;EAAAC,GAAA,EAAK;AAAS,EACxE,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
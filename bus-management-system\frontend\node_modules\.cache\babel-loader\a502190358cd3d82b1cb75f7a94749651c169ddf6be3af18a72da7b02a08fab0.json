{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CornerLeftDown = createLucideIcon(\"CornerLeftDown\", [[\"polyline\", {\n  points: \"14 15 9 20 4 15\",\n  key: \"nkc4i\"\n}], [\"path\", {\n  d: \"M20 4h-7a4 4 0 0 0-4 4v12\",\n  key: \"nbpdq2\"\n}]]);\nexport { CornerLeftDown as default };", "map": {"version": 3, "names": ["CornerLeftDown", "createLucideIcon", "points", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\corner-left-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CornerLeftDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxNCAxNSA5IDIwIDQgMTUiIC8+CiAgPHBhdGggZD0iTTIwIDRoLTdhNCA0IDAgMCAwLTQgNHYxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/corner-left-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CornerLeftDown = createLucideIcon('CornerLeftDown', [\n  ['polyline', { points: '14 15 9 20 4 15', key: 'nkc4i' }],\n  ['path', { d: 'M20 4h-7a4 4 0 0 0-4 4v12', key: 'nbpdq2' }],\n]);\n\nexport default CornerLeftDown;\n"], "mappings": ";;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,UAAY;EAAEC,MAAA,EAAQ,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAS,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n\n// TYPES\n// FUNCTIONS\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state\n  };\n} // Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\n\nfunction dehydrateQuery(query) {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash\n  };\n}\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === 'success';\n}\nexport function dehydrate(client, options) {\n  var _options, _options2;\n  options = options || {};\n  var mutations = [];\n  var queries = [];\n  if (((_options = options) == null ? void 0 : _options.dehydrateMutations) !== false) {\n    var shouldDehydrateMutation = options.shouldDehydrateMutation || defaultShouldDehydrateMutation;\n    client.getMutationCache().getAll().forEach(function (mutation) {\n      if (shouldDehydrateMutation(mutation)) {\n        mutations.push(dehydrateMutation(mutation));\n      }\n    });\n  }\n  if (((_options2 = options) == null ? void 0 : _options2.dehydrateQueries) !== false) {\n    var shouldDehydrateQuery = options.shouldDehydrateQuery || defaultShouldDehydrateQuery;\n    client.getQueryCache().getAll().forEach(function (query) {\n      if (shouldDehydrateQuery(query)) {\n        queries.push(dehydrateQuery(query));\n      }\n    });\n  }\n  return {\n    mutations: mutations,\n    queries: queries\n  };\n}\nexport function hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return;\n  }\n  var mutationCache = client.getMutationCache();\n  var queryCache = client.getQueryCache();\n  var mutations = dehydratedState.mutations || [];\n  var queries = dehydratedState.queries || [];\n  mutations.forEach(function (dehydratedMutation) {\n    var _options$defaultOptio;\n    mutationCache.build(client, _extends({}, options == null ? void 0 : (_options$defaultOptio = options.defaultOptions) == null ? void 0 : _options$defaultOptio.mutations, {\n      mutationKey: dehydratedMutation.mutationKey\n    }), dehydratedMutation.state);\n  });\n  queries.forEach(function (dehydratedQuery) {\n    var _options$defaultOptio2;\n    var query = queryCache.get(dehydratedQuery.queryHash); // Do not hydrate if an existing query exists with newer data\n\n    if (query) {\n      if (query.state.dataUpdatedAt < dehydratedQuery.state.dataUpdatedAt) {\n        query.setState(dehydratedQuery.state);\n      }\n      return;\n    } // Restore query\n\n    queryCache.build(client, _extends({}, options == null ? void 0 : (_options$defaultOptio2 = options.defaultOptions) == null ? void 0 : _options$defaultOptio2.queries, {\n      queryKey: dehydratedQuery.queryKey,\n      queryHash: dehydratedQuery.queryHash\n    }), dehydratedQuery.state);\n  });\n}", "map": {"version": 3, "names": ["_extends", "dehydrateMutation", "mutation", "<PERSON><PERSON><PERSON>", "options", "state", "dehydrate<PERSON><PERSON>y", "query", "query<PERSON><PERSON>", "queryHash", "defaultShouldDehydrateMutation", "isPaused", "defaultShouldDehydrateQuery", "status", "dehydrate", "client", "_options", "_options2", "mutations", "queries", "dehydrateMutations", "shouldDehydrateMutation", "getMutationCache", "getAll", "for<PERSON>ach", "push", "dehydrateQueries", "shouldDehydrateQuery", "get<PERSON><PERSON><PERSON><PERSON>ache", "hydrate", "dehydratedState", "mutationCache", "queryCache", "dehydratedMutation", "_options$defaultOptio", "build", "defaultOptions", "dehydrated<PERSON><PERSON>y", "_options$defaultOptio2", "get", "dataUpdatedAt", "setState"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/core/hydration.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n\n// TYPES\n// FUNCTIONS\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state\n  };\n} // Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\n\n\nfunction dehydrateQuery(query) {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash\n  };\n}\n\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\n\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === 'success';\n}\n\nexport function dehydrate(client, options) {\n  var _options, _options2;\n\n  options = options || {};\n  var mutations = [];\n  var queries = [];\n\n  if (((_options = options) == null ? void 0 : _options.dehydrateMutations) !== false) {\n    var shouldDehydrateMutation = options.shouldDehydrateMutation || defaultShouldDehydrateMutation;\n    client.getMutationCache().getAll().forEach(function (mutation) {\n      if (shouldDehydrateMutation(mutation)) {\n        mutations.push(dehydrateMutation(mutation));\n      }\n    });\n  }\n\n  if (((_options2 = options) == null ? void 0 : _options2.dehydrateQueries) !== false) {\n    var shouldDehydrateQuery = options.shouldDehydrateQuery || defaultShouldDehydrateQuery;\n    client.getQueryCache().getAll().forEach(function (query) {\n      if (shouldDehydrateQuery(query)) {\n        queries.push(dehydrateQuery(query));\n      }\n    });\n  }\n\n  return {\n    mutations: mutations,\n    queries: queries\n  };\n}\nexport function hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return;\n  }\n\n  var mutationCache = client.getMutationCache();\n  var queryCache = client.getQueryCache();\n  var mutations = dehydratedState.mutations || [];\n  var queries = dehydratedState.queries || [];\n  mutations.forEach(function (dehydratedMutation) {\n    var _options$defaultOptio;\n\n    mutationCache.build(client, _extends({}, options == null ? void 0 : (_options$defaultOptio = options.defaultOptions) == null ? void 0 : _options$defaultOptio.mutations, {\n      mutationKey: dehydratedMutation.mutationKey\n    }), dehydratedMutation.state);\n  });\n  queries.forEach(function (dehydratedQuery) {\n    var _options$defaultOptio2;\n\n    var query = queryCache.get(dehydratedQuery.queryHash); // Do not hydrate if an existing query exists with newer data\n\n    if (query) {\n      if (query.state.dataUpdatedAt < dehydratedQuery.state.dataUpdatedAt) {\n        query.setState(dehydratedQuery.state);\n      }\n\n      return;\n    } // Restore query\n\n\n    queryCache.build(client, _extends({}, options == null ? void 0 : (_options$defaultOptio2 = options.defaultOptions) == null ? void 0 : _options$defaultOptio2.queries, {\n      queryKey: dehydratedQuery.queryKey,\n      queryHash: dehydratedQuery.queryHash\n    }), dehydratedQuery.state);\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;;AAEzD;AACA;AACA,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACnC,OAAO;IACLC,WAAW,EAAED,QAAQ,CAACE,OAAO,CAACD,WAAW;IACzCE,KAAK,EAAEH,QAAQ,CAACG;EAClB,CAAC;AACH,CAAC,CAAC;AACF;AACA;AACA;;AAGA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAO;IACLF,KAAK,EAAEE,KAAK,CAACF,KAAK;IAClBG,QAAQ,EAAED,KAAK,CAACC,QAAQ;IACxBC,SAAS,EAAEF,KAAK,CAACE;EACnB,CAAC;AACH;AAEA,SAASC,8BAA8BA,CAACR,QAAQ,EAAE;EAChD,OAAOA,QAAQ,CAACG,KAAK,CAACM,QAAQ;AAChC;AAEA,SAASC,2BAA2BA,CAACL,KAAK,EAAE;EAC1C,OAAOA,KAAK,CAACF,KAAK,CAACQ,MAAM,KAAK,SAAS;AACzC;AAEA,OAAO,SAASC,SAASA,CAACC,MAAM,EAAEX,OAAO,EAAE;EACzC,IAAIY,QAAQ,EAAEC,SAAS;EAEvBb,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIc,SAAS,GAAG,EAAE;EAClB,IAAIC,OAAO,GAAG,EAAE;EAEhB,IAAI,CAAC,CAACH,QAAQ,GAAGZ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGY,QAAQ,CAACI,kBAAkB,MAAM,KAAK,EAAE;IACnF,IAAIC,uBAAuB,GAAGjB,OAAO,CAACiB,uBAAuB,IAAIX,8BAA8B;IAC/FK,MAAM,CAACO,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUtB,QAAQ,EAAE;MAC7D,IAAImB,uBAAuB,CAACnB,QAAQ,CAAC,EAAE;QACrCgB,SAAS,CAACO,IAAI,CAACxB,iBAAiB,CAACC,QAAQ,CAAC,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ;EAEA,IAAI,CAAC,CAACe,SAAS,GAAGb,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGa,SAAS,CAACS,gBAAgB,MAAM,KAAK,EAAE;IACnF,IAAIC,oBAAoB,GAAGvB,OAAO,CAACuB,oBAAoB,IAAIf,2BAA2B;IACtFG,MAAM,CAACa,aAAa,CAAC,CAAC,CAACL,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUjB,KAAK,EAAE;MACvD,IAAIoB,oBAAoB,CAACpB,KAAK,CAAC,EAAE;QAC/BY,OAAO,CAACM,IAAI,CAACnB,cAAc,CAACC,KAAK,CAAC,CAAC;MACrC;IACF,CAAC,CAAC;EACJ;EAEA,OAAO;IACLW,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA;EACX,CAAC;AACH;AACA,OAAO,SAASU,OAAOA,CAACd,MAAM,EAAEe,eAAe,EAAE1B,OAAO,EAAE;EACxD,IAAI,OAAO0B,eAAe,KAAK,QAAQ,IAAIA,eAAe,KAAK,IAAI,EAAE;IACnE;EACF;EAEA,IAAIC,aAAa,GAAGhB,MAAM,CAACO,gBAAgB,CAAC,CAAC;EAC7C,IAAIU,UAAU,GAAGjB,MAAM,CAACa,aAAa,CAAC,CAAC;EACvC,IAAIV,SAAS,GAAGY,eAAe,CAACZ,SAAS,IAAI,EAAE;EAC/C,IAAIC,OAAO,GAAGW,eAAe,CAACX,OAAO,IAAI,EAAE;EAC3CD,SAAS,CAACM,OAAO,CAAC,UAAUS,kBAAkB,EAAE;IAC9C,IAAIC,qBAAqB;IAEzBH,aAAa,CAACI,KAAK,CAACpB,MAAM,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEI,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC8B,qBAAqB,GAAG9B,OAAO,CAACgC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAAChB,SAAS,EAAE;MACvKf,WAAW,EAAE8B,kBAAkB,CAAC9B;IAClC,CAAC,CAAC,EAAE8B,kBAAkB,CAAC5B,KAAK,CAAC;EAC/B,CAAC,CAAC;EACFc,OAAO,CAACK,OAAO,CAAC,UAAUa,eAAe,EAAE;IACzC,IAAIC,sBAAsB;IAE1B,IAAI/B,KAAK,GAAGyB,UAAU,CAACO,GAAG,CAACF,eAAe,CAAC5B,SAAS,CAAC,CAAC,CAAC;;IAEvD,IAAIF,KAAK,EAAE;MACT,IAAIA,KAAK,CAACF,KAAK,CAACmC,aAAa,GAAGH,eAAe,CAAChC,KAAK,CAACmC,aAAa,EAAE;QACnEjC,KAAK,CAACkC,QAAQ,CAACJ,eAAe,CAAChC,KAAK,CAAC;MACvC;MAEA;IACF,CAAC,CAAC;;IAGF2B,UAAU,CAACG,KAAK,CAACpB,MAAM,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEI,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACkC,sBAAsB,GAAGlC,OAAO,CAACgC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,sBAAsB,CAACnB,OAAO,EAAE;MACpKX,QAAQ,EAAE6B,eAAe,CAAC7B,QAAQ;MAClCC,SAAS,EAAE4B,eAAe,CAAC5B;IAC7B,CAAC,CAAC,EAAE4B,eAAe,CAAChC,KAAK,CAAC;EAC5B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": 3, "file": "watchdog.js", "sourceRoot": "", "sources": ["../../src/watchdog.ts"], "names": [], "mappings": "AAAA,+DAA+D;AAC/D,gEAAgE;AAChE,uCAAuC;AAEvC,OAAO,EAAgB,KAAK,EAAE,MAAM,eAAe,CAAA;AAEnD,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;CAqB9B,CAAA;AAED;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,KAAmB,EAAE,EAAE;IAC9C,IAAI,SAAS,GAAG,KAAK,CAAA;IACrB,MAAM,GAAG,GAAG,KAAK,CACf,OAAO,CAAC,QAAQ,EAChB,CAAC,IAAI,EAAE,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EACvC;QACE,KAAK,EAAE,QAAQ;KAChB,CACF,CAAA;IACD,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;IACxC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;QACpB,IAAI,CAAC,SAAS;YAAE,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACrC,CAAC,CAAC,CAAA;IACF,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA", "sourcesContent": ["// this spawns a child process that listens for SIGH<PERSON> when the\n// parent process exits, and after 200ms, sends a SIGKILL to the\n// child, in case it did not terminate.\n\nimport { ChildProcess, spawn } from 'child_process'\n\nconst watchdogCode = String.raw`\nconst pid = parseInt(process.argv[1], 10)\nprocess.title = 'node (foreground-child watchdog pid=' + pid + ')'\nif (!isNaN(pid)) {\n  let barked = false\n  // keepalive\n  const interval = setInterval(() => {}, 60000)\n  const bark = () => {\n    clearInterval(interval)\n    if (barked) return\n    barked = true\n    process.removeListener('SIGHUP', bark)\n    setTimeout(() => {\n      try {\n        process.kill(pid, 'SIGKILL')\n        setTimeout(() => process.exit(), 200)\n      } catch (_) {}\n    }, 500)\n  })\n  process.on('SIGHUP', bark)\n}\n`\n\n/**\n * Pass in a ChildProcess, and this will spawn a watchdog process that\n * will make sure it exits if the parent does, thus preventing any\n * dangling detached zombie processes.\n *\n * If the child ends before the parent, then the watchdog will terminate.\n */\nexport const watchdog = (child: ChildProcess) => {\n  let dogExited = false\n  const dog = spawn(\n    process.execPath,\n    ['-e', watchdogCode, String(child.pid)],\n    {\n      stdio: 'ignore',\n    },\n  )\n  dog.on('exit', () => (dogExited = true))\n  child.on('exit', () => {\n    if (!dogExited) dog.kill('SIGKILL')\n  })\n  return dog\n}\n"]}
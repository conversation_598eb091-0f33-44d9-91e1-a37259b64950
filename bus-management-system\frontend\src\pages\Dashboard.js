import React from 'react';
import { useQuery } from 'react-query';
import { dashboardAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import {
  Bus, Users, UserCheck, AlertTriangle,
  TrendingUp, Clock, MapPin, DollarSign
} from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';

const Dashboard = () => {
  const { user, isAdmin, isDriver, isStudent } = useAuth();

  // Fetch dashboard stats
  const { data: statsData, isLoading } = useQuery(
    'dashboard-stats',
    dashboardAPI.getStats
  );

  if (isLoading) return <LoadingSpinner text="Loading dashboard..." />;

  const stats = statsData?.data?.data || {};

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold">
          Welcome back, {user?.first_name}!
        </h1>
        <p className="text-primary-100 mt-1">
          {isAdmin() && "Manage your bus fleet with AI-powered insights"}
          {isDriver() && "Your daily operations dashboard"}
          {isStudent() && "Track your bus and attendance"}
        </p>
      </div>

      {/* Admin Dashboard */}
      {isAdmin() && (
        <AdminDashboard stats={stats} />
      )}

      {/* Driver Dashboard */}
      {isDriver() && (
        <DriverDashboard stats={stats} />
      )}

      {/* Student Dashboard */}
      {isStudent() && (
        <StudentDashboard stats={stats} />
      )}
    </div>
  );
};

const AdminDashboard = ({ stats }) => {
  const quickStats = [
    {
      name: 'Total Buses',
      value: stats.total_buses || 0,
      icon: Bus,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: '+2 this month',
    },
    {
      name: 'Active Drivers',
      value: stats.total_drivers || 0,
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: '+1 this week',
    },
    {
      name: 'Total Students',
      value: stats.total_students || 0,
      icon: UserCheck,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: '+15 this month',
    },
    {
      name: 'Active Routes',
      value: stats.total_routes || 0,
      icon: MapPin,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      change: 'No change',
    },
  ];

  const alerts = [
    {
      type: 'warning',
      message: '3 buses have insurance expiring within 30 days',
      icon: AlertTriangle,
    },
    {
      type: 'info',
      message: 'Route optimization suggestions available',
      icon: TrendingUp,
    },
    {
      type: 'warning',
      message: '2 drivers have license expiring soon',
      icon: Clock,
    },
  ];

  return (
    <>
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-xs text-gray-500">{stat.change}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alerts */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">System Alerts</h3>
          <div className="space-y-3">
            {alerts.map((alert, index) => {
              const Icon = alert.icon;
              return (
                <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Icon className={`h-5 w-5 ${
                    alert.type === 'warning' ? 'text-yellow-600' : 'text-blue-600'
                  }`} />
                  <span className="text-sm text-gray-700">{alert.message}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-700">Bus KA01AB1234 completed morning route</span>
              <span className="text-xs text-gray-500">2 min ago</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-700">New student registered: John Doe</span>
              <span className="text-xs text-gray-500">15 min ago</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-sm text-gray-700">Maintenance scheduled for Bus KA01CD5678</span>
              <span className="text-xs text-gray-500">1 hour ago</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

const DriverDashboard = ({ stats }) => {
  const driverStats = [
    {
      name: 'Today\'s Trips',
      value: stats.todays_trips || 0,
      icon: Bus,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: 'Students Transported',
      value: stats.students_transported || 0,
      icon: UserCheck,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: 'On-Time Performance',
      value: `${stats.on_time_percentage || 95}%`,
      icon: Clock,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      name: 'Distance Covered',
      value: `${stats.distance_covered || 0} km`,
      icon: MapPin,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ];

  return (
    <>
      {/* Driver Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {driverStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Schedule */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Today's Schedule</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
              <div>
                <p className="font-medium text-green-900">Morning Route</p>
                <p className="text-sm text-green-700">City Center → School</p>
              </div>
              <span className="badge badge-success">Completed</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
              <div>
                <p className="font-medium text-blue-900">Evening Route</p>
                <p className="text-sm text-blue-700">School → City Center</p>
              </div>
              <span className="badge badge-primary">Upcoming</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full btn btn-primary text-left">
              Start Trip
            </button>
            <button className="w-full btn btn-outline text-left">
              Mark Attendance
            </button>
            <button className="w-full btn btn-outline text-left">
              Report Issue
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

const StudentDashboard = ({ stats }) => {
  const studentStats = [
    {
      name: 'Attendance Rate',
      value: `${stats.attendance_percentage || 95}%`,
      icon: UserCheck,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: 'Monthly Fee',
      value: `₹${stats.monthly_fee || 2000}`,
      icon: DollarSign,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: 'Bus Status',
      value: stats.bus_status || 'On Route',
      icon: Bus,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      name: 'ETA',
      value: stats.eta || '15 min',
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ];

  return (
    <>
      {/* Student Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {studentStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Bus Tracking */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Live Bus Tracking</h3>
          <div className="bg-gray-100 rounded-lg h-48 flex items-center justify-center">
            <div className="text-center">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">Bus location will appear here</p>
            </div>
          </div>
        </div>

        {/* Recent Trips */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Trips</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Morning Trip</p>
                <p className="text-sm text-gray-600">Today, 7:30 AM</p>
              </div>
              <span className="badge badge-success">Present</span>
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Evening Trip</p>
                <p className="text-sm text-gray-600">Yesterday, 3:30 PM</p>
              </div>
              <span className="badge badge-success">Present</span>
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Morning Trip</p>
                <p className="text-sm text-gray-600">Yesterday, 7:30 AM</p>
              </div>
              <span className="badge badge-danger">Absent</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Dashboard;

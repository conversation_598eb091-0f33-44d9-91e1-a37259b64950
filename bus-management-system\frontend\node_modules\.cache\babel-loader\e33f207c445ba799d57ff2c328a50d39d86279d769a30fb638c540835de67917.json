{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Gavel = createLucideIcon(\"Gavel\", [[\"path\", {\n  d: \"m14 13-7.5 7.5c-.83.83-2.17.83-3 0 0 0 0 0 0 0a2.12 2.12 0 0 1 0-3L11 10\",\n  key: \"c9cbz0\"\n}], [\"path\", {\n  d: \"m16 16 6-6\",\n  key: \"vzrcl6\"\n}], [\"path\", {\n  d: \"m8 8 6-6\",\n  key: \"18bi4p\"\n}], [\"path\", {\n  d: \"m9 7 8 8\",\n  key: \"5jnvq1\"\n}], [\"path\", {\n  d: \"m21 11-8-8\",\n  key: \"z4y7zo\"\n}]]);\nexport { Gavel as default };", "map": {"version": 3, "names": ["Gavel", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\gavel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Gavel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTQgMTMtNy41IDcuNWMtLjgzLjgzLTIuMTcuODMtMyAwIDAgMCAwIDAgMCAwYTIuMTIgMi4xMiAwIDAgMSAwLTNMMTEgMTAiIC8+CiAgPHBhdGggZD0ibTE2IDE2IDYtNiIgLz4KICA8cGF0aCBkPSJtOCA4IDYtNiIgLz4KICA8cGF0aCBkPSJtOSA3IDggOCIgLz4KICA8cGF0aCBkPSJtMjEgMTEtOC04IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/gavel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Gavel = createLucideIcon('Gavel', [\n  [\n    'path',\n    {\n      d: 'm14 13-7.5 7.5c-.83.83-2.17.83-3 0 0 0 0 0 0 0a2.12 2.12 0 0 1 0-3L11 10',\n      key: 'c9cbz0',\n    },\n  ],\n  ['path', { d: 'm16 16 6-6', key: 'vzrcl6' }],\n  ['path', { d: 'm8 8 6-6', key: '18bi4p' }],\n  ['path', { d: 'm9 7 8 8', key: '5jnvq1' }],\n  ['path', { d: 'm21 11-8-8', key: 'z4y7zo' }],\n]);\n\nexport default Gavel;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
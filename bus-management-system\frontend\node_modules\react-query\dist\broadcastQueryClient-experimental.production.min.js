!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e=e||self).ReactQueryBroadcastQueryClientExperimental={})}(this,(function(e){"use strict";function n(e){return e||(e=0),new Promise((function(n){return setTimeout(n,e)}))}function t(){return Math.random().toString(36).substring(2)}var r=0,o=0;function s(){var e=(new Date).getTime();return e===r?1e3*e+ ++o:(r=e,o=0,1e3*e)}var i="[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0);var a={create:function(e){var n={messagesCallback:null,bc:new BroadcastChannel(e),subFns:[]};return n.bc.onmessage=function(e){n.messagesCallback&&n.messagesCallback(e.data)},n},close:function(e){e.bc.close(),e.subFns=[]},onMessage:function(e,n){e.messagesCallback=n},postMessage:function(e,n){e.bc.postMessage(n,!1)},canBeUsed:function(){if(i&&"undefined"==typeof window)return!1;if("function"==typeof BroadcastChannel){if(BroadcastChannel._pubkey)throw new Error("BroadcastChannel: Do not overwrite window.BroadcastChannel with this module, this is not a polyfill");return!0}return!1},type:"native",averageResponseTime:function(){return 150},microSeconds:s},u=function(e){var n=new Set,t=new Map;this.has=n.has.bind(n),this.add=function(r){t.set(r,c()),n.add(r),function(){var r=c()-e,o=n[Symbol.iterator]();for(;;){var s=o.next().value;if(!s)return;if(!(t.get(s)<r))return;t.delete(s),n.delete(s)}}()},this.clear=function(){n.clear(),t.clear()}};function c(){return(new Date).getTime()}function d(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=JSON.parse(JSON.stringify(e));return void 0===n.webWorkerSupport&&(n.webWorkerSupport=!0),n.idb||(n.idb={}),n.idb.ttl||(n.idb.ttl=45e3),n.idb.fallbackInterval||(n.idb.fallbackInterval=150),e.idb&&"function"==typeof e.idb.onclose&&(n.idb.onclose=e.idb.onclose),n.localstorage||(n.localstorage={}),n.localstorage.removeTimeout||(n.localstorage.removeTimeout=6e4),e.methods&&(n.methods=e.methods),n.node||(n.node={}),n.node.ttl||(n.node.ttl=12e4),void 0===n.node.useFastPath&&(n.node.useFastPath=!0),n}function l(){if("undefined"!=typeof indexedDB)return indexedDB;if("undefined"!=typeof window){if(void 0!==window.mozIndexedDB)return window.mozIndexedDB;if(void 0!==window.webkitIndexedDB)return window.webkitIndexedDB;if(void 0!==window.msIndexedDB)return window.msIndexedDB}return!1}function f(e,n){var t=e.transaction("messages").objectStore("messages"),r=[];return new Promise((function(e){(function(){try{var e=IDBKeyRange.bound(n+1,1/0);return t.openCursor(e)}catch(e){return t.openCursor()}}()).onsuccess=function(t){var o=t.target.result;o?o.value.id<n+1?o.continue(n+1):(r.push(o.value),o.continue()):e(r)}}))}function m(e,n){return function(e,n){var t=(new Date).getTime()-n,r=e.transaction("messages").objectStore("messages"),o=[];return new Promise((function(e){r.openCursor().onsuccess=function(n){var r=n.target.result;if(r){var s=r.value;if(!(s.time<t))return void e(o);o.push(s),r.continue()}else e(o)}}))}(e,n).then((function(n){return Promise.all(n.map((function(n){return function(e,n){var t=e.transaction(["messages"],"readwrite").objectStore("messages").delete(n);return new Promise((function(e){t.onsuccess=function(){return e()}}))}(e,n.id)})))}))}function h(e){return e.closed?Promise.resolve():e.messagesCallback?f(e.db,e.lastCursorId).then((function(n){return n.filter((function(e){return!!e})).map((function(n){return n.id>e.lastCursorId&&(e.lastCursorId=n.id),n})).filter((function(n){return function(e,n){return e.uuid!==n.uuid&&(!n.eMIs.has(e.id)&&!(e.data.time<n.messagesCallbackTime))}(n,e)})).sort((function(e,n){return e.time-n.time})).forEach((function(n){e.messagesCallback&&(e.eMIs.add(n.id),e.messagesCallback(n.data))})),Promise.resolve()})):Promise.resolve()}var p={create:function(e,r){return r=d(r),function(e){var n="pubkey.broadcast-channel-0-"+e,t=l().open(n,1);return t.onupgradeneeded=function(e){e.target.result.createObjectStore("messages",{keyPath:"id",autoIncrement:!0})},new Promise((function(e,n){t.onerror=function(e){return n(e)},t.onsuccess=function(){e(t.result)}}))}(e).then((function(o){var s={closed:!1,lastCursorId:0,channelName:e,options:r,uuid:t(),eMIs:new u(2*r.idb.ttl),writeBlockPromise:Promise.resolve(),messagesCallback:null,readQueuePromises:[],db:o};return o.onclose=function(){s.closed=!0,r.idb.onclose&&r.idb.onclose()},function e(t){if(t.closed)return;h(t).then((function(){return n(t.options.idb.fallbackInterval)})).then((function(){return e(t)}))}(s),s}))},close:function(e){e.closed=!0,e.db.close()},onMessage:function(e,n,t){e.messagesCallbackTime=t,e.messagesCallback=n,h(e)},postMessage:function(e,n){return e.writeBlockPromise=e.writeBlockPromise.then((function(){return function(e,n,t){var r={uuid:n,time:(new Date).getTime(),data:t},o=e.transaction(["messages"],"readwrite");return new Promise((function(e,n){o.oncomplete=function(){return e()},o.onerror=function(e){return n(e)},o.objectStore("messages").add(r)}))}(e.db,e.uuid,n)})).then((function(){var n,t;0===(n=0,t=10,Math.floor(Math.random()*(t-n+1)+n))&&m(e.db,e.options.idb.ttl)})),e.writeBlockPromise},canBeUsed:function(){return!i&&!!l()},type:"idb",averageResponseTime:function(e){return 2*e.idb.fallbackInterval},microSeconds:s};function v(){var e;if("undefined"==typeof window)return null;try{e=window.localStorage,e=window["ie8-eventlistener/storage"]||window.localStorage}catch(e){}return e}function g(e){return"pubkey.broadcastChannel-"+e}function b(){if(i)return!1;var e=v();if(!e)return!1;try{var n="__broadcastchannel_check";e.setItem(n,"works"),e.removeItem(n)}catch(e){return!1}return!0}var y={create:function(e,n){if(n=d(n),!b())throw new Error("BroadcastChannel: localstorage cannot be used");var r=t(),o=new u(n.localstorage.removeTimeout),s={channelName:e,uuid:r,eMIs:o};return s.listener=function(e,n){var t=g(e),r=function(e){e.key===t&&n(JSON.parse(e.newValue))};return window.addEventListener("storage",r),r}(e,(function(e){s.messagesCallback&&e.uuid!==r&&e.token&&!o.has(e.token)&&(e.data.time&&e.data.time<s.messagesCallbackTime||(o.add(e.token),s.messagesCallback(e.data)))})),s},close:function(e){var n;n=e.listener,window.removeEventListener("storage",n)},onMessage:function(e,n,t){e.messagesCallbackTime=t,e.messagesCallback=n},postMessage:function(e,r){return new Promise((function(o){n().then((function(){var n=g(e.channelName),s={token:t(),time:(new Date).getTime(),data:r,uuid:e.uuid},i=JSON.stringify(s);v().setItem(n,i);var a=document.createEvent("Event");a.initEvent("storage",!0,!0),a.key=n,a.newValue=i,window.dispatchEvent(a),o()}))}))},canBeUsed:b,type:"localstorage",averageResponseTime:function(){var e=navigator.userAgent.toLowerCase();return e.includes("safari")&&!e.includes("chrome")?240:120},microSeconds:s},w=s,k=new Set;var C={create:function(e){var n={name:e,messagesCallback:null};return k.add(n),n},close:function(e){k.delete(e)},onMessage:function(e,n){e.messagesCallback=n},postMessage:function(e,n){return new Promise((function(t){return setTimeout((function(){Array.from(k).filter((function(n){return n.name===e.name})).filter((function(n){return n!==e})).filter((function(e){return!!e.messagesCallback})).forEach((function(e){return e.messagesCallback(n)})),t()}),5)}))},canBeUsed:function(){return!0},type:"simulate",averageResponseTime:function(){return 5},microSeconds:w},_=[a,p,y];if(i){var P=require("../../src/methods/node.js");"function"==typeof P.canBeUsed&&_.push(P)}var M=function(e,n){var t,r,o;this.name=e,this.options=d(n),this.method=function(e){var n=[].concat(e.methods,_).filter(Boolean);if(e.type){if("simulate"===e.type)return C;var t=n.find((function(n){return n.type===e.type}));if(t)return t;throw new Error("method-type "+e.type+" not found")}e.webWorkerSupport||i||(n=n.filter((function(e){return"idb"!==e.type})));var r=n.find((function(e){return e.canBeUsed()}));if(r)return r;throw new Error("No useable methode found:"+JSON.stringify(_.map((function(e){return e.type}))))}(this.options),this._iL=!1,this._onML=null,this._addEL={message:[],internal:[]},this._befC=[],this._prepP=null,r=(t=this).method.create(t.name,t.options),(o=r)&&"function"==typeof o.then?(t._prepP=r,r.then((function(e){t._state=e}))):t._state=r};function S(e,n,t){var r={time:e.method.microSeconds(),type:n,data:t};return(e._prepP?e._prepP:Promise.resolve()).then((function(){return e.method.postMessage(e._state,r)}))}function B(e){return e._addEL.message.length>0||e._addEL.internal.length>0}function E(e,n,t){e._addEL[n].push(t),function(e){if(!e._iL&&B(e)){var n=function(n){e._addEL[n.type].forEach((function(e){n.time>=e.time&&e.fn(n.data)}))},t=e.method.microSeconds();e._prepP?e._prepP.then((function(){e._iL=!0,e.method.onMessage(e._state,n,t)})):(e._iL=!0,e.method.onMessage(e._state,n,t))}}(e)}function L(e,n,t){e._addEL[n]=e._addEL[n].filter((function(e){return e!==t})),function(e){if(e._iL&&!B(e)){e._iL=!1;var n=e.method.microSeconds();e.method.onMessage(e._state,null,n)}}(e)}M._pubkey=!0,M.prototype={postMessage:function(e){if(this.closed)throw new Error("BroadcastChannel.postMessage(): Cannot post message after channel has closed");return S(this,"message",e)},postInternal:function(e){return S(this,"internal",e)},set onmessage(e){var n={time:this.method.microSeconds(),fn:e};L(this,"message",this._onML),e&&"function"==typeof e?(this._onML=n,E(this,"message",n)):this._onML=null},addEventListener:function(e,n){E(this,e,{time:this.method.microSeconds(),fn:n})},removeEventListener:function(e,n){L(this,e,this._addEL[e].find((function(e){return e.fn===n})))},close:function(){var e=this;if(!this.closed){this.closed=!0;var n=this._prepP?this._prepP:Promise.resolve();return this._onML=null,this._addEL.message=[],n.then((function(){return Promise.all(e._befC.map((function(e){return e()})))})).then((function(){return e.method.close(e._state)}))}},get type(){return this.method.type}},e.broadcastQueryClient=function(e){var n=e.queryClient,t=e.broadcastChannel,r=!1,o=new M(void 0===t?"react-query":t,{webWorkerSupport:!1}),s=n.getQueryCache();n.getQueryCache().subscribe((function(e){var n;if(!r&&(null==e?void 0:e.query)){var t=e.query,s=t.queryHash,i=t.queryKey,a=t.state;"queryUpdated"===e.type&&"success"===(null==(n=e.action)?void 0:n.type)&&o.postMessage({type:"queryUpdated",queryHash:s,queryKey:i,state:a}),"queryRemoved"===e.type&&o.postMessage({type:"queryRemoved",queryHash:s,queryKey:i})}})),o.onmessage=function(e){(null==e?void 0:e.type)&&(r=!0,function(){var t=e.type,r=e.queryHash,o=e.queryKey,i=e.state;if("queryUpdated"===t){var a=s.get(r);if(a)return void a.setState(i);s.build(n,{queryKey:o,queryHash:r},i)}else if("queryRemoved"===t){var u=s.get(r);u&&s.remove(u)}}(),r=!1)}},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=broadcastQueryClient-experimental.production.min.js.map

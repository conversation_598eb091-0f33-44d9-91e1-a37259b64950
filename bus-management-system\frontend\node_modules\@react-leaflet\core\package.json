{"name": "@react-leaflet/core", "version": "2.1.0", "description": "React Leaflet core", "repository": {"type": "git", "url": "https://github.com/PaulLeCam/react-leaflet.git"}, "keywords": ["react-component", "react", "leaflet", "map"], "author": "<PERSON> <<EMAIL>>", "license": "Hippocratic-2.1", "bugs": {"url": "https://github.com/PaulLeCam/react-leaflet/issues"}, "homepage": "https://react-leaflet.js.org/docs/core-introduction", "type": "module", "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": "./lib/index.js"}, "files": ["lib/*"], "sideEffects": false, "peerDependencies": {"leaflet": "^1.9.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.0.21", "@types/react-dom": "^18.0.6"}, "jest": {"extensionsToTreatAsEsm": [".ts", ".tsx"], "resolver": "ts-jest-resolver", "testEnvironment": "jsdom", "transform": {"^.+\\.(t|j)sx?$": ["@swc/jest", {"root": "../.."}]}}, "scripts": {"build:clean": "del lib", "build:js": "swc src -d ./lib --config-file  ../../.swcrc", "build:types": "tsc --emitDeclarationOnly", "build": "pnpm run build:clean && pnpm run build:types && pnpm run build:js", "test:types": "tsc --noEmit", "test:unit": "cross-env NODE_ENV=test jest", "test": "pnpm run test:types && pnpm run test:unit", "start": "pnpm run test && pnpm run build"}}
# College Management System

A comprehensive full-stack College Management System built with React frontend and Flask backend.

## 🚀 Features

- **Student Management**: Register, view, update, delete students with attendance and performance tracking
- **Faculty Management**: Manage faculty, assign courses, maintain staff details
- **Course Management**: Add courses, assign faculty/students, upload materials
- **Department Management**: Manage departments, assign HODs
- **Time Table Management**: Create and update schedules
- **Exam & Results**: Schedule exams, enter marks, generate report cards
- **Fee Management**: Track fees, generate receipts, send reminders
- **Notice Board**: Post announcements with role-based access
- **Authentication**: JWT-based auth with role-based access control

## 🛠️ Tech Stack

### Frontend
- React 18
- React Router DOM
- Axios for API calls
- Tailwind CSS for styling
- React Context for state management

### Backend
- Python Flask
- SQLAlchemy ORM
- MySQL Database
- Flask-JWT-Extended for authentication
- Flask-CORS for cross-origin requests
- Flask-Migrate for database migrations

## 📁 Project Structure

```
college-management-system/
├── frontend/                 # React application
│   ├── public/
│   ├── src/
│   │   ├── components/       # Reusable components
│   │   ├── pages/           # Page components
│   │   ├── services/        # API services
│   │   ├── context/         # React contexts
│   │   ├── utils/           # Utility functions
│   │   └── App.js
│   ├── package.json
│   └── tailwind.config.js
├── backend/                  # Flask application
│   ├── app/
│   │   ├── models/          # SQLAlchemy models
│   │   ├── routes/          # API routes (blueprints)
│   │   ├── utils/           # Utility functions
│   │   └── __init__.py      # App factory
│   ├── config.py            # Configuration
│   ├── requirements.txt
│   └── run.py              # Application entry point
├── .env.example
└── README.md
```

## 🚦 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- Python 3.8+
- MySQL

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd college-management-system
   ```

2. **Backend Setup**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ```

4. **Environment Configuration**
   - Copy `.env.example` to `.env`
   - Update database and other configuration values

5. **Database Setup**
   ```bash
   cd backend
   flask db init
   flask db migrate -m "Initial migration"
   flask db upgrade
   ```

6. **Run the Applications**
   
   Backend:
   ```bash
   cd backend
   python run.py
   ```
   
   Frontend:
   ```bash
   cd frontend
   npm start
   ```

## 🌐 Deployment

### Frontend (Vercel)
1. Push code to GitHub
2. Connect repository to Vercel
3. Set environment variables
4. Deploy

### Backend (Render/Railway)
1. Push code to GitHub
2. Connect repository to Render/Railway
3. Set environment variables
4. Deploy

## 👥 User Roles

- **Admin**: Full system access
- **Faculty**: Course management, student grades, attendance
- **Student**: View courses, grades, attendance, notices

## 📝 API Documentation

The API follows RESTful conventions:
- `GET /api/students` - Get all students
- `POST /api/students` - Create new student
- `PUT /api/students/:id` - Update student
- `DELETE /api/students/:id` - Delete student

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License.

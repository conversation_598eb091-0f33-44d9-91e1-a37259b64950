# @babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression

> Rename destructuring parameter to workaround https://bugs.webkit.org/show_bug.cgi?id=220517

See our website [@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression](https://babeljs.io/docs/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression
```

or using yarn:

```sh
yarn add @babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression --dev
```

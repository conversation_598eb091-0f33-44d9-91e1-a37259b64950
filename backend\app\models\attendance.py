from datetime import datetime, date
from app import db, ma

class Attendance(db.Model):
    """Student attendance model."""
    __tablename__ = 'attendances'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    student_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON>('students.id'), nullable=False)
    course_id = db.Column(db.In<PERSON>ger, db.<PERSON>('courses.id'), nullable=False)
    faculty_id = db.Column(db.Integer, db.<PERSON>('faculty.id'), nullable=False)
    date = db.Column(db.Date, nullable=False, default=date.today)
    status = db.Column(db.<PERSON>um('Present', 'Absent', 'Late', name='attendance_status'), 
                      nullable=False, default='Present')
    remarks = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Composite unique constraint to prevent duplicate entries
    __table_args__ = (db.UniqueConstraint('student_id', 'course_id', 'date', 
                                         name='unique_student_course_date'),)
    
    def to_dict(self):
        """Convert attendance to dictionary."""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'student_name': self.student.full_name if self.student else None,
            'student_roll': self.student.roll_number if self.student else None,
            'course_id': self.course_id,
            'course_name': self.course.name if self.course else None,
            'course_code': self.course.code if self.course else None,
            'faculty_id': self.faculty_id,
            'faculty_name': self.faculty.full_name if self.faculty else None,
            'date': self.date.isoformat() if self.date else None,
            'status': self.status,
            'remarks': self.remarks,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @staticmethod
    def get_attendance_percentage(student_id, course_id):
        """Calculate attendance percentage for a student in a course."""
        total_classes = Attendance.query.filter_by(
            student_id=student_id, course_id=course_id
        ).count()
        
        if total_classes == 0:
            return 0
        
        present_classes = Attendance.query.filter_by(
            student_id=student_id, course_id=course_id, status='Present'
        ).count()
        
        return round((present_classes / total_classes) * 100, 2)
    
    def __repr__(self):
        return f'<Attendance {self.student.roll_number} - {self.course.code} - {self.date}>'

class AttendanceSchema(ma.SQLAlchemyAutoSchema):
    """Attendance serialization schema."""
    class Meta:
        model = Attendance
        load_instance = True
        include_fk = True

attendance_schema = AttendanceSchema()
attendances_schema = AttendanceSchema(many=True)

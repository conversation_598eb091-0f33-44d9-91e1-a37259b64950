# Flask Configuration
FLASK_CONFIG=development
SECRET_KEY=your-secret-key-here-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-here-change-in-production

# Database Configuration
DATABASE_URL=mysql+pymysql://username:password@localhost/college_management_dev

# For Production (example with PlanetScale)
# DATABASE_URL=mysql+pymysql://username:password@host:port/database_name?ssl_ca=/etc/ssl/cert.pem

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,https://your-frontend-domain.vercel.app

# Email Configuration (Optional - for future features)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Development Settings
FLASK_DEBUG=true

{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Axe = createLucideIcon(\"Axe\", [[\"path\", {\n  d: \"m14 12-8.5 8.5a2.12 2.12 0 1 1-3-3L11 9\",\n  key: \"csbz4o\"\n}], [\"path\", {\n  d: \"M15 13 9 7l4-4 6 6h3a8 8 0 0 1-7 7z\",\n  key: \"113wfo\"\n}]]);\nexport { Axe as default };", "map": {"version": 3, "names": ["Axe", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\axe.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Axe\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTQgMTItOC41IDguNWEyLjEyIDIuMTIgMCAxIDEtMy0zTDExIDkiIC8+CiAgPHBhdGggZD0iTTE1IDEzIDkgN2w0LTQgNiA2aDNhOCA4IDAgMCAxLTcgN3oiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/axe\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Axe = createLucideIcon('Axe', [\n  ['path', { d: 'm14 12-8.5 8.5a2.12 2.12 0 1 1-3-3L11 9', key: 'csbz4o' }],\n  ['path', { d: 'M15 13 9 7l4-4 6 6h3a8 8 0 0 1-7 7z', key: '113wfo' }],\n]);\n\nexport default Axe;\n"], "mappings": ";;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,EACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\components\\\\DocumentModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport { busesAPI } from '../services/api';\nimport { X, Upload, FileText, AlertTriangle, CheckCircle, Eye, Scan } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DocumentModal = ({\n  bus,\n  onClose\n}) => {\n  _s();\n  var _documentsData$data, _documentsData$data$d;\n  const [showUploadForm, setShowUploadForm] = useState(false);\n  const [uploadData, setUploadData] = useState({\n    document_type: 'Insurance',\n    document_name: '',\n    document_number: '',\n    issue_date: '',\n    expiry_date: '',\n    issuing_authority: ''\n  });\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isUploading, setIsUploading] = useState(false);\n  const [ocrProcessing, setOcrProcessing] = useState(false);\n  const queryClient = useQueryClient();\n\n  // Fetch bus documents\n  const {\n    data: documentsData,\n    isLoading\n  } = useQuery(['bus-documents', bus.id], () => busesAPI.getDocuments(bus.id));\n\n  // Upload document mutation\n  const uploadDocumentMutation = useMutation(formData => busesAPI.uploadDocument(bus.id, formData), {\n    onSuccess: () => {\n      queryClient.invalidateQueries(['bus-documents', bus.id]);\n      toast.success('Document uploaded successfully');\n      setShowUploadForm(false);\n      resetUploadForm();\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Failed to upload document');\n    }\n  });\n  const resetUploadForm = () => {\n    setUploadData({\n      document_type: 'Insurance',\n      document_name: '',\n      document_number: '',\n      issue_date: '',\n      expiry_date: '',\n      issuing_authority: ''\n    });\n    setSelectedFile(null);\n  };\n  const handleFileSelect = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/tiff'];\n      if (!allowedTypes.includes(file.type)) {\n        toast.error('Invalid file type. Please upload PDF, JPG, PNG, or TIFF files.');\n        return;\n      }\n\n      // Validate file size (max 16MB)\n      if (file.size > 16 * 1024 * 1024) {\n        toast.error('File size too large. Maximum size is 16MB.');\n        return;\n      }\n      setSelectedFile(file);\n\n      // Auto-fill document name if empty\n      if (!uploadData.document_name) {\n        setUploadData(prev => ({\n          ...prev,\n          document_name: file.name.split('.')[0]\n        }));\n      }\n\n      // Simulate OCR processing\n      performOCR(file);\n    }\n  };\n  const performOCR = async file => {\n    setOcrProcessing(true);\n\n    // Simulate OCR processing delay\n    setTimeout(() => {\n      // Mock OCR results based on document type\n      const ocrResults = {\n        Insurance: {\n          document_number: 'INS123456789',\n          issue_date: '2023-01-01',\n          expiry_date: '2024-01-01',\n          issuing_authority: 'National Insurance Company'\n        },\n        RC: {\n          document_number: 'RC' + bus.registration_number.replace(/\\s/g, ''),\n          issue_date: '2020-01-01',\n          expiry_date: '2025-01-01',\n          issuing_authority: 'Regional Transport Office'\n        },\n        'Fitness Certificate': {\n          document_number: 'FC' + Date.now().toString().slice(-6),\n          issue_date: '2023-01-01',\n          expiry_date: '2024-01-01',\n          issuing_authority: 'Motor Vehicle Inspector'\n        }\n      };\n      const result = ocrResults[uploadData.document_type] || {};\n      setUploadData(prev => ({\n        ...prev,\n        ...result\n      }));\n      setOcrProcessing(false);\n      toast.success('Document scanned successfully! Details auto-filled.');\n    }, 2000);\n  };\n  const handleUploadSubmit = async e => {\n    e.preventDefault();\n    if (!selectedFile) {\n      toast.error('Please select a file to upload');\n      return;\n    }\n    setIsUploading(true);\n    const formData = new FormData();\n    formData.append('file', selectedFile);\n    formData.append('document_type', uploadData.document_type);\n    formData.append('document_name', uploadData.document_name);\n    formData.append('document_number', uploadData.document_number);\n    formData.append('issue_date', uploadData.issue_date);\n    formData.append('expiry_date', uploadData.expiry_date);\n    formData.append('issuing_authority', uploadData.issuing_authority);\n    try {\n      await uploadDocumentMutation.mutateAsync(formData);\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const getDocumentStatusIcon = document => {\n    if (document.is_expired) {\n      return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n        className: \"h-5 w-5 text-red-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 14\n      }, this);\n    }\n    if (document.is_expiring_soon) {\n      return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n        className: \"h-5 w-5 text-yellow-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(CheckCircle, {\n      className: \"h-5 w-5 text-green-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 12\n    }, this);\n  };\n  const getDocumentStatusText = document => {\n    if (document.is_expired) {\n      return 'Expired';\n    }\n    if (document.is_expiring_soon) {\n      return `Expires in ${document.days_to_expiry} days`;\n    }\n    return 'Valid';\n  };\n  const documents = (documentsData === null || documentsData === void 0 ? void 0 : (_documentsData$data = documentsData.data) === null || _documentsData$data === void 0 ? void 0 : (_documentsData$data$d = _documentsData$data.data) === null || _documentsData$data$d === void 0 ? void 0 : _documentsData$data$d.documents) || [];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content max-w-4xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: [\"Documents - \", bus.bus_number]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-400 hover:text-gray-500\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: !showUploadForm ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-gray-900\",\n              children: \"Uploaded Documents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowUploadForm(true),\n              className: \"btn btn-primary flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Upload, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Upload Document\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-spinner h-8 w-8 mx-auto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-gray-600\",\n              children: \"Loading documents...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this) : documents.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"No documents uploaded yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowUploadForm(true),\n              className: \"btn btn-primary mt-4\",\n              children: \"Upload First Document\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: documents.map(document => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(FileText, {\n                    className: \"h-8 w-8 text-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-medium text-gray-900\",\n                      children: document.document_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: [document.document_type, \" \\u2022 \", document.file_size_mb, \" MB\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [getDocumentStatusIcon(document), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-sm ${document.is_expired ? 'text-red-600' : document.is_expiring_soon ? 'text-yellow-600' : 'text-green-600'}`,\n                      children: getDocumentStatusText(document)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-blue-600 hover:text-blue-900\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Document Number:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: document.document_number || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Issue Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: document.issue_date || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Expiry Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: document.expiry_date || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Issuing Authority:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: document.issuing_authority || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 23\n              }, this), document.ocr_data && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 p-3 bg-gray-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Scan, {\n                    className: \"h-4 w-4 text-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: \"OCR Extracted Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 29\n                  }, this), document.ocr_confidence && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-600\",\n                    children: [\"(\", Math.round(document.ocr_confidence * 100), \"% confidence)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 line-clamp-3\",\n                  children: document.ocr_data.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 25\n              }, this)]\n            }, document.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) :\n        /*#__PURE__*/\n        /* Upload Form */\n        _jsxDEV(\"form\", {\n          onSubmit: handleUploadSubmit,\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-gray-900\",\n              children: \"Upload New Document\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowUploadForm(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Document Type *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: uploadData.document_type,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  document_type: e.target.value\n                })),\n                className: \"input\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Insurance\",\n                  children: \"Insurance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"RC\",\n                  children: \"RC (Registration Certificate)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Fitness Certificate\",\n                  children: \"Fitness Certificate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Permit\",\n                  children: \"Permit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Pollution Certificate\",\n                  children: \"Pollution Certificate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Tax Receipt\",\n                  children: \"Tax Receipt\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Service Record\",\n                  children: \"Service Record\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Document Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: uploadData.document_name,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  document_name: e.target.value\n                })),\n                className: \"input\",\n                required: true,\n                placeholder: \"Enter document name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Document Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: uploadData.document_number,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  document_number: e.target.value\n                })),\n                className: \"input\",\n                placeholder: \"Will be auto-filled by OCR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Issuing Authority\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: uploadData.issuing_authority,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  issuing_authority: e.target.value\n                })),\n                className: \"input\",\n                placeholder: \"Will be auto-filled by OCR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Issue Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: uploadData.issue_date,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  issue_date: e.target.value\n                })),\n                className: \"input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Expiry Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: uploadData.expiry_date,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  expiry_date: e.target.value\n                })),\n                className: \"input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Upload File *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-1 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(Upload, {\n                  className: \"mx-auto h-12 w-12 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Upload a file\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      className: \"sr-only\",\n                      accept: \".pdf,.jpg,.jpeg,.png,.tiff\",\n                      onChange: handleFileSelect,\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"pl-1\",\n                    children: \"or drag and drop\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"PDF, JPG, PNG, TIFF up to 16MB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(FileText, {\n                className: \"h-4 w-4 text-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-900\",\n                children: selectedFile.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: [\"(\", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), ocrProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"loading-spinner h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-blue-900\",\n                children: \"Processing document with AI OCR...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowUploadForm(false),\n              className: \"btn btn-secondary\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isUploading || ocrProcessing,\n              className: \"btn btn-primary\",\n              children: isUploading ? 'Uploading...' : 'Upload Document'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentModal, \"KV+FlNP54CMAsFOHFOGuOU/9zD8=\", false, function () {\n  return [useQueryClient, useQuery, useMutation];\n});\n_c = DocumentModal;\nexport default DocumentModal;\nvar _c;\n$RefreshReg$(_c, \"DocumentModal\");", "map": {"version": 3, "names": ["React", "useState", "useQuery", "useMutation", "useQueryClient", "busesAPI", "X", "Upload", "FileText", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "Eye", "<PERSON><PERSON>", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DocumentModal", "bus", "onClose", "_s", "_documentsData$data", "_documentsData$data$d", "showUploadForm", "setShowUploadForm", "uploadData", "setUploadData", "document_type", "document_name", "document_number", "issue_date", "expiry_date", "issuing_authority", "selectedFile", "setSelectedFile", "isUploading", "setIsUploading", "ocrProcessing", "setOcrProcessing", "queryClient", "data", "documentsData", "isLoading", "id", "getDocuments", "uploadDocumentMutation", "formData", "uploadDocument", "onSuccess", "invalidateQueries", "success", "resetUploadForm", "onError", "error", "_error$response", "_error$response$data", "response", "handleFileSelect", "e", "file", "target", "files", "allowedTypes", "includes", "type", "size", "prev", "name", "split", "performOCR", "setTimeout", "ocrResults", "Insurance", "RC", "registration_number", "replace", "Date", "now", "toString", "slice", "result", "handleUploadSubmit", "preventDefault", "FormData", "append", "mutateAsync", "getDocumentStatusIcon", "document", "is_expired", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "is_expiring_soon", "getDocumentStatusText", "days_to_expiry", "documents", "children", "bus_number", "onClick", "length", "map", "file_size_mb", "ocr_data", "ocr_confidence", "Math", "round", "text", "onSubmit", "value", "onChange", "required", "placeholder", "accept", "toFixed", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/components/DocumentModal.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport { busesAPI } from '../services/api';\nimport { X, Upload, FileText, AlertTriangle, CheckCircle, Eye, Scan } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst DocumentModal = ({ bus, onClose }) => {\n  const [showUploadForm, setShowUploadForm] = useState(false);\n  const [uploadData, setUploadData] = useState({\n    document_type: 'Insurance',\n    document_name: '',\n    document_number: '',\n    issue_date: '',\n    expiry_date: '',\n    issuing_authority: '',\n  });\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isUploading, setIsUploading] = useState(false);\n  const [ocrProcessing, setOcrProcessing] = useState(false);\n\n  const queryClient = useQueryClient();\n\n  // Fetch bus documents\n  const { data: documentsData, isLoading } = useQuery(\n    ['bus-documents', bus.id],\n    () => busesAPI.getDocuments(bus.id)\n  );\n\n  // Upload document mutation\n  const uploadDocumentMutation = useMutation(\n    (formData) => busesAPI.uploadDocument(bus.id, formData),\n    {\n      onSuccess: () => {\n        queryClient.invalidateQueries(['bus-documents', bus.id]);\n        toast.success('Document uploaded successfully');\n        setShowUploadForm(false);\n        resetUploadForm();\n      },\n      onError: (error) => {\n        toast.error(error.response?.data?.error || 'Failed to upload document');\n      },\n    }\n  );\n\n  const resetUploadForm = () => {\n    setUploadData({\n      document_type: 'Insurance',\n      document_name: '',\n      document_number: '',\n      issue_date: '',\n      expiry_date: '',\n      issuing_authority: '',\n    });\n    setSelectedFile(null);\n  };\n\n  const handleFileSelect = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/tiff'];\n      if (!allowedTypes.includes(file.type)) {\n        toast.error('Invalid file type. Please upload PDF, JPG, PNG, or TIFF files.');\n        return;\n      }\n\n      // Validate file size (max 16MB)\n      if (file.size > 16 * 1024 * 1024) {\n        toast.error('File size too large. Maximum size is 16MB.');\n        return;\n      }\n\n      setSelectedFile(file);\n      \n      // Auto-fill document name if empty\n      if (!uploadData.document_name) {\n        setUploadData(prev => ({\n          ...prev,\n          document_name: file.name.split('.')[0]\n        }));\n      }\n\n      // Simulate OCR processing\n      performOCR(file);\n    }\n  };\n\n  const performOCR = async (file) => {\n    setOcrProcessing(true);\n    \n    // Simulate OCR processing delay\n    setTimeout(() => {\n      // Mock OCR results based on document type\n      const ocrResults = {\n        Insurance: {\n          document_number: 'INS123456789',\n          issue_date: '2023-01-01',\n          expiry_date: '2024-01-01',\n          issuing_authority: 'National Insurance Company',\n        },\n        RC: {\n          document_number: 'RC' + bus.registration_number.replace(/\\s/g, ''),\n          issue_date: '2020-01-01',\n          expiry_date: '2025-01-01',\n          issuing_authority: 'Regional Transport Office',\n        },\n        'Fitness Certificate': {\n          document_number: 'FC' + Date.now().toString().slice(-6),\n          issue_date: '2023-01-01',\n          expiry_date: '2024-01-01',\n          issuing_authority: 'Motor Vehicle Inspector',\n        },\n      };\n\n      const result = ocrResults[uploadData.document_type] || {};\n      \n      setUploadData(prev => ({\n        ...prev,\n        ...result\n      }));\n\n      setOcrProcessing(false);\n      toast.success('Document scanned successfully! Details auto-filled.');\n    }, 2000);\n  };\n\n  const handleUploadSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!selectedFile) {\n      toast.error('Please select a file to upload');\n      return;\n    }\n\n    setIsUploading(true);\n\n    const formData = new FormData();\n    formData.append('file', selectedFile);\n    formData.append('document_type', uploadData.document_type);\n    formData.append('document_name', uploadData.document_name);\n    formData.append('document_number', uploadData.document_number);\n    formData.append('issue_date', uploadData.issue_date);\n    formData.append('expiry_date', uploadData.expiry_date);\n    formData.append('issuing_authority', uploadData.issuing_authority);\n\n    try {\n      await uploadDocumentMutation.mutateAsync(formData);\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const getDocumentStatusIcon = (document) => {\n    if (document.is_expired) {\n      return <AlertTriangle className=\"h-5 w-5 text-red-500\" />;\n    }\n    if (document.is_expiring_soon) {\n      return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />;\n    }\n    return <CheckCircle className=\"h-5 w-5 text-green-500\" />;\n  };\n\n  const getDocumentStatusText = (document) => {\n    if (document.is_expired) {\n      return 'Expired';\n    }\n    if (document.is_expiring_soon) {\n      return `Expires in ${document.days_to_expiry} days`;\n    }\n    return 'Valid';\n  };\n\n  const documents = documentsData?.data?.data?.documents || [];\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"modal-content max-w-4xl\">\n        <div className=\"modal-header\">\n          <h3 className=\"text-lg font-medium text-gray-900\">\n            Documents - {bus.bus_number}\n          </h3>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-500\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"modal-body\">\n          {!showUploadForm ? (\n            <>\n              {/* Documents List */}\n              <div className=\"flex justify-between items-center mb-6\">\n                <h4 className=\"text-md font-medium text-gray-900\">Uploaded Documents</h4>\n                <button\n                  onClick={() => setShowUploadForm(true)}\n                  className=\"btn btn-primary flex items-center space-x-2\"\n                >\n                  <Upload className=\"h-4 w-4\" />\n                  <span>Upload Document</span>\n                </button>\n              </div>\n\n              {isLoading ? (\n                <div className=\"text-center py-8\">\n                  <div className=\"loading-spinner h-8 w-8 mx-auto\"></div>\n                  <p className=\"mt-2 text-gray-600\">Loading documents...</p>\n                </div>\n              ) : documents.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-600\">No documents uploaded yet</p>\n                  <button\n                    onClick={() => setShowUploadForm(true)}\n                    className=\"btn btn-primary mt-4\"\n                  >\n                    Upload First Document\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {documents.map((document) => (\n                    <div key={document.id} className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-3\">\n                          <FileText className=\"h-8 w-8 text-blue-500\" />\n                          <div>\n                            <h5 className=\"font-medium text-gray-900\">\n                              {document.document_name}\n                            </h5>\n                            <p className=\"text-sm text-gray-600\">\n                              {document.document_type} • {document.file_size_mb} MB\n                            </p>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex items-center space-x-4\">\n                          <div className=\"flex items-center space-x-2\">\n                            {getDocumentStatusIcon(document)}\n                            <span className={`text-sm ${\n                              document.is_expired ? 'text-red-600' :\n                              document.is_expiring_soon ? 'text-yellow-600' : 'text-green-600'\n                            }`}>\n                              {getDocumentStatusText(document)}\n                            </span>\n                          </div>\n                          \n                          <button className=\"text-blue-600 hover:text-blue-900\">\n                            <Eye className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Document Details */}\n                      <div className=\"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                        <div>\n                          <span className=\"text-gray-600\">Document Number:</span>\n                          <p className=\"font-medium\">{document.document_number || 'N/A'}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-600\">Issue Date:</span>\n                          <p className=\"font-medium\">{document.issue_date || 'N/A'}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-600\">Expiry Date:</span>\n                          <p className=\"font-medium\">{document.expiry_date || 'N/A'}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-600\">Issuing Authority:</span>\n                          <p className=\"font-medium\">{document.issuing_authority || 'N/A'}</p>\n                        </div>\n                      </div>\n\n                      {/* OCR Data */}\n                      {document.ocr_data && (\n                        <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <Scan className=\"h-4 w-4 text-blue-500\" />\n                            <span className=\"text-sm font-medium text-gray-900\">\n                              OCR Extracted Data\n                            </span>\n                            {document.ocr_confidence && (\n                              <span className=\"text-xs text-gray-600\">\n                                ({Math.round(document.ocr_confidence * 100)}% confidence)\n                              </span>\n                            )}\n                          </div>\n                          <p className=\"text-sm text-gray-600 line-clamp-3\">\n                            {document.ocr_data.text}\n                          </p>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </>\n          ) : (\n            /* Upload Form */\n            <form onSubmit={handleUploadSubmit} className=\"space-y-6\">\n              <div className=\"flex justify-between items-center\">\n                <h4 className=\"text-md font-medium text-gray-900\">Upload New Document</h4>\n                <button\n                  type=\"button\"\n                  onClick={() => setShowUploadForm(false)}\n                  className=\"text-gray-400 hover:text-gray-500\"\n                >\n                  <X className=\"h-5 w-5\" />\n                </button>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"form-label\">Document Type *</label>\n                  <select\n                    value={uploadData.document_type}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, document_type: e.target.value }))}\n                    className=\"input\"\n                    required\n                  >\n                    <option value=\"Insurance\">Insurance</option>\n                    <option value=\"RC\">RC (Registration Certificate)</option>\n                    <option value=\"Fitness Certificate\">Fitness Certificate</option>\n                    <option value=\"Permit\">Permit</option>\n                    <option value=\"Pollution Certificate\">Pollution Certificate</option>\n                    <option value=\"Tax Receipt\">Tax Receipt</option>\n                    <option value=\"Service Record\">Service Record</option>\n                    <option value=\"Other\">Other</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Document Name *</label>\n                  <input\n                    type=\"text\"\n                    value={uploadData.document_name}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, document_name: e.target.value }))}\n                    className=\"input\"\n                    required\n                    placeholder=\"Enter document name\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Document Number</label>\n                  <input\n                    type=\"text\"\n                    value={uploadData.document_number}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, document_number: e.target.value }))}\n                    className=\"input\"\n                    placeholder=\"Will be auto-filled by OCR\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Issuing Authority</label>\n                  <input\n                    type=\"text\"\n                    value={uploadData.issuing_authority}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, issuing_authority: e.target.value }))}\n                    className=\"input\"\n                    placeholder=\"Will be auto-filled by OCR\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Issue Date</label>\n                  <input\n                    type=\"date\"\n                    value={uploadData.issue_date}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, issue_date: e.target.value }))}\n                    className=\"input\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Expiry Date</label>\n                  <input\n                    type=\"date\"\n                    value={uploadData.expiry_date}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, expiry_date: e.target.value }))}\n                    className=\"input\"\n                  />\n                </div>\n              </div>\n\n              {/* File Upload */}\n              <div>\n                <label className=\"form-label\">Upload File *</label>\n                <div className=\"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md\">\n                  <div className=\"space-y-1 text-center\">\n                    <Upload className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div className=\"flex text-sm text-gray-600\">\n                      <label className=\"relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500\">\n                        <span>Upload a file</span>\n                        <input\n                          type=\"file\"\n                          className=\"sr-only\"\n                          accept=\".pdf,.jpg,.jpeg,.png,.tiff\"\n                          onChange={handleFileSelect}\n                          required\n                        />\n                      </label>\n                      <p className=\"pl-1\">or drag and drop</p>\n                    </div>\n                    <p className=\"text-xs text-gray-500\">\n                      PDF, JPG, PNG, TIFF up to 16MB\n                    </p>\n                  </div>\n                </div>\n                \n                {selectedFile && (\n                  <div className=\"mt-2 flex items-center space-x-2\">\n                    <FileText className=\"h-4 w-4 text-blue-500\" />\n                    <span className=\"text-sm text-gray-900\">{selectedFile.name}</span>\n                    <span className=\"text-xs text-gray-500\">\n                      ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)\n                    </span>\n                  </div>\n                )}\n              </div>\n\n              {/* OCR Processing Indicator */}\n              {ocrProcessing && (\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"loading-spinner h-4 w-4\"></div>\n                    <span className=\"text-sm text-blue-900\">\n                      Processing document with AI OCR...\n                    </span>\n                  </div>\n                </div>\n              )}\n\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  type=\"button\"\n                  onClick={() => setShowUploadForm(false)}\n                  className=\"btn btn-secondary\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={isUploading || ocrProcessing}\n                  className=\"btn btn-primary\"\n                >\n                  {isUploading ? 'Uploading...' : 'Upload Document'}\n                </button>\n              </div>\n            </form>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DocumentModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,CAAC,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,WAAW,EAAEC,GAAG,EAAEC,IAAI,QAAQ,cAAc;AACzF,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,aAAa,GAAGA,CAAC;EAAEC,GAAG;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,qBAAA;EAC1C,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC;IAC3C2B,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAMuC,WAAW,GAAGpC,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAM;IAAEqC,IAAI,EAAEC,aAAa;IAAEC;EAAU,CAAC,GAAGzC,QAAQ,CACjD,CAAC,eAAe,EAAEiB,GAAG,CAACyB,EAAE,CAAC,EACzB,MAAMvC,QAAQ,CAACwC,YAAY,CAAC1B,GAAG,CAACyB,EAAE,CACpC,CAAC;;EAED;EACA,MAAME,sBAAsB,GAAG3C,WAAW,CACvC4C,QAAQ,IAAK1C,QAAQ,CAAC2C,cAAc,CAAC7B,GAAG,CAACyB,EAAE,EAAEG,QAAQ,CAAC,EACvD;IACEE,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACU,iBAAiB,CAAC,CAAC,eAAe,EAAE/B,GAAG,CAACyB,EAAE,CAAC,CAAC;MACxD/B,KAAK,CAACsC,OAAO,CAAC,gCAAgC,CAAC;MAC/C1B,iBAAiB,CAAC,KAAK,CAAC;MACxB2B,eAAe,CAAC,CAAC;IACnB,CAAC;IACDC,OAAO,EAAGC,KAAK,IAAK;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MAClB3C,KAAK,CAACyC,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACG,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBd,IAAI,cAAAe,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,KAAI,2BAA2B,CAAC;IACzE;EACF,CACF,CAAC;EAED,MAAMF,eAAe,GAAGA,CAAA,KAAM;IAC5BzB,aAAa,CAAC;MACZC,aAAa,EAAE,WAAW;MAC1BC,aAAa,EAAE,EAAE;MACjBC,eAAe,EAAE,EAAE;MACnBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACFE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMuB,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR;MACA,MAAMG,YAAY,GAAG,CAAC,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;MAC9F,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;QACrCpD,KAAK,CAACyC,KAAK,CAAC,gEAAgE,CAAC;QAC7E;MACF;;MAEA;MACA,IAAIM,IAAI,CAACM,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;QAChCrD,KAAK,CAACyC,KAAK,CAAC,4CAA4C,CAAC;QACzD;MACF;MAEAnB,eAAe,CAACyB,IAAI,CAAC;;MAErB;MACA,IAAI,CAAClC,UAAU,CAACG,aAAa,EAAE;QAC7BF,aAAa,CAACwC,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPtC,aAAa,EAAE+B,IAAI,CAACQ,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;MACL;;MAEA;MACAC,UAAU,CAACV,IAAI,CAAC;IAClB;EACF,CAAC;EAED,MAAMU,UAAU,GAAG,MAAOV,IAAI,IAAK;IACjCrB,gBAAgB,CAAC,IAAI,CAAC;;IAEtB;IACAgC,UAAU,CAAC,MAAM;MACf;MACA,MAAMC,UAAU,GAAG;QACjBC,SAAS,EAAE;UACT3C,eAAe,EAAE,cAAc;UAC/BC,UAAU,EAAE,YAAY;UACxBC,WAAW,EAAE,YAAY;UACzBC,iBAAiB,EAAE;QACrB,CAAC;QACDyC,EAAE,EAAE;UACF5C,eAAe,EAAE,IAAI,GAAGX,GAAG,CAACwD,mBAAmB,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;UAClE7C,UAAU,EAAE,YAAY;UACxBC,WAAW,EAAE,YAAY;UACzBC,iBAAiB,EAAE;QACrB,CAAC;QACD,qBAAqB,EAAE;UACrBH,eAAe,EAAE,IAAI,GAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;UACvDjD,UAAU,EAAE,YAAY;UACxBC,WAAW,EAAE,YAAY;UACzBC,iBAAiB,EAAE;QACrB;MACF,CAAC;MAED,MAAMgD,MAAM,GAAGT,UAAU,CAAC9C,UAAU,CAACE,aAAa,CAAC,IAAI,CAAC,CAAC;MAEzDD,aAAa,CAACwC,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,GAAGc;MACL,CAAC,CAAC,CAAC;MAEH1C,gBAAgB,CAAC,KAAK,CAAC;MACvB1B,KAAK,CAACsC,OAAO,CAAC,qDAAqD,CAAC;IACtE,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM+B,kBAAkB,GAAG,MAAOvB,CAAC,IAAK;IACtCA,CAAC,CAACwB,cAAc,CAAC,CAAC;IAElB,IAAI,CAACjD,YAAY,EAAE;MACjBrB,KAAK,CAACyC,KAAK,CAAC,gCAAgC,CAAC;MAC7C;IACF;IAEAjB,cAAc,CAAC,IAAI,CAAC;IAEpB,MAAMU,QAAQ,GAAG,IAAIqC,QAAQ,CAAC,CAAC;IAC/BrC,QAAQ,CAACsC,MAAM,CAAC,MAAM,EAAEnD,YAAY,CAAC;IACrCa,QAAQ,CAACsC,MAAM,CAAC,eAAe,EAAE3D,UAAU,CAACE,aAAa,CAAC;IAC1DmB,QAAQ,CAACsC,MAAM,CAAC,eAAe,EAAE3D,UAAU,CAACG,aAAa,CAAC;IAC1DkB,QAAQ,CAACsC,MAAM,CAAC,iBAAiB,EAAE3D,UAAU,CAACI,eAAe,CAAC;IAC9DiB,QAAQ,CAACsC,MAAM,CAAC,YAAY,EAAE3D,UAAU,CAACK,UAAU,CAAC;IACpDgB,QAAQ,CAACsC,MAAM,CAAC,aAAa,EAAE3D,UAAU,CAACM,WAAW,CAAC;IACtDe,QAAQ,CAACsC,MAAM,CAAC,mBAAmB,EAAE3D,UAAU,CAACO,iBAAiB,CAAC;IAElE,IAAI;MACF,MAAMa,sBAAsB,CAACwC,WAAW,CAACvC,QAAQ,CAAC;IACpD,CAAC,SAAS;MACRV,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMkD,qBAAqB,GAAIC,QAAQ,IAAK;IAC1C,IAAIA,QAAQ,CAACC,UAAU,EAAE;MACvB,oBAAO1E,OAAA,CAACN,aAAa;QAACiF,SAAS,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC3D;IACA,IAAIN,QAAQ,CAACO,gBAAgB,EAAE;MAC7B,oBAAOhF,OAAA,CAACN,aAAa;QAACiF,SAAS,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC9D;IACA,oBAAO/E,OAAA,CAACL,WAAW;MAACgF,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3D,CAAC;EAED,MAAME,qBAAqB,GAAIR,QAAQ,IAAK;IAC1C,IAAIA,QAAQ,CAACC,UAAU,EAAE;MACvB,OAAO,SAAS;IAClB;IACA,IAAID,QAAQ,CAACO,gBAAgB,EAAE;MAC7B,OAAO,cAAcP,QAAQ,CAACS,cAAc,OAAO;IACrD;IACA,OAAO,OAAO;EAChB,CAAC;EAED,MAAMC,SAAS,GAAG,CAAAxD,aAAa,aAAbA,aAAa,wBAAApB,mBAAA,GAAboB,aAAa,CAAED,IAAI,cAAAnB,mBAAA,wBAAAC,qBAAA,GAAnBD,mBAAA,CAAqBmB,IAAI,cAAAlB,qBAAA,uBAAzBA,qBAAA,CAA2B2E,SAAS,KAAI,EAAE;EAE5D,oBACEnF,OAAA;IAAK2E,SAAS,EAAC,eAAe;IAAAS,QAAA,eAC5BpF,OAAA;MAAK2E,SAAS,EAAC,yBAAyB;MAAAS,QAAA,gBACtCpF,OAAA;QAAK2E,SAAS,EAAC,cAAc;QAAAS,QAAA,gBAC3BpF,OAAA;UAAI2E,SAAS,EAAC,mCAAmC;UAAAS,QAAA,GAAC,cACpC,EAAChF,GAAG,CAACiF,UAAU;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACL/E,OAAA;UACEsF,OAAO,EAAEjF,OAAQ;UACjBsE,SAAS,EAAC,mCAAmC;UAAAS,QAAA,eAE7CpF,OAAA,CAACT,CAAC;YAACoF,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/E,OAAA;QAAK2E,SAAS,EAAC,YAAY;QAAAS,QAAA,EACxB,CAAC3E,cAAc,gBACdT,OAAA,CAAAE,SAAA;UAAAkF,QAAA,gBAEEpF,OAAA;YAAK2E,SAAS,EAAC,wCAAwC;YAAAS,QAAA,gBACrDpF,OAAA;cAAI2E,SAAS,EAAC,mCAAmC;cAAAS,QAAA,EAAC;YAAkB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzE/E,OAAA;cACEsF,OAAO,EAAEA,CAAA,KAAM5E,iBAAiB,CAAC,IAAI,CAAE;cACvCiE,SAAS,EAAC,6CAA6C;cAAAS,QAAA,gBAEvDpF,OAAA,CAACR,MAAM;gBAACmF,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9B/E,OAAA;gBAAAoF,QAAA,EAAM;cAAe;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELnD,SAAS,gBACR5B,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BpF,OAAA;cAAK2E,SAAS,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvD/E,OAAA;cAAG2E,SAAS,EAAC,oBAAoB;cAAAS,QAAA,EAAC;YAAoB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,GACJI,SAAS,CAACI,MAAM,KAAK,CAAC,gBACxBvF,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BpF,OAAA,CAACP,QAAQ;cAACkF,SAAS,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7D/E,OAAA;cAAG2E,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAyB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1D/E,OAAA;cACEsF,OAAO,EAAEA,CAAA,KAAM5E,iBAAiB,CAAC,IAAI,CAAE;cACvCiE,SAAS,EAAC,sBAAsB;cAAAS,QAAA,EACjC;YAED;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAEN/E,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAS,QAAA,EACvBD,SAAS,CAACK,GAAG,CAAEf,QAAQ,iBACtBzE,OAAA;cAAuB2E,SAAS,EAAC,uCAAuC;cAAAS,QAAA,gBACtEpF,OAAA;gBAAK2E,SAAS,EAAC,mCAAmC;gBAAAS,QAAA,gBAChDpF,OAAA;kBAAK2E,SAAS,EAAC,6BAA6B;kBAAAS,QAAA,gBAC1CpF,OAAA,CAACP,QAAQ;oBAACkF,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9C/E,OAAA;oBAAAoF,QAAA,gBACEpF,OAAA;sBAAI2E,SAAS,EAAC,2BAA2B;sBAAAS,QAAA,EACtCX,QAAQ,CAAC3D;oBAAa;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACL/E,OAAA;sBAAG2E,SAAS,EAAC,uBAAuB;sBAAAS,QAAA,GACjCX,QAAQ,CAAC5D,aAAa,EAAC,UAAG,EAAC4D,QAAQ,CAACgB,YAAY,EAAC,KACpD;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN/E,OAAA;kBAAK2E,SAAS,EAAC,6BAA6B;kBAAAS,QAAA,gBAC1CpF,OAAA;oBAAK2E,SAAS,EAAC,6BAA6B;oBAAAS,QAAA,GACzCZ,qBAAqB,CAACC,QAAQ,CAAC,eAChCzE,OAAA;sBAAM2E,SAAS,EAAE,WACfF,QAAQ,CAACC,UAAU,GAAG,cAAc,GACpCD,QAAQ,CAACO,gBAAgB,GAAG,iBAAiB,GAAG,gBAAgB,EAC/D;sBAAAI,QAAA,EACAH,qBAAqB,CAACR,QAAQ;oBAAC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAEN/E,OAAA;oBAAQ2E,SAAS,EAAC,mCAAmC;oBAAAS,QAAA,eACnDpF,OAAA,CAACJ,GAAG;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN/E,OAAA;gBAAK2E,SAAS,EAAC,oDAAoD;gBAAAS,QAAA,gBACjEpF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAM2E,SAAS,EAAC,eAAe;oBAAAS,QAAA,EAAC;kBAAgB;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvD/E,OAAA;oBAAG2E,SAAS,EAAC,aAAa;oBAAAS,QAAA,EAAEX,QAAQ,CAAC1D,eAAe,IAAI;kBAAK;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC,eACN/E,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAM2E,SAAS,EAAC,eAAe;oBAAAS,QAAA,EAAC;kBAAW;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClD/E,OAAA;oBAAG2E,SAAS,EAAC,aAAa;oBAAAS,QAAA,EAAEX,QAAQ,CAACzD,UAAU,IAAI;kBAAK;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACN/E,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAM2E,SAAS,EAAC,eAAe;oBAAAS,QAAA,EAAC;kBAAY;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnD/E,OAAA;oBAAG2E,SAAS,EAAC,aAAa;oBAAAS,QAAA,EAAEX,QAAQ,CAACxD,WAAW,IAAI;kBAAK;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACN/E,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAM2E,SAAS,EAAC,eAAe;oBAAAS,QAAA,EAAC;kBAAkB;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzD/E,OAAA;oBAAG2E,SAAS,EAAC,aAAa;oBAAAS,QAAA,EAAEX,QAAQ,CAACvD,iBAAiB,IAAI;kBAAK;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLN,QAAQ,CAACiB,QAAQ,iBAChB1F,OAAA;gBAAK2E,SAAS,EAAC,gCAAgC;gBAAAS,QAAA,gBAC7CpF,OAAA;kBAAK2E,SAAS,EAAC,kCAAkC;kBAAAS,QAAA,gBAC/CpF,OAAA,CAACH,IAAI;oBAAC8E,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1C/E,OAAA;oBAAM2E,SAAS,EAAC,mCAAmC;oBAAAS,QAAA,EAAC;kBAEpD;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACNN,QAAQ,CAACkB,cAAc,iBACtB3F,OAAA;oBAAM2E,SAAS,EAAC,uBAAuB;oBAAAS,QAAA,GAAC,GACrC,EAACQ,IAAI,CAACC,KAAK,CAACpB,QAAQ,CAACkB,cAAc,GAAG,GAAG,CAAC,EAAC,eAC9C;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN/E,OAAA;kBAAG2E,SAAS,EAAC,oCAAoC;kBAAAS,QAAA,EAC9CX,QAAQ,CAACiB,QAAQ,CAACI;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA,GArEON,QAAQ,CAAC5C,EAAE;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsEhB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,eACD,CAAC;QAAA;QAEH;QACA/E,OAAA;UAAM+F,QAAQ,EAAE5B,kBAAmB;UAACQ,SAAS,EAAC,WAAW;UAAAS,QAAA,gBACvDpF,OAAA;YAAK2E,SAAS,EAAC,mCAAmC;YAAAS,QAAA,gBAChDpF,OAAA;cAAI2E,SAAS,EAAC,mCAAmC;cAAAS,QAAA,EAAC;YAAmB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E/E,OAAA;cACEkD,IAAI,EAAC,QAAQ;cACboC,OAAO,EAAEA,CAAA,KAAM5E,iBAAiB,CAAC,KAAK,CAAE;cACxCiE,SAAS,EAAC,mCAAmC;cAAAS,QAAA,eAE7CpF,OAAA,CAACT,CAAC;gBAACoF,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN/E,OAAA;YAAK2E,SAAS,EAAC,uCAAuC;YAAAS,QAAA,gBACpDpF,OAAA;cAAAoF,QAAA,gBACEpF,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAAAS,QAAA,EAAC;cAAe;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD/E,OAAA;gBACEgG,KAAK,EAAErF,UAAU,CAACE,aAAc;gBAChCoF,QAAQ,EAAGrD,CAAC,IAAKhC,aAAa,CAACwC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEvC,aAAa,EAAE+B,CAAC,CAACE,MAAM,CAACkD;gBAAM,CAAC,CAAC,CAAE;gBACrFrB,SAAS,EAAC,OAAO;gBACjBuB,QAAQ;gBAAAd,QAAA,gBAERpF,OAAA;kBAAQgG,KAAK,EAAC,WAAW;kBAAAZ,QAAA,EAAC;gBAAS;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C/E,OAAA;kBAAQgG,KAAK,EAAC,IAAI;kBAAAZ,QAAA,EAAC;gBAA6B;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzD/E,OAAA;kBAAQgG,KAAK,EAAC,qBAAqB;kBAAAZ,QAAA,EAAC;gBAAmB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChE/E,OAAA;kBAAQgG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAM;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC/E,OAAA;kBAAQgG,KAAK,EAAC,uBAAuB;kBAAAZ,QAAA,EAAC;gBAAqB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpE/E,OAAA;kBAAQgG,KAAK,EAAC,aAAa;kBAAAZ,QAAA,EAAC;gBAAW;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChD/E,OAAA;kBAAQgG,KAAK,EAAC,gBAAgB;kBAAAZ,QAAA,EAAC;gBAAc;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtD/E,OAAA;kBAAQgG,KAAK,EAAC,OAAO;kBAAAZ,QAAA,EAAC;gBAAK;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/E,OAAA;cAAAoF,QAAA,gBACEpF,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAAAS,QAAA,EAAC;cAAe;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD/E,OAAA;gBACEkD,IAAI,EAAC,MAAM;gBACX8C,KAAK,EAAErF,UAAU,CAACG,aAAc;gBAChCmF,QAAQ,EAAGrD,CAAC,IAAKhC,aAAa,CAACwC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEtC,aAAa,EAAE8B,CAAC,CAACE,MAAM,CAACkD;gBAAM,CAAC,CAAC,CAAE;gBACrFrB,SAAS,EAAC,OAAO;gBACjBuB,QAAQ;gBACRC,WAAW,EAAC;cAAqB;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/E,OAAA;cAAAoF,QAAA,gBACEpF,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAAAS,QAAA,EAAC;cAAe;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD/E,OAAA;gBACEkD,IAAI,EAAC,MAAM;gBACX8C,KAAK,EAAErF,UAAU,CAACI,eAAgB;gBAClCkF,QAAQ,EAAGrD,CAAC,IAAKhC,aAAa,CAACwC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAErC,eAAe,EAAE6B,CAAC,CAACE,MAAM,CAACkD;gBAAM,CAAC,CAAC,CAAE;gBACvFrB,SAAS,EAAC,OAAO;gBACjBwB,WAAW,EAAC;cAA4B;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/E,OAAA;cAAAoF,QAAA,gBACEpF,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAAAS,QAAA,EAAC;cAAiB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvD/E,OAAA;gBACEkD,IAAI,EAAC,MAAM;gBACX8C,KAAK,EAAErF,UAAU,CAACO,iBAAkB;gBACpC+E,QAAQ,EAAGrD,CAAC,IAAKhC,aAAa,CAACwC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAElC,iBAAiB,EAAE0B,CAAC,CAACE,MAAM,CAACkD;gBAAM,CAAC,CAAC,CAAE;gBACzFrB,SAAS,EAAC,OAAO;gBACjBwB,WAAW,EAAC;cAA4B;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/E,OAAA;cAAAoF,QAAA,gBACEpF,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAAAS,QAAA,EAAC;cAAU;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChD/E,OAAA;gBACEkD,IAAI,EAAC,MAAM;gBACX8C,KAAK,EAAErF,UAAU,CAACK,UAAW;gBAC7BiF,QAAQ,EAAGrD,CAAC,IAAKhC,aAAa,CAACwC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEpC,UAAU,EAAE4B,CAAC,CAACE,MAAM,CAACkD;gBAAM,CAAC,CAAC,CAAE;gBAClFrB,SAAS,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/E,OAAA;cAAAoF,QAAA,gBACEpF,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAAAS,QAAA,EAAC;cAAW;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjD/E,OAAA;gBACEkD,IAAI,EAAC,MAAM;gBACX8C,KAAK,EAAErF,UAAU,CAACM,WAAY;gBAC9BgF,QAAQ,EAAGrD,CAAC,IAAKhC,aAAa,CAACwC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEnC,WAAW,EAAE2B,CAAC,CAACE,MAAM,CAACkD;gBAAM,CAAC,CAAC,CAAE;gBACnFrB,SAAS,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/E,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAO2E,SAAS,EAAC,YAAY;cAAAS,QAAA,EAAC;YAAa;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD/E,OAAA;cAAK2E,SAAS,EAAC,2FAA2F;cAAAS,QAAA,eACxGpF,OAAA;gBAAK2E,SAAS,EAAC,uBAAuB;gBAAAS,QAAA,gBACpCpF,OAAA,CAACR,MAAM;kBAACmF,SAAS,EAAC;gBAAiC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtD/E,OAAA;kBAAK2E,SAAS,EAAC,4BAA4B;kBAAAS,QAAA,gBACzCpF,OAAA;oBAAO2E,SAAS,EAAC,wMAAwM;oBAAAS,QAAA,gBACvNpF,OAAA;sBAAAoF,QAAA,EAAM;oBAAa;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1B/E,OAAA;sBACEkD,IAAI,EAAC,MAAM;sBACXyB,SAAS,EAAC,SAAS;sBACnByB,MAAM,EAAC,4BAA4B;sBACnCH,QAAQ,EAAEtD,gBAAiB;sBAC3BuD,QAAQ;oBAAA;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACR/E,OAAA;oBAAG2E,SAAS,EAAC,MAAM;oBAAAS,QAAA,EAAC;kBAAgB;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACN/E,OAAA;kBAAG2E,SAAS,EAAC,uBAAuB;kBAAAS,QAAA,EAAC;gBAErC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL5D,YAAY,iBACXnB,OAAA;cAAK2E,SAAS,EAAC,kCAAkC;cAAAS,QAAA,gBAC/CpF,OAAA,CAACP,QAAQ;gBAACkF,SAAS,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9C/E,OAAA;gBAAM2E,SAAS,EAAC,uBAAuB;gBAAAS,QAAA,EAAEjE,YAAY,CAACkC;cAAI;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClE/E,OAAA;gBAAM2E,SAAS,EAAC,uBAAuB;gBAAAS,QAAA,GAAC,GACrC,EAAC,CAACjE,YAAY,CAACgC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEkD,OAAO,CAAC,CAAC,CAAC,EAAC,MACjD;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLxD,aAAa,iBACZvB,OAAA;YAAK2E,SAAS,EAAC,kDAAkD;YAAAS,QAAA,eAC/DpF,OAAA;cAAK2E,SAAS,EAAC,6BAA6B;cAAAS,QAAA,gBAC1CpF,OAAA;gBAAK2E,SAAS,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/C/E,OAAA;gBAAM2E,SAAS,EAAC,uBAAuB;gBAAAS,QAAA,EAAC;cAExC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED/E,OAAA;YAAK2E,SAAS,EAAC,4BAA4B;YAAAS,QAAA,gBACzCpF,OAAA;cACEkD,IAAI,EAAC,QAAQ;cACboC,OAAO,EAAEA,CAAA,KAAM5E,iBAAiB,CAAC,KAAK,CAAE;cACxCiE,SAAS,EAAC,mBAAmB;cAAAS,QAAA,EAC9B;YAED;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/E,OAAA;cACEkD,IAAI,EAAC,QAAQ;cACboD,QAAQ,EAAEjF,WAAW,IAAIE,aAAc;cACvCoD,SAAS,EAAC,iBAAiB;cAAAS,QAAA,EAE1B/D,WAAW,GAAG,cAAc,GAAG;YAAiB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzE,EAAA,CAncIH,aAAa;EAAA,QAcGd,cAAc,EAGSF,QAAQ,EAMpBC,WAAW;AAAA;AAAAmH,EAAA,GAvBtCpG,aAAa;AAqcnB,eAAeA,aAAa;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
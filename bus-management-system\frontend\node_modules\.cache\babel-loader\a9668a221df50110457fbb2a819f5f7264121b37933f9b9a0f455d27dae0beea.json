{"ast": null, "code": "// TYPES\n// FUNCTIONS\nvar logger = console;\nexport function getLogger() {\n  return logger;\n}\nexport function setLogger(newLogger) {\n  logger = newLogger;\n}", "map": {"version": 3, "names": ["logger", "console", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/core/logger.js"], "sourcesContent": ["// TYPES\n// FUNCTIONS\nvar logger = console;\nexport function getLogger() {\n  return logger;\n}\nexport function setLogger(newLogger) {\n  logger = newLogger;\n}"], "mappings": "AAAA;AACA;AACA,IAAIA,MAAM,GAAGC,OAAO;AACpB,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1B,OAAOF,MAAM;AACf;AACA,OAAO,SAASG,SAASA,CAACC,SAAS,EAAE;EACnCJ,MAAM,GAAGI,SAAS;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
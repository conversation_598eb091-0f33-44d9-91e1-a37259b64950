import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Galician locale.
 * @language Galician
 * @iso-639-2 glg
 * <AUTHOR> https://github.com/cocodinTech}
 * <AUTHOR> Pita [@fidelpita]{@link https://github.com/fidelpita}
 */
var locale = {
  code: 'gl',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1
  }
};
export default locale;
{"ast": null, "code": "import { styled } from './utils';\nexport var Panel = styled('div', function (_props, theme) {\n  return {\n    fontSize: 'clamp(12px, 1.5vw, 14px)',\n    fontFamily: \"sans-serif\",\n    display: 'flex',\n    backgroundColor: theme.background,\n    color: theme.foreground\n  };\n}, {\n  '(max-width: 700px)': {\n    flexDirection: 'column'\n  },\n  '(max-width: 600px)': {\n    fontSize: '.9em' // flexDirection: 'column',\n  }\n});\nexport var ActiveQueryPanel = styled('div', function () {\n  return {\n    flex: '1 1 500px',\n    display: 'flex',\n    flexDirection: 'column',\n    overflow: 'auto',\n    height: '100%'\n  };\n}, {\n  '(max-width: 700px)': function maxWidth700px(_props, theme) {\n    return {\n      borderTop: \"2px solid \" + theme.gray\n    };\n  }\n});\nexport var Button = styled('button', function (props, theme) {\n  return {\n    appearance: 'none',\n    fontSize: '.9em',\n    fontWeight: 'bold',\n    background: theme.gray,\n    border: '0',\n    borderRadius: '.3em',\n    color: 'white',\n    padding: '.5em',\n    opacity: props.disabled ? '.5' : undefined,\n    cursor: 'pointer'\n  };\n});\nexport var QueryKeys = styled('span', {\n  display: 'inline-block',\n  fontSize: '0.9em'\n});\nexport var QueryKey = styled('span', {\n  display: 'inline-flex',\n  alignItems: 'center',\n  padding: '.2em .4em',\n  fontWeight: 'bold',\n  textShadow: '0 0 10px black',\n  borderRadius: '.2em'\n});\nexport var Code = styled('code', {\n  fontSize: '.9em',\n  color: 'inherit',\n  background: 'inherit'\n});\nexport var Input = styled('input', function (_props, theme) {\n  return {\n    backgroundColor: theme.inputBackgroundColor,\n    border: 0,\n    borderRadius: '.2em',\n    color: theme.inputTextColor,\n    fontSize: '.9em',\n    lineHeight: \"1.3\",\n    padding: '.3em .4em'\n  };\n});\nexport var Select = styled('select', function (_props, theme) {\n  return {\n    display: \"inline-block\",\n    fontSize: \".9em\",\n    fontFamily: \"sans-serif\",\n    fontWeight: 'normal',\n    lineHeight: \"1.3\",\n    padding: \".3em 1.5em .3em .5em\",\n    height: 'auto',\n    border: 0,\n    borderRadius: \".2em\",\n    appearance: \"none\",\n    WebkitAppearance: 'none',\n    backgroundColor: theme.inputBackgroundColor,\n    backgroundImage: \"url(\\\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>\\\")\",\n    backgroundRepeat: \"no-repeat\",\n    backgroundPosition: \"right .55em center\",\n    backgroundSize: \".65em auto, 100%\",\n    color: theme.inputTextColor\n  };\n}, {\n  '(max-width: 500px)': {\n    display: 'none'\n  }\n});", "map": {"version": 3, "names": ["styled", "Panel", "_props", "theme", "fontSize", "fontFamily", "display", "backgroundColor", "background", "color", "foreground", "flexDirection", "ActiveQueryPanel", "flex", "overflow", "height", "maxWidth700px", "borderTop", "gray", "<PERSON><PERSON>", "props", "appearance", "fontWeight", "border", "borderRadius", "padding", "opacity", "disabled", "undefined", "cursor", "Query<PERSON><PERSON>s", "Query<PERSON>ey", "alignItems", "textShadow", "Code", "Input", "inputBackgroundColor", "inputTextColor", "lineHeight", "Select", "WebkitAppearance", "backgroundImage", "backgroundRepeat", "backgroundPosition", "backgroundSize"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/devtools/styledComponents.js"], "sourcesContent": ["import { styled } from './utils';\nexport var Panel = styled('div', function (_props, theme) {\n  return {\n    fontSize: 'clamp(12px, 1.5vw, 14px)',\n    fontFamily: \"sans-serif\",\n    display: 'flex',\n    backgroundColor: theme.background,\n    color: theme.foreground\n  };\n}, {\n  '(max-width: 700px)': {\n    flexDirection: 'column'\n  },\n  '(max-width: 600px)': {\n    fontSize: '.9em' // flexDirection: 'column',\n\n  }\n});\nexport var ActiveQueryPanel = styled('div', function () {\n  return {\n    flex: '1 1 500px',\n    display: 'flex',\n    flexDirection: 'column',\n    overflow: 'auto',\n    height: '100%'\n  };\n}, {\n  '(max-width: 700px)': function maxWidth700px(_props, theme) {\n    return {\n      borderTop: \"2px solid \" + theme.gray\n    };\n  }\n});\nexport var Button = styled('button', function (props, theme) {\n  return {\n    appearance: 'none',\n    fontSize: '.9em',\n    fontWeight: 'bold',\n    background: theme.gray,\n    border: '0',\n    borderRadius: '.3em',\n    color: 'white',\n    padding: '.5em',\n    opacity: props.disabled ? '.5' : undefined,\n    cursor: 'pointer'\n  };\n});\nexport var QueryKeys = styled('span', {\n  display: 'inline-block',\n  fontSize: '0.9em'\n});\nexport var QueryKey = styled('span', {\n  display: 'inline-flex',\n  alignItems: 'center',\n  padding: '.2em .4em',\n  fontWeight: 'bold',\n  textShadow: '0 0 10px black',\n  borderRadius: '.2em'\n});\nexport var Code = styled('code', {\n  fontSize: '.9em',\n  color: 'inherit',\n  background: 'inherit'\n});\nexport var Input = styled('input', function (_props, theme) {\n  return {\n    backgroundColor: theme.inputBackgroundColor,\n    border: 0,\n    borderRadius: '.2em',\n    color: theme.inputTextColor,\n    fontSize: '.9em',\n    lineHeight: \"1.3\",\n    padding: '.3em .4em'\n  };\n});\nexport var Select = styled('select', function (_props, theme) {\n  return {\n    display: \"inline-block\",\n    fontSize: \".9em\",\n    fontFamily: \"sans-serif\",\n    fontWeight: 'normal',\n    lineHeight: \"1.3\",\n    padding: \".3em 1.5em .3em .5em\",\n    height: 'auto',\n    border: 0,\n    borderRadius: \".2em\",\n    appearance: \"none\",\n    WebkitAppearance: 'none',\n    backgroundColor: theme.inputBackgroundColor,\n    backgroundImage: \"url(\\\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>\\\")\",\n    backgroundRepeat: \"no-repeat\",\n    backgroundPosition: \"right .55em center\",\n    backgroundSize: \".65em auto, 100%\",\n    color: theme.inputTextColor\n  };\n}, {\n  '(max-width: 500px)': {\n    display: 'none'\n  }\n});"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,OAAO,IAAIC,KAAK,GAAGD,MAAM,CAAC,KAAK,EAAE,UAAUE,MAAM,EAAEC,KAAK,EAAE;EACxD,OAAO;IACLC,QAAQ,EAAE,0BAA0B;IACpCC,UAAU,EAAE,YAAY;IACxBC,OAAO,EAAE,MAAM;IACfC,eAAe,EAAEJ,KAAK,CAACK,UAAU;IACjCC,KAAK,EAAEN,KAAK,CAACO;EACf,CAAC;AACH,CAAC,EAAE;EACD,oBAAoB,EAAE;IACpBC,aAAa,EAAE;EACjB,CAAC;EACD,oBAAoB,EAAE;IACpBP,QAAQ,EAAE,MAAM,CAAC;EAEnB;AACF,CAAC,CAAC;AACF,OAAO,IAAIQ,gBAAgB,GAAGZ,MAAM,CAAC,KAAK,EAAE,YAAY;EACtD,OAAO;IACLa,IAAI,EAAE,WAAW;IACjBP,OAAO,EAAE,MAAM;IACfK,aAAa,EAAE,QAAQ;IACvBG,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE;EACV,CAAC;AACH,CAAC,EAAE;EACD,oBAAoB,EAAE,SAASC,aAAaA,CAACd,MAAM,EAAEC,KAAK,EAAE;IAC1D,OAAO;MACLc,SAAS,EAAE,YAAY,GAAGd,KAAK,CAACe;IAClC,CAAC;EACH;AACF,CAAC,CAAC;AACF,OAAO,IAAIC,MAAM,GAAGnB,MAAM,CAAC,QAAQ,EAAE,UAAUoB,KAAK,EAAEjB,KAAK,EAAE;EAC3D,OAAO;IACLkB,UAAU,EAAE,MAAM;IAClBjB,QAAQ,EAAE,MAAM;IAChBkB,UAAU,EAAE,MAAM;IAClBd,UAAU,EAAEL,KAAK,CAACe,IAAI;IACtBK,MAAM,EAAE,GAAG;IACXC,YAAY,EAAE,MAAM;IACpBf,KAAK,EAAE,OAAO;IACdgB,OAAO,EAAE,MAAM;IACfC,OAAO,EAAEN,KAAK,CAACO,QAAQ,GAAG,IAAI,GAAGC,SAAS;IAC1CC,MAAM,EAAE;EACV,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAIC,SAAS,GAAG9B,MAAM,CAAC,MAAM,EAAE;EACpCM,OAAO,EAAE,cAAc;EACvBF,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,OAAO,IAAI2B,QAAQ,GAAG/B,MAAM,CAAC,MAAM,EAAE;EACnCM,OAAO,EAAE,aAAa;EACtB0B,UAAU,EAAE,QAAQ;EACpBP,OAAO,EAAE,WAAW;EACpBH,UAAU,EAAE,MAAM;EAClBW,UAAU,EAAE,gBAAgB;EAC5BT,YAAY,EAAE;AAChB,CAAC,CAAC;AACF,OAAO,IAAIU,IAAI,GAAGlC,MAAM,CAAC,MAAM,EAAE;EAC/BI,QAAQ,EAAE,MAAM;EAChBK,KAAK,EAAE,SAAS;EAChBD,UAAU,EAAE;AACd,CAAC,CAAC;AACF,OAAO,IAAI2B,KAAK,GAAGnC,MAAM,CAAC,OAAO,EAAE,UAAUE,MAAM,EAAEC,KAAK,EAAE;EAC1D,OAAO;IACLI,eAAe,EAAEJ,KAAK,CAACiC,oBAAoB;IAC3Cb,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,MAAM;IACpBf,KAAK,EAAEN,KAAK,CAACkC,cAAc;IAC3BjC,QAAQ,EAAE,MAAM;IAChBkC,UAAU,EAAE,KAAK;IACjBb,OAAO,EAAE;EACX,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAIc,MAAM,GAAGvC,MAAM,CAAC,QAAQ,EAAE,UAAUE,MAAM,EAAEC,KAAK,EAAE;EAC5D,OAAO;IACLG,OAAO,EAAE,cAAc;IACvBF,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,YAAY;IACxBiB,UAAU,EAAE,QAAQ;IACpBgB,UAAU,EAAE,KAAK;IACjBb,OAAO,EAAE,sBAAsB;IAC/BV,MAAM,EAAE,MAAM;IACdQ,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,MAAM;IACpBH,UAAU,EAAE,MAAM;IAClBmB,gBAAgB,EAAE,MAAM;IACxBjC,eAAe,EAAEJ,KAAK,CAACiC,oBAAoB;IAC3CK,eAAe,EAAE,gKAAgK;IACjLC,gBAAgB,EAAE,WAAW;IAC7BC,kBAAkB,EAAE,oBAAoB;IACxCC,cAAc,EAAE,kBAAkB;IAClCnC,KAAK,EAAEN,KAAK,CAACkC;EACf,CAAC;AACH,CAAC,EAAE;EACD,oBAAoB,EAAE;IACpB/B,OAAO,EAAE;EACX;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst SaveAll = createLucideIcon(\"SaveAll\", [[\"path\", {\n  d: \"M6 4a2 2 0 0 1 2-2h10l4 4v10.2a2 2 0 0 1-2 1.8H8a2 2 0 0 1-2-2Z\",\n  key: \"1unput\"\n}], [\"path\", {\n  d: \"M10 2v4h6\",\n  key: \"1p5sg6\"\n}], [\"path\", {\n  d: \"M18 18v-7h-8v7\",\n  key: \"1oniuk\"\n}], [\"path\", {\n  d: \"M18 22H4a2 2 0 0 1-2-2V6\",\n  key: \"pblm9e\"\n}]]);\nexport { SaveAll as default };", "map": {"version": 3, "names": ["SaveAll", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\save-all.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SaveAll\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiA0YTIgMiAwIDAgMSAyLTJoMTBsNCA0djEwLjJhMiAyIDAgMCAxLTIgMS44SDhhMiAyIDAgMCAxLTItMloiIC8+CiAgPHBhdGggZD0iTTEwIDJ2NGg2IiAvPgogIDxwYXRoIGQ9Ik0xOCAxOHYtN2gtOHY3IiAvPgogIDxwYXRoIGQ9Ik0xOCAyMkg0YTIgMiAwIDAgMS0yLTJWNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/save-all\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SaveAll = createLucideIcon('SaveAll', [\n  [\n    'path',\n    {\n      d: 'M6 4a2 2 0 0 1 2-2h10l4 4v10.2a2 2 0 0 1-2 1.8H8a2 2 0 0 1-2-2Z',\n      key: '1unput',\n    },\n  ],\n  ['path', { d: 'M10 2v4h6', key: '1p5sg6' }],\n  ['path', { d: 'M18 18v-7h-8v7', key: '1oniuk' }],\n  ['path', { d: 'M18 22H4a2 2 0 0 1-2-2V6', key: 'pblm9e' }],\n]);\n\nexport default SaveAll;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary German locale.
 * @language German
 * @iso-639-2 deu
 * <AUTHOR> [@DeMuu]{@link https://github.com/DeMuu}
 * <AUTHOR> [@asia-t]{@link https://github.com/asia-t}
 * <AUTHOR> [@vanvuongngo]{@link https://github.com/vanvuongngo}
 * <AUTHOR> [@pex]{@link https://github.com/pex}
 * <AUTHOR> [@Philipp91]{@link https://github.com/Philipp91}
 */
var locale = {
  code: 'de',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4
  }
};
export default locale;
{"name": "tryer", "version": "1.0.1", "description": "Because everyone loves a tryer! Conditional and repeated task invocation for node and browser.", "homepage": "https://gitlab.com/philbooth/tryer", "bugs": "https://gitlab.com/philbooth/tryer/issues", "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://philbooth.me/)", "main": "./src/tryer", "repository": {"type": "git", "url": "git+https://gitlab.com/philbooth/tryer.git"}, "keywords": ["repeat", "retry", "predicate", "conditional", "invocation", "execution", "loop", "condition", "termination", "exponential", "backoff"], "devDependencies": {"chai": "4.1.x", "jshint": "2.9.x", "mocha": "5.2.x", "please-release-me": "2.0.x", "spooks": "2.0.x", "uglify-js": "3.4.x"}, "scripts": {"lint": "jshint src/tryer.js test/unit.js", "test": "mocha --ui tdd --reporter spec --colors test/unit.js", "minify": "uglifyjs ./src/tryer.js --compress --mangle --output ./lib/tryer.min.js"}}
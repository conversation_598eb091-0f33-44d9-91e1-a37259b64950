from datetime import datetime, date
from app import db, ma

class Student(db.Model):
    """Student model."""
    __tablename__ = 'students'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('users.id'), nullable=False, unique=True)
    roll_number = db.Column(db.String(20), nullable=False, unique=True, index=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    date_of_birth = db.Column(db.Date)
    gender = db.Column(db.Enum('Male', 'Female', 'Other', name='gender_types'))
    phone = db.Column(db.String(15))
    address = db.Column(db.Text)
    department_id = db.Column(db.Integer, db.<PERSON>('departments.id'), nullable=False)
    year = db.Column(db.Integer, nullable=False)  # 1, 2, 3, 4
    semester = db.Column(db.Integer, nullable=False)  # 1-8
    admission_date = db.Column(db.Date, default=date.today)
    guardian_name = db.Column(db.String(100))
    guardian_phone = db.Column(db.String(15))
    guardian_email = db.Column(db.String(120))
    blood_group = db.Column(db.String(5))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    enrollments = db.relationship('Enrollment', backref='student', lazy='dynamic', 
                                 cascade='all, delete-orphan')
    attendances = db.relationship('Attendance', backref='student', lazy='dynamic',
                                 cascade='all, delete-orphan')
    grades = db.relationship('Grade', backref='student', lazy='dynamic',
                            cascade='all, delete-orphan')
    fees = db.relationship('Fee', backref='student', lazy='dynamic',
                          cascade='all, delete-orphan')
    
    @property
    def full_name(self):
        """Get full name."""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self):
        """Calculate age from date of birth."""
        if self.date_of_birth:
            today = date.today()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None
    
    def get_current_courses(self):
        """Get currently enrolled courses."""
        return [enrollment.course for enrollment in 
                self.enrollments.filter_by(is_active=True).all()]
    
    def to_dict(self):
        """Convert student to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'roll_number': self.roll_number,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'date_of_birth': self.date_of_birth.isoformat() if self.date_of_birth else None,
            'age': self.age,
            'gender': self.gender,
            'phone': self.phone,
            'address': self.address,
            'department_id': self.department_id,
            'department_name': self.department.name if self.department else None,
            'year': self.year,
            'semester': self.semester,
            'admission_date': self.admission_date.isoformat() if self.admission_date else None,
            'guardian_name': self.guardian_name,
            'guardian_phone': self.guardian_phone,
            'guardian_email': self.guardian_email,
            'blood_group': self.blood_group,
            'is_active': self.is_active,
            'email': self.user.email if self.user else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Student {self.roll_number}: {self.full_name}>'

class StudentSchema(ma.SQLAlchemyAutoSchema):
    """Student serialization schema."""
    class Meta:
        model = Student
        load_instance = True
        include_fk = True

student_schema = StudentSchema()
students_schema = StudentSchema(many=True)

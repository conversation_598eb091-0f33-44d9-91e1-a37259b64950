!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react-query")):"function"==typeof define&&define.amd?define(["exports","react-query"],t):t((e=e||self).ReactQueryPersistQueryClientExperimental={},e.<PERSON>actQuery)}(this,(function(e,t){"use strict";var r=console;function n(){return r}function i(){}var o,u=(o=function(e){var r=e.queryClient,o=e.persistor,u=e.maxAge,a=void 0===u?864e5:u,c=e.buster,s=void 0===c?"":c,f=e.hydrateOptions,d=e.dehydrateOptions;return function(e){var t=e();if(t&&t.then)return t.then(i)}((function(){if("undefined"!=typeof window){var e=function(){var e={buster:s,timestamp:Date.now(),clientState:t.dehydrate(r,d)};o.persistClient(e)};return function(e,t){return e&&e.then?e.then(t):t(e)}(function(e,t){try{var r=e()}catch(e){return t(e)}return r&&r.then?r.then(void 0,t):r}((function(){return e=o.restoreClient(),n=function(e){if(e)if(e.timestamp){var n=Date.now()-e.timestamp>a,i=e.buster!==s;n||i?o.removeClient():t.hydrate(r,e.clientState,f)}else o.removeClient()},i?n?n(e):e:(e&&e.then||(e=Promise.resolve(e)),n?e.then(n):e);var e,n,i}),(function(e){n().error(e),n().warn("Encountered an error attempting to restore client cache from persisted location. As a precaution, the persisted cache will be discarded."),o.removeClient()})),(function(){r.getQueryCache().subscribe(e)}))}}))},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return Promise.resolve(o.apply(this,e))}catch(e){return Promise.reject(e)}});e.persistQueryClient=u,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=persistQueryClient-experimental.production.min.js.map

{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/transform.ts", "../src/index.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/core\";\nimport { isTransparentExprWrapper } from \"@babel/helper-skip-transparent-expression-wrappers\";\n/**\n * Test if a NodePath will be cast to boolean when evaluated.\n * It respects transparent expression wrappers defined in\n * \"@babel/helper-skip-transparent-expression-wrappers\"\n *\n * @example\n * // returns true\n * const nodePathADotB = NodePath(\"if (a.b) {}\").get(\"test\"); // a.b\n * willPathCastToBoolean(nodePathADotB)\n * @example\n * // returns false\n * willPathCastToBoolean(NodePath(\"a.b\"))\n * @param {NodePath} path\n * @returns {boolean}\n */\nexport function willPathCastToBoolean(path: NodePath): boolean {\n  const maybeWrapped = findOutermostTransparentParent(path);\n  const { node, parentPath } = maybeWrapped;\n  if (parentPath.isLogicalExpression()) {\n    const { operator, right } = parentPath.node;\n    if (\n      operator === \"&&\" ||\n      operator === \"||\" ||\n      (operator === \"??\" && node === right)\n    ) {\n      return willPathCastToBoolean(parentPath);\n    }\n  }\n  if (parentPath.isSequenceExpression()) {\n    const { expressions } = parentPath.node;\n    if (expressions[expressions.length - 1] === node) {\n      return willPathCastToBoolean(parentPath);\n    } else {\n      // if it is in the middle of a sequence expression, we don't\n      // care the return value so just cast to boolean for smaller\n      // output\n      return true;\n    }\n  }\n  return (\n    parentPath.isConditional({ test: node }) ||\n    parentPath.isUnaryExpression({ operator: \"!\" }) ||\n    parentPath.isLoop({ test: node })\n  );\n}\n\n/**\n * Return the outermost transparent expression wrapper of a given path,\n * otherwise returns path itself.\n * @example\n * const nodePathADotB = NodePath(\"(a.b as any)\").get(\"expression\"); // a.b\n * // returns NodePath(\"(a.b as any)\")\n * findOutermostTransparentParent(nodePathADotB);\n * @param {NodePath} path\n * @returns {NodePath}\n */\nexport function findOutermostTransparentParent(path: NodePath): NodePath {\n  let maybeWrapped = path;\n  path.findParent(p => {\n    if (!isTransparentExprWrapper(p.node)) return true;\n    maybeWrapped = p;\n  });\n  return maybeWrapped;\n}\n", "import { types as t, template, type NodePath } from \"@babel/core\";\nimport {\n  skipTransparentExprWrapperNodes,\n  skipTransparentExprWrappers,\n} from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport {\n  willPathCastToBoolean,\n  findOutermostTransparentParent,\n} from \"./util.ts\";\n\n// TODO(Babel 9): Use .at(-1)\nconst last = <T>(arr: T[]) => arr[arr.length - 1];\n\nfunction isSimpleMemberExpression(\n  expression: t.Expression | t.Super,\n): expression is t.Identifier | t.Super | t.MemberExpression {\n  expression = skipTransparentExprWrapperNodes(expression);\n  return (\n    t.isIdentifier(expression) ||\n    t.isSuper(expression) ||\n    (t.isMemberExpression(expression) &&\n      !expression.computed &&\n      isSimpleMemberExpression(expression.object))\n  );\n}\n\n/**\n * Test if a given optional chain `path` needs to be memoized\n * @param {NodePath} path\n * @returns {boolean}\n */\nfunction needsMemoize(\n  path: NodePath<t.OptionalCallExpression | t.OptionalMemberExpression>,\n) {\n  let optionalPath: NodePath = path;\n  const { scope } = path;\n  while (\n    optionalPath.isOptionalMemberExpression() ||\n    optionalPath.isOptionalCallExpression()\n  ) {\n    const { node } = optionalPath;\n    const childPath = skipTransparentExprWrappers(\n      optionalPath.isOptionalMemberExpression()\n        ? optionalPath.get(\"object\")\n        : optionalPath.get(\"callee\"),\n    );\n    if (node.optional) {\n      return !scope.isStatic(childPath.node);\n    }\n\n    optionalPath = childPath;\n  }\n}\n\nconst NULLISH_CHECK = template.expression(\n  `%%check%% === null || %%ref%% === void 0`,\n);\nconst NULLISH_CHECK_NO_DDA = template.expression(`%%check%% == null`);\nconst NULLISH_CHECK_NEG = template.expression(\n  `%%check%% !== null && %%ref%% !== void 0`,\n);\nconst NULLISH_CHECK_NO_DDA_NEG = template.expression(`%%check%% != null`);\n\ninterface OptionalChainAssumptions {\n  pureGetters: boolean;\n  noDocumentAll: boolean;\n}\n\nexport function transformOptionalChain(\n  path: NodePath<t.OptionalCallExpression | t.OptionalMemberExpression>,\n  { pureGetters, noDocumentAll }: OptionalChainAssumptions,\n  replacementPath: NodePath<t.Expression>,\n  ifNullish: t.Expression,\n  wrapLast?: (value: t.Expression) => t.Expression,\n) {\n  const { scope } = path;\n\n  // Replace `function (a, x = a.b?.c) {}` to `function (a, x = (() => a.b?.c)() ){}`\n  // so the temporary variable can be injected in correct scope\n  if (scope.path.isPattern() && needsMemoize(path)) {\n    replacementPath.replaceWith(\n      template.expression.ast`(() => ${replacementPath.node})()`,\n    );\n    // The injected optional chain will be queued and eventually transformed when visited\n    return;\n  }\n\n  const optionals = [];\n\n  let optionalPath = path;\n  while (\n    optionalPath.isOptionalMemberExpression() ||\n    optionalPath.isOptionalCallExpression()\n  ) {\n    const { node } = optionalPath;\n    if (node.optional) {\n      optionals.push(node);\n    }\n    if (optionalPath.isOptionalMemberExpression()) {\n      // @ts-expect-error todo(flow->ts) avoid changing more type\n      optionalPath.node.type = \"MemberExpression\";\n      // @ts-expect-error todo(flow->ts)\n      optionalPath = skipTransparentExprWrappers(optionalPath.get(\"object\"));\n    } else if (optionalPath.isOptionalCallExpression()) {\n      // @ts-expect-error todo(flow->ts) avoid changing more type\n      optionalPath.node.type = \"CallExpression\";\n      // @ts-expect-error todo(flow->ts)\n      optionalPath = skipTransparentExprWrappers(optionalPath.get(\"callee\"));\n    }\n  }\n\n  if (optionals.length === 0) {\n    // Malformed AST: there was an OptionalMemberExpression node\n    // with no actual optional elements.\n    return;\n  }\n\n  const checks = [];\n\n  let tmpVar;\n\n  for (let i = optionals.length - 1; i >= 0; i--) {\n    const node = optionals[i] as unknown as\n      | t.MemberExpression\n      | t.CallExpression;\n\n    const isCall = t.isCallExpression(node);\n\n    const chainWithTypes = isCall\n      ? // V8 intrinsics must not be an optional call\n        (node.callee as t.Expression)\n      : node.object;\n    const chain = skipTransparentExprWrapperNodes(chainWithTypes);\n\n    let ref;\n    let check;\n    if (isCall && t.isIdentifier(chain, { name: \"eval\" })) {\n      check = ref = chain;\n      // `eval?.()` is an indirect eval call transformed to `(0,eval)()`\n      node.callee = t.sequenceExpression([t.numericLiteral(0), ref]);\n    } else if (pureGetters && isCall && isSimpleMemberExpression(chain)) {\n      // If we assume getters are pure (avoiding a Function#call) and we are at the call,\n      // we can avoid a needless memoize. We only do this if the callee is a simple member\n      // expression, to avoid multiple calls to nested call expressions.\n      check = ref = node.callee;\n    } else if (scope.isStatic(chain)) {\n      check = ref = chainWithTypes;\n    } else {\n      // We cannot reuse the tmpVar for calls, because we need to\n      // store both the method and the receiver.\n      if (!tmpVar || isCall) {\n        tmpVar = scope.generateUidIdentifierBasedOnNode(chain);\n        scope.push({ id: t.cloneNode(tmpVar) });\n      }\n      ref = tmpVar;\n      check = t.assignmentExpression(\n        \"=\",\n        t.cloneNode(tmpVar),\n        // Here `chainWithTypes` MUST NOT be cloned because it could be\n        // updated when generating the memoised context of a call\n        // expression. It must be an Expression when `ref` is an identifier\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        chainWithTypes as t.Expression,\n      );\n\n      if (isCall) {\n        node.callee = ref;\n      } else {\n        node.object = ref;\n      }\n    }\n\n    // Ensure call expressions have the proper `this`\n    // `foo.bar()` has context `foo`.\n    if (isCall && t.isMemberExpression(chain)) {\n      if (pureGetters && isSimpleMemberExpression(chain)) {\n        // To avoid a Function#call, we can instead re-grab the property from the context object.\n        // `a.?b.?()` translates roughly to `_a.b != null && _a.b()`\n        node.callee = chainWithTypes;\n      } else {\n        // Otherwise, we need to memoize the context object, and change the call into a Function#call.\n        // `a.?b.?()` translates roughly to `(_b = _a.b) != null && _b.call(_a)`\n        const { object } = chain;\n        let context: t.Expression;\n        if (t.isSuper(object)) {\n          context = t.thisExpression();\n        } else {\n          const memoized = scope.maybeGenerateMemoised(object);\n          if (memoized) {\n            context = memoized;\n            chain.object = t.assignmentExpression(\"=\", memoized, object);\n          } else {\n            context = object;\n          }\n        }\n\n        node.arguments.unshift(t.cloneNode(context));\n        // @ts-expect-error node.callee can not be an V8IntrinsicIdentifier: V8 intrinsic is disallowed in optional chain\n        node.callee = t.memberExpression(node.callee, t.identifier(\"call\"));\n      }\n    }\n\n    const data = { check: t.cloneNode(check), ref: t.cloneNode(ref) };\n    // We make `ref` non-enumerable, so that @babel/template doesn't throw\n    // in the noDocumentAll template if it's not used.\n    Object.defineProperty(data, \"ref\", { enumerable: false });\n    checks.push(data);\n  }\n\n  let result = replacementPath.node;\n  if (wrapLast) result = wrapLast(result);\n\n  const ifNullishBoolean = t.isBooleanLiteral(ifNullish);\n  const ifNullishFalse = ifNullishBoolean && ifNullish.value === false;\n  const ifNullishVoid =\n    !ifNullishBoolean && t.isUnaryExpression(ifNullish, { operator: \"void\" });\n\n  const isEvaluationValueIgnored =\n    (t.isExpressionStatement(replacementPath.parent) &&\n      !replacementPath.isCompletionRecord()) ||\n    (t.isSequenceExpression(replacementPath.parent) &&\n      last(replacementPath.parent.expressions) !== replacementPath.node);\n\n  // prettier-ignore\n  const tpl = ifNullishFalse\n    ? (noDocumentAll ? NULLISH_CHECK_NO_DDA_NEG : NULLISH_CHECK_NEG)\n    : (noDocumentAll ? NULLISH_CHECK_NO_DDA : NULLISH_CHECK);\n  const logicalOp = ifNullishFalse ? \"&&\" : \"||\";\n\n  const check = checks\n    .map(tpl)\n    .reduce((expr, check) => t.logicalExpression(logicalOp, expr, check));\n\n  replacementPath.replaceWith(\n    ifNullishBoolean || (ifNullishVoid && isEvaluationValueIgnored)\n      ? t.logicalExpression(logicalOp, check, result)\n      : t.conditionalExpression(check, ifNullish, result),\n  );\n}\n\nexport function transform(\n  path: NodePath<t.OptionalCallExpression | t.OptionalMemberExpression>,\n  assumptions: OptionalChainAssumptions,\n) {\n  const { scope } = path;\n\n  // maybeWrapped points to the outermost transparent expression wrapper\n  // or the path itself\n  const maybeWrapped = findOutermostTransparentParent(path);\n  const { parentPath } = maybeWrapped;\n\n  if (parentPath.isUnaryExpression({ operator: \"delete\" })) {\n    transformOptionalChain(\n      path,\n      assumptions,\n      parentPath,\n      t.booleanLiteral(true),\n    );\n  } else {\n    let wrapLast;\n    if (\n      parentPath.isCallExpression({ callee: maybeWrapped.node }) &&\n      // note that the first condition must implies that `path.optional` is `true`,\n      // otherwise the parentPath should be an OptionalCallExpression\n      path.isOptionalMemberExpression()\n    ) {\n      // Ensure (a?.b)() has proper `this`\n      wrapLast = (replacement: t.MemberExpression) => {\n        // `(a?.b)()` to `(a == null ? undefined : a.b.bind(a))()`\n        // object must not be Super as super?.foo is invalid\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        const object = skipTransparentExprWrapperNodes(\n          replacement.object,\n        ) as t.Expression;\n        let baseRef: t.Expression;\n        if (!assumptions.pureGetters || !isSimpleMemberExpression(object)) {\n          // memoize the context object when getters are not always pure\n          // or the object is not a simple member expression\n          // `(a?.b.c)()` to `(a == null ? undefined : (_a$b = a.b).c.bind(_a$b))()`\n          baseRef = scope.maybeGenerateMemoised(object);\n          if (baseRef) {\n            replacement.object = t.assignmentExpression(\"=\", baseRef, object);\n          }\n        }\n        return t.callExpression(\n          t.memberExpression(replacement, t.identifier(\"bind\")),\n          [t.cloneNode(baseRef ?? object)],\n        );\n      };\n    }\n\n    transformOptionalChain(\n      path,\n      assumptions,\n      path,\n      willPathCastToBoolean(maybeWrapped)\n        ? t.booleanLiteral(false)\n        : scope.buildUndefinedNode(),\n      wrapLast,\n    );\n  }\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { transform, transformOptionalChain } from \"./transform.ts\";\nimport type { NodePath, types as t } from \"@babel/core\";\n\nexport interface Options {\n  loose?: boolean;\n}\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { loose = false } = options;\n  const noDocumentAll = api.assumption(\"noDocumentAll\") ?? loose;\n  const pureGetters = api.assumption(\"pureGetters\") ?? loose;\n\n  return {\n    name: \"transform-optional-chaining\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"optionalChaining\"),\n\n    visitor: {\n      \"OptionalCallExpression|OptionalMemberExpression\"(\n        path: NodePath<t.OptionalCallExpression | t.OptionalMemberExpression>,\n      ) {\n        transform(path, { noDocumentAll, pureGetters });\n      },\n    },\n  };\n});\n\nexport { transform, transformOptionalChain };\n"], "names": ["willPathCastToBoolean", "path", "maybeWrapped", "findOutermostTransparentParent", "node", "parentPath", "isLogicalExpression", "operator", "right", "isSequenceExpression", "expressions", "length", "isConditional", "test", "isUnaryExpression", "isLoop", "findParent", "p", "isTransparentExprWrapper", "last", "arr", "isSimpleMemberExpression", "expression", "skipTransparentExprWrapperNodes", "t", "isIdentifier", "is<PERSON><PERSON><PERSON>", "isMemberExpression", "computed", "object", "needsMemoize", "optionalPath", "scope", "isOptionalMemberExpression", "isOptionalCallExpression", "child<PERSON><PERSON>", "skipTransparentExprWrappers", "get", "optional", "isStatic", "NULLISH_CHECK", "template", "NULLISH_CHECK_NO_DDA", "NULLISH_CHECK_NEG", "NULLISH_CHECK_NO_DDA_NEG", "transformOptionalChain", "pureGetters", "noDocumentAll", "<PERSON><PERSON><PERSON>", "if<PERSON><PERSON>ish", "wrapLast", "isPattern", "replaceWith", "ast", "optionals", "push", "type", "checks", "tmpVar", "i", "isCall", "isCallExpression", "chainWithTypes", "callee", "chain", "ref", "check", "name", "sequenceExpression", "numericLiteral", "generateUidIdentifierBasedOnNode", "id", "cloneNode", "assignmentExpression", "context", "thisExpression", "memoized", "maybeGenerateMemoised", "arguments", "unshift", "memberExpression", "identifier", "data", "Object", "defineProperty", "enumerable", "result", "ifNullishBoolean", "isBooleanLiteral", "ifNullishFalse", "value", "ifNullishVoid", "isEvaluationValueIgnored", "isExpressionStatement", "parent", "isCompletionRecord", "tpl", "logicalOp", "map", "reduce", "expr", "logicalExpression", "conditionalExpression", "transform", "assumptions", "booleanLiteral", "replacement", "baseRef", "callExpression", "buildUndefinedNode", "declare", "api", "options", "_api$assumption", "_api$assumption2", "assertVersion", "loose", "assumption", "manipulateOptions", "_", "parser", "plugins", "visitor", "OptionalCallExpression|OptionalMemberExpression"], "mappings": ";;;;;;;;AAiBO,SAASA,qBAAqBA,CAACC,IAAc,EAAW;AAC7D,EAAA,MAAMC,YAAY,GAAGC,8BAA8B,CAACF,IAAI,CAAC,CAAA;EACzD,MAAM;IAAEG,IAAI;AAAEC,IAAAA,UAAAA;AAAW,GAAC,GAAGH,YAAY,CAAA;AACzC,EAAA,IAAIG,UAAU,CAACC,mBAAmB,EAAE,EAAE;IACpC,MAAM;MAAEC,QAAQ;AAAEC,MAAAA,KAAAA;KAAO,GAAGH,UAAU,CAACD,IAAI,CAAA;AAC3C,IAAA,IACEG,QAAQ,KAAK,IAAI,IACjBA,QAAQ,KAAK,IAAI,IAChBA,QAAQ,KAAK,IAAI,IAAIH,IAAI,KAAKI,KAAM,EACrC;MACA,OAAOR,qBAAqB,CAACK,UAAU,CAAC,CAAA;AAC1C,KAAA;AACF,GAAA;AACA,EAAA,IAAIA,UAAU,CAACI,oBAAoB,EAAE,EAAE;IACrC,MAAM;AAAEC,MAAAA,WAAAA;KAAa,GAAGL,UAAU,CAACD,IAAI,CAAA;IACvC,IAAIM,WAAW,CAACA,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKP,IAAI,EAAE;MAChD,OAAOJ,qBAAqB,CAACK,UAAU,CAAC,CAAA;AAC1C,KAAC,MAAM;AAIL,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;EACA,OACEA,UAAU,CAACO,aAAa,CAAC;AAAEC,IAAAA,IAAI,EAAET,IAAAA;AAAK,GAAC,CAAC,IACxCC,UAAU,CAACS,iBAAiB,CAAC;AAAEP,IAAAA,QAAQ,EAAE,GAAA;AAAI,GAAC,CAAC,IAC/CF,UAAU,CAACU,MAAM,CAAC;AAAEF,IAAAA,IAAI,EAAET,IAAAA;AAAK,GAAC,CAAC,CAAA;AAErC,CAAA;AAYO,SAASD,8BAA8BA,CAACF,IAAc,EAAY;EACvE,IAAIC,YAAY,GAAGD,IAAI,CAAA;AACvBA,EAAAA,IAAI,CAACe,UAAU,CAACC,CAAC,IAAI;IACnB,IAAI,CAACC,gEAAwB,CAACD,CAAC,CAACb,IAAI,CAAC,EAAE,OAAO,IAAI,CAAA;AAClDF,IAAAA,YAAY,GAAGe,CAAC,CAAA;AAClB,GAAC,CAAC,CAAA;AACF,EAAA,OAAOf,YAAY,CAAA;AACrB;;ACtDA,MAAMiB,IAAI,GAAOC,GAAQ,IAAKA,GAAG,CAACA,GAAG,CAACT,MAAM,GAAG,CAAC,CAAC,CAAA;AAEjD,SAASU,wBAAwBA,CAC/BC,UAAkC,EACyB;AAC3DA,EAAAA,UAAU,GAAGC,uEAA+B,CAACD,UAAU,CAAC,CAAA;AACxD,EAAA,OACEE,UAAC,CAACC,YAAY,CAACH,UAAU,CAAC,IAC1BE,UAAC,CAACE,OAAO,CAACJ,UAAU,CAAC,IACpBE,UAAC,CAACG,kBAAkB,CAACL,UAAU,CAAC,IAC/B,CAACA,UAAU,CAACM,QAAQ,IACpBP,wBAAwB,CAACC,UAAU,CAACO,MAAM,CAAE,CAAA;AAElD,CAAA;AAOA,SAASC,YAAYA,CACnB7B,IAAqE,EACrE;EACA,IAAI8B,YAAsB,GAAG9B,IAAI,CAAA;EACjC,MAAM;AAAE+B,IAAAA,KAAAA;AAAM,GAAC,GAAG/B,IAAI,CAAA;EACtB,OACE8B,YAAY,CAACE,0BAA0B,EAAE,IACzCF,YAAY,CAACG,wBAAwB,EAAE,EACvC;IACA,MAAM;AAAE9B,MAAAA,IAAAA;AAAK,KAAC,GAAG2B,YAAY,CAAA;IAC7B,MAAMI,SAAS,GAAGC,mEAA2B,CAC3CL,YAAY,CAACE,0BAA0B,EAAE,GACrCF,YAAY,CAACM,GAAG,CAAC,QAAQ,CAAC,GAC1BN,YAAY,CAACM,GAAG,CAAC,QAAQ,CAC/B,CAAC,CAAA;IACD,IAAIjC,IAAI,CAACkC,QAAQ,EAAE;MACjB,OAAO,CAACN,KAAK,CAACO,QAAQ,CAACJ,SAAS,CAAC/B,IAAI,CAAC,CAAA;AACxC,KAAA;AAEA2B,IAAAA,YAAY,GAAGI,SAAS,CAAA;AAC1B,GAAA;AACF,CAAA;AAEA,MAAMK,aAAa,GAAGC,aAAQ,CAACnB,UAAU,CACvC,0CACF,CAAC,CAAA;AACD,MAAMoB,oBAAoB,GAAGD,aAAQ,CAACnB,UAAU,CAAC,mBAAmB,CAAC,CAAA;AACrE,MAAMqB,iBAAiB,GAAGF,aAAQ,CAACnB,UAAU,CAC3C,0CACF,CAAC,CAAA;AACD,MAAMsB,wBAAwB,GAAGH,aAAQ,CAACnB,UAAU,CAAC,mBAAmB,CAAC,CAAA;AAOlE,SAASuB,sBAAsBA,CACpC5C,IAAqE,EACrE;EAAE6C,WAAW;AAAEC,EAAAA,aAAAA;AAAwC,CAAC,EACxDC,eAAuC,EACvCC,SAAuB,EACvBC,QAAgD,EAChD;EACA,MAAM;AAAElB,IAAAA,KAAAA;AAAM,GAAC,GAAG/B,IAAI,CAAA;AAItB,EAAA,IAAI+B,KAAK,CAAC/B,IAAI,CAACkD,SAAS,EAAE,IAAIrB,YAAY,CAAC7B,IAAI,CAAC,EAAE;AAChD+C,IAAAA,eAAe,CAACI,WAAW,CACzBX,aAAQ,CAACnB,UAAU,CAAC+B,GAAG,CAAUL,OAAAA,EAAAA,eAAe,CAAC5C,IAAI,KACvD,CAAC,CAAA;AAED,IAAA,OAAA;AACF,GAAA;EAEA,MAAMkD,SAAS,GAAG,EAAE,CAAA;EAEpB,IAAIvB,YAAY,GAAG9B,IAAI,CAAA;EACvB,OACE8B,YAAY,CAACE,0BAA0B,EAAE,IACzCF,YAAY,CAACG,wBAAwB,EAAE,EACvC;IACA,MAAM;AAAE9B,MAAAA,IAAAA;AAAK,KAAC,GAAG2B,YAAY,CAAA;IAC7B,IAAI3B,IAAI,CAACkC,QAAQ,EAAE;AACjBgB,MAAAA,SAAS,CAACC,IAAI,CAACnD,IAAI,CAAC,CAAA;AACtB,KAAA;AACA,IAAA,IAAI2B,YAAY,CAACE,0BAA0B,EAAE,EAAE;AAE7CF,MAAAA,YAAY,CAAC3B,IAAI,CAACoD,IAAI,GAAG,kBAAkB,CAAA;MAE3CzB,YAAY,GAAGK,mEAA2B,CAACL,YAAY,CAACM,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;AACxE,KAAC,MAAM,IAAIN,YAAY,CAACG,wBAAwB,EAAE,EAAE;AAElDH,MAAAA,YAAY,CAAC3B,IAAI,CAACoD,IAAI,GAAG,gBAAgB,CAAA;MAEzCzB,YAAY,GAAGK,mEAA2B,CAACL,YAAY,CAACM,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;AACxE,KAAA;AACF,GAAA;AAEA,EAAA,IAAIiB,SAAS,CAAC3C,MAAM,KAAK,CAAC,EAAE;AAG1B,IAAA,OAAA;AACF,GAAA;EAEA,MAAM8C,MAAM,GAAG,EAAE,CAAA;AAEjB,EAAA,IAAIC,MAAM,CAAA;AAEV,EAAA,KAAK,IAAIC,CAAC,GAAGL,SAAS,CAAC3C,MAAM,GAAG,CAAC,EAAEgD,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AAC9C,IAAA,MAAMvD,IAAI,GAAGkD,SAAS,CAACK,CAAC,CAEJ,CAAA;AAEpB,IAAA,MAAMC,MAAM,GAAGpC,UAAC,CAACqC,gBAAgB,CAACzD,IAAI,CAAC,CAAA;IAEvC,MAAM0D,cAAc,GAAGF,MAAM,GAExBxD,IAAI,CAAC2D,MAAM,GACZ3D,IAAI,CAACyB,MAAM,CAAA;AACf,IAAA,MAAMmC,KAAK,GAAGzC,uEAA+B,CAACuC,cAAc,CAAC,CAAA;AAE7D,IAAA,IAAIG,GAAG,CAAA;AACP,IAAA,IAAIC,KAAK,CAAA;AACT,IAAA,IAAIN,MAAM,IAAIpC,UAAC,CAACC,YAAY,CAACuC,KAAK,EAAE;AAAEG,MAAAA,IAAI,EAAE,MAAA;AAAO,KAAC,CAAC,EAAE;MACrDD,KAAK,GAAGD,GAAG,GAAGD,KAAK,CAAA;AAEnB5D,MAAAA,IAAI,CAAC2D,MAAM,GAAGvC,UAAC,CAAC4C,kBAAkB,CAAC,CAAC5C,UAAC,CAAC6C,cAAc,CAAC,CAAC,CAAC,EAAEJ,GAAG,CAAC,CAAC,CAAA;KAC/D,MAAM,IAAInB,WAAW,IAAIc,MAAM,IAAIvC,wBAAwB,CAAC2C,KAAK,CAAC,EAAE;AAInEE,MAAAA,KAAK,GAAGD,GAAG,GAAG7D,IAAI,CAAC2D,MAAM,CAAA;KAC1B,MAAM,IAAI/B,KAAK,CAACO,QAAQ,CAACyB,KAAK,CAAC,EAAE;MAChCE,KAAK,GAAGD,GAAG,GAAGH,cAAc,CAAA;AAC9B,KAAC,MAAM;AAGL,MAAA,IAAI,CAACJ,MAAM,IAAIE,MAAM,EAAE;AACrBF,QAAAA,MAAM,GAAG1B,KAAK,CAACsC,gCAAgC,CAACN,KAAK,CAAC,CAAA;QACtDhC,KAAK,CAACuB,IAAI,CAAC;AAAEgB,UAAAA,EAAE,EAAE/C,UAAC,CAACgD,SAAS,CAACd,MAAM,CAAA;AAAE,SAAC,CAAC,CAAA;AACzC,OAAA;AACAO,MAAAA,GAAG,GAAGP,MAAM,CAAA;AACZQ,MAAAA,KAAK,GAAG1C,UAAC,CAACiD,oBAAoB,CAC5B,GAAG,EACHjD,UAAC,CAACgD,SAAS,CAACd,MAAM,CAAC,EAKnBI,cACF,CAAC,CAAA;AAED,MAAA,IAAIF,MAAM,EAAE;QACVxD,IAAI,CAAC2D,MAAM,GAAGE,GAAG,CAAA;AACnB,OAAC,MAAM;QACL7D,IAAI,CAACyB,MAAM,GAAGoC,GAAG,CAAA;AACnB,OAAA;AACF,KAAA;IAIA,IAAIL,MAAM,IAAIpC,UAAC,CAACG,kBAAkB,CAACqC,KAAK,CAAC,EAAE;AACzC,MAAA,IAAIlB,WAAW,IAAIzB,wBAAwB,CAAC2C,KAAK,CAAC,EAAE;QAGlD5D,IAAI,CAAC2D,MAAM,GAAGD,cAAc,CAAA;AAC9B,OAAC,MAAM;QAGL,MAAM;AAAEjC,UAAAA,MAAAA;AAAO,SAAC,GAAGmC,KAAK,CAAA;AACxB,QAAA,IAAIU,OAAqB,CAAA;AACzB,QAAA,IAAIlD,UAAC,CAACE,OAAO,CAACG,MAAM,CAAC,EAAE;AACrB6C,UAAAA,OAAO,GAAGlD,UAAC,CAACmD,cAAc,EAAE,CAAA;AAC9B,SAAC,MAAM;AACL,UAAA,MAAMC,QAAQ,GAAG5C,KAAK,CAAC6C,qBAAqB,CAAChD,MAAM,CAAC,CAAA;AACpD,UAAA,IAAI+C,QAAQ,EAAE;AACZF,YAAAA,OAAO,GAAGE,QAAQ,CAAA;AAClBZ,YAAAA,KAAK,CAACnC,MAAM,GAAGL,UAAC,CAACiD,oBAAoB,CAAC,GAAG,EAAEG,QAAQ,EAAE/C,MAAM,CAAC,CAAA;AAC9D,WAAC,MAAM;AACL6C,YAAAA,OAAO,GAAG7C,MAAM,CAAA;AAClB,WAAA;AACF,SAAA;QAEAzB,IAAI,CAAC0E,SAAS,CAACC,OAAO,CAACvD,UAAC,CAACgD,SAAS,CAACE,OAAO,CAAC,CAAC,CAAA;AAE5CtE,QAAAA,IAAI,CAAC2D,MAAM,GAAGvC,UAAC,CAACwD,gBAAgB,CAAC5E,IAAI,CAAC2D,MAAM,EAAEvC,UAAC,CAACyD,UAAU,CAAC,MAAM,CAAC,CAAC,CAAA;AACrE,OAAA;AACF,KAAA;AAEA,IAAA,MAAMC,IAAI,GAAG;AAAEhB,MAAAA,KAAK,EAAE1C,UAAC,CAACgD,SAAS,CAACN,KAAK,CAAC;AAAED,MAAAA,GAAG,EAAEzC,UAAC,CAACgD,SAAS,CAACP,GAAG,CAAA;KAAG,CAAA;AAGjEkB,IAAAA,MAAM,CAACC,cAAc,CAACF,IAAI,EAAE,KAAK,EAAE;AAAEG,MAAAA,UAAU,EAAE,KAAA;AAAM,KAAC,CAAC,CAAA;AACzD5B,IAAAA,MAAM,CAACF,IAAI,CAAC2B,IAAI,CAAC,CAAA;AACnB,GAAA;AAEA,EAAA,IAAII,MAAM,GAAGtC,eAAe,CAAC5C,IAAI,CAAA;AACjC,EAAA,IAAI8C,QAAQ,EAAEoC,MAAM,GAAGpC,QAAQ,CAACoC,MAAM,CAAC,CAAA;AAEvC,EAAA,MAAMC,gBAAgB,GAAG/D,UAAC,CAACgE,gBAAgB,CAACvC,SAAS,CAAC,CAAA;EACtD,MAAMwC,cAAc,GAAGF,gBAAgB,IAAItC,SAAS,CAACyC,KAAK,KAAK,KAAK,CAAA;EACpE,MAAMC,aAAa,GACjB,CAACJ,gBAAgB,IAAI/D,UAAC,CAACV,iBAAiB,CAACmC,SAAS,EAAE;AAAE1C,IAAAA,QAAQ,EAAE,MAAA;AAAO,GAAC,CAAC,CAAA;AAE3E,EAAA,MAAMqF,wBAAwB,GAC3BpE,UAAC,CAACqE,qBAAqB,CAAC7C,eAAe,CAAC8C,MAAM,CAAC,IAC9C,CAAC9C,eAAe,CAAC+C,kBAAkB,EAAE,IACtCvE,UAAC,CAACf,oBAAoB,CAACuC,eAAe,CAAC8C,MAAM,CAAC,IAC7C3E,IAAI,CAAC6B,eAAe,CAAC8C,MAAM,CAACpF,WAAW,CAAC,KAAKsC,eAAe,CAAC5C,IAAK,CAAA;AAGtE,EAAA,MAAM4F,GAAG,GAAGP,cAAc,GACrB1C,aAAa,GAAGH,wBAAwB,GAAGD,iBAAiB,GAC5DI,aAAa,GAAGL,oBAAoB,GAAGF,aAAc,CAAA;AAC1D,EAAA,MAAMyD,SAAS,GAAGR,cAAc,GAAG,IAAI,GAAG,IAAI,CAAA;EAE9C,MAAMvB,KAAK,GAAGT,MAAM,CACjByC,GAAG,CAACF,GAAG,CAAC,CACRG,MAAM,CAAC,CAACC,IAAI,EAAElC,KAAK,KAAK1C,UAAC,CAAC6E,iBAAiB,CAACJ,SAAS,EAAEG,IAAI,EAAElC,KAAK,CAAC,CAAC,CAAA;AAEvElB,EAAAA,eAAe,CAACI,WAAW,CACzBmC,gBAAgB,IAAKI,aAAa,IAAIC,wBAAyB,GAC3DpE,UAAC,CAAC6E,iBAAiB,CAACJ,SAAS,EAAE/B,KAAK,EAAEoB,MAAM,CAAC,GAC7C9D,UAAC,CAAC8E,qBAAqB,CAACpC,KAAK,EAAEjB,SAAS,EAAEqC,MAAM,CACtD,CAAC,CAAA;AACH,CAAA;AAEO,SAASiB,SAASA,CACvBtG,IAAqE,EACrEuG,WAAqC,EACrC;EACA,MAAM;AAAExE,IAAAA,KAAAA;AAAM,GAAC,GAAG/B,IAAI,CAAA;AAItB,EAAA,MAAMC,YAAY,GAAGC,8BAA8B,CAACF,IAAI,CAAC,CAAA;EACzD,MAAM;AAAEI,IAAAA,UAAAA;AAAW,GAAC,GAAGH,YAAY,CAAA;EAEnC,IAAIG,UAAU,CAACS,iBAAiB,CAAC;AAAEP,IAAAA,QAAQ,EAAE,QAAA;AAAS,GAAC,CAAC,EAAE;AACxDsC,IAAAA,sBAAsB,CACpB5C,IAAI,EACJuG,WAAW,EACXnG,UAAU,EACVmB,UAAC,CAACiF,cAAc,CAAC,IAAI,CACvB,CAAC,CAAA;AACH,GAAC,MAAM;AACL,IAAA,IAAIvD,QAAQ,CAAA;IACZ,IACE7C,UAAU,CAACwD,gBAAgB,CAAC;MAAEE,MAAM,EAAE7D,YAAY,CAACE,IAAAA;AAAK,KAAC,CAAC,IAG1DH,IAAI,CAACgC,0BAA0B,EAAE,EACjC;MAEAiB,QAAQ,GAAIwD,WAA+B,IAAK;AAI9C,QAAA,MAAM7E,MAAM,GAAGN,uEAA+B,CAC5CmF,WAAW,CAAC7E,MACd,CAAiB,CAAA;AACjB,QAAA,IAAI8E,OAAqB,CAAA;QACzB,IAAI,CAACH,WAAW,CAAC1D,WAAW,IAAI,CAACzB,wBAAwB,CAACQ,MAAM,CAAC,EAAE;AAIjE8E,UAAAA,OAAO,GAAG3E,KAAK,CAAC6C,qBAAqB,CAAChD,MAAM,CAAC,CAAA;AAC7C,UAAA,IAAI8E,OAAO,EAAE;AACXD,YAAAA,WAAW,CAAC7E,MAAM,GAAGL,UAAC,CAACiD,oBAAoB,CAAC,GAAG,EAAEkC,OAAO,EAAE9E,MAAM,CAAC,CAAA;AACnE,WAAA;AACF,SAAA;AACA,QAAA,OAAOL,UAAC,CAACoF,cAAc,CACrBpF,UAAC,CAACwD,gBAAgB,CAAC0B,WAAW,EAAElF,UAAC,CAACyD,UAAU,CAAC,MAAM,CAAC,CAAC,EACrD,CAACzD,UAAC,CAACgD,SAAS,CAACmC,OAAO,IAAA,IAAA,GAAPA,OAAO,GAAI9E,MAAM,CAAC,CACjC,CAAC,CAAA;OACF,CAAA;AACH,KAAA;IAEAgB,sBAAsB,CACpB5C,IAAI,EACJuG,WAAW,EACXvG,IAAI,EACJD,qBAAqB,CAACE,YAAY,CAAC,GAC/BsB,UAAC,CAACiF,cAAc,CAAC,KAAK,CAAC,GACvBzE,KAAK,CAAC6E,kBAAkB,EAAE,EAC9B3D,QACF,CAAC,CAAA;AACH,GAAA;AACF;;ACtSA,YAAe4D,yBAAO,CAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA,CAAA;EAChDH,GAAG,CAACI,aAAa,CAAA,sCAAoB,CAAC,CAAA;EAEtC,MAAM;AAAEC,IAAAA,KAAK,GAAG,KAAA;AAAM,GAAC,GAAGJ,OAAO,CAAA;AACjC,EAAA,MAAMjE,aAAa,GAAA,CAAAkE,eAAA,GAAGF,GAAG,CAACM,UAAU,CAAC,eAAe,CAAC,KAAAJ,IAAAA,GAAAA,eAAA,GAAIG,KAAK,CAAA;AAC9D,EAAA,MAAMtE,WAAW,GAAA,CAAAoE,gBAAA,GAAGH,GAAG,CAACM,UAAU,CAAC,aAAa,CAAC,KAAAH,IAAAA,GAAAA,gBAAA,GAAIE,KAAK,CAAA;EAE1D,OAAO;AACLjD,IAAAA,IAAI,EAAE,6BAA6B;AACnCmD,IAAAA,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAAClE,IAAI,CAAC,kBAAkB,CAAC;AAE1DmE,IAAAA,OAAO,EAAE;MACP,iDAAiDC,CAC/C1H,IAAqE,EACrE;QACAsG,SAAS,CAACtG,IAAI,EAAE;UAAE8C,aAAa;AAAED,UAAAA,WAAAA;AAAY,SAAC,CAAC,CAAA;AACjD,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;;;"}
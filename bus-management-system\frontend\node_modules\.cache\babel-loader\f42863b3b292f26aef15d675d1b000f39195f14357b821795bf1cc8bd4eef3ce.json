{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\components\\\\SimpleLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleLayout = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const {\n    user,\n    logout\n  } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n  const navigation = [{\n    name: 'Dashboard',\n    href: '/dashboard',\n    roles: ['admin', 'driver', 'conductor', 'student', 'parent']\n  }, {\n    name: 'Bus Management',\n    href: '/buses',\n    roles: ['admin']\n  }, {\n    name: 'Driver Management',\n    href: '/drivers',\n    roles: ['admin']\n  }, {\n    name: 'Route Management',\n    href: '/routes',\n    roles: ['admin']\n  }, {\n    name: 'Student Management',\n    href: '/students',\n    roles: ['admin', 'driver', 'conductor']\n  }, {\n    name: 'Trip Management',\n    href: '/trips',\n    roles: ['admin', 'driver', 'conductor']\n  }, {\n    name: 'Attendance',\n    href: '/attendance',\n    roles: ['admin', 'driver', 'conductor']\n  }, {\n    name: 'Fee Management',\n    href: '/fees',\n    roles: ['admin']\n  }, {\n    name: 'Maintenance',\n    href: '/maintenance',\n    roles: ['admin']\n  }, {\n    name: 'Live Tracking',\n    href: '/tracking',\n    roles: ['admin', 'driver', 'conductor', 'student', 'parent']\n  }, {\n    name: 'Reports',\n    href: '/reports',\n    roles: ['admin']\n  }];\n  const filteredNavigation = navigation.filter(item => !item.roles || item.roles.includes(user === null || user === void 0 ? void 0 : user.role));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex overflow-hidden bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${sidebarOpen ? 'block' : 'hidden'} fixed inset-0 flex z-40 md:hidden`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 right-0 -mr-12 pt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n            onClick: () => setSidebarOpen(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SidebarContent, {\n          navigation: filteredNavigation,\n          location: location\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden md:flex md:flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col w-64\",\n        children: /*#__PURE__*/_jsxDEV(SidebarContent, {\n          navigation: filteredNavigation,\n          location: location\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col w-0 flex-1 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden\",\n          onClick: () => setSidebarOpen(true),\n          children: \"\\u2630\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 px-4 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full flex md:ml-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative w-full text-gray-400 focus-within:text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"h-5 w-5\",\n                    children: \"\\uD83D\\uDD0D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 75,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent sm:text-sm\",\n                  placeholder: \"Search buses, routes, students...\",\n                  type: \"search\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4 flex items-center md:ml-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3 relative\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: [user === null || user === void 0 ? void 0 : user.first_name, \" \", user === null || user === void 0 ? void 0 : user.last_name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 capitalize\",\n                    children: user === null || user === void 0 ? void 0 : user.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/profile\",\n                  className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                  children: \"\\uD83D\\uDC64\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleLogout,\n                  className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                  children: \"\\uD83D\\uDEAA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 relative overflow-y-auto focus:outline-none\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n            children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleLayout, \"4XeGbH0uZYTp7AIKnWcw5Qm5mxM=\", false, function () {\n  return [useAuth, useLocation, useNavigate];\n});\n_c = SimpleLayout;\nconst SidebarContent = ({\n  navigation,\n  location\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center flex-shrink-0 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl\",\n          children: \"\\uD83D\\uDE8C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"ml-2 text-xl font-bold text-gray-900\",\n          children: \"BusMS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"mt-5 flex-1 px-2 space-y-1\",\n      children: navigation.map(item => {\n        const isActive = location.pathname === item.href;\n        return /*#__PURE__*/_jsxDEV(Link, {\n          to: item.href,\n          className: `${isActive ? 'bg-blue-100 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium border-l-4`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-3 text-lg\",\n            children: [item.name === 'Dashboard' && '📊', item.name === 'Bus Management' && '🚌', item.name === 'Driver Management' && '👨‍💼', item.name === 'Route Management' && '🗺️', item.name === 'Student Management' && '👨‍🎓', item.name === 'Trip Management' && '📅', item.name === 'Attendance' && '✅', item.name === 'Fee Management' && '💰', item.name === 'Maintenance' && '🔧', item.name === 'Live Tracking' && '📍', item.name === 'Reports' && '📈']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), item.name]\n        }, item.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-shrink-0 flex border-t border-gray-200 p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-xs text-gray-500\",\n      children: \"Bus Management System v1.0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 131,\n  columnNumber: 3\n}, this);\n_c2 = SidebarContent;\nexport default SimpleLayout;\nvar _c, _c2;\n$RefreshReg$(_c, \"SimpleLayout\");\n$RefreshReg$(_c2, \"SidebarContent\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "Link", "useLocation", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "SimpleLayout", "_s", "sidebarOpen", "setSidebarOpen", "user", "logout", "location", "navigate", "handleLogout", "navigation", "name", "href", "roles", "filteredNavigation", "filter", "item", "includes", "role", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON>bar<PERSON><PERSON>nt", "placeholder", "type", "first_name", "last_name", "to", "_c", "map", "isActive", "pathname", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/components/SimpleLayout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst SimpleLayout = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user, logout } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', roles: ['admin', 'driver', 'conductor', 'student', 'parent'] },\n    { name: 'Bus Management', href: '/buses', roles: ['admin'] },\n    { name: 'Driver Management', href: '/drivers', roles: ['admin'] },\n    { name: 'Route Management', href: '/routes', roles: ['admin'] },\n    { name: 'Student Management', href: '/students', roles: ['admin', 'driver', 'conductor'] },\n    { name: 'Trip Management', href: '/trips', roles: ['admin', 'driver', 'conductor'] },\n    { name: 'Attendance', href: '/attendance', roles: ['admin', 'driver', 'conductor'] },\n    { name: 'Fee Management', href: '/fees', roles: ['admin'] },\n    { name: 'Maintenance', href: '/maintenance', roles: ['admin'] },\n    { name: 'Live Tracking', href: '/tracking', roles: ['admin', 'driver', 'conductor', 'student', 'parent'] },\n    { name: 'Reports', href: '/reports', roles: ['admin'] },\n  ];\n\n  const filteredNavigation = navigation.filter(item => \n    !item.roles || item.roles.includes(user?.role)\n  );\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gray-100\">\n      {/* Mobile sidebar */}\n      <div className={`${sidebarOpen ? 'block' : 'hidden'} fixed inset-0 flex z-40 md:hidden`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)}></div>\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              ✕\n            </button>\n          </div>\n          <SidebarContent navigation={filteredNavigation} location={location} />\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:flex-shrink-0\">\n        <div className=\"flex flex-col w-64\">\n          <SidebarContent navigation={filteredNavigation} location={location} />\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Top navigation */}\n        <div className=\"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            ☰\n          </button>\n          \n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <span className=\"h-5 w-5\">🔍</span>\n                  </div>\n                  <input\n                    className=\"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent sm:text-sm\"\n                    placeholder=\"Search buses, routes, students...\"\n                    type=\"search\"\n                  />\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"ml-4 flex items-center md:ml-6\">\n              <div className=\"ml-3 relative\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-medium text-gray-700\">\n                      {user?.first_name} {user?.last_name}\n                    </div>\n                    <div className=\"text-xs text-gray-500 capitalize\">\n                      {user?.role}\n                    </div>\n                  </div>\n                  \n                  <Link\n                    to=\"/profile\"\n                    className=\"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                  >\n                    👤\n                  </Link>\n                  \n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                  >\n                    🚪\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              <Outlet />\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nconst SidebarContent = ({ navigation, location }) => (\n  <div className=\"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\">\n    <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n      <div className=\"flex items-center flex-shrink-0 px-4\">\n        <div className=\"flex items-center\">\n          <span className=\"text-2xl\">🚌</span>\n          <h1 className=\"ml-2 text-xl font-bold text-gray-900\">BusMS</h1>\n        </div>\n      </div>\n      \n      <nav className=\"mt-5 flex-1 px-2 space-y-1\">\n        {navigation.map((item) => {\n          const isActive = location.pathname === item.href;\n          \n          return (\n            <Link\n              key={item.name}\n              to={item.href}\n              className={`${\n                isActive\n                  ? 'bg-blue-100 border-blue-500 text-blue-700'\n                  : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n              } group flex items-center px-2 py-2 text-sm font-medium border-l-4`}\n            >\n              <span className=\"mr-3 text-lg\">\n                {item.name === 'Dashboard' && '📊'}\n                {item.name === 'Bus Management' && '🚌'}\n                {item.name === 'Driver Management' && '👨‍💼'}\n                {item.name === 'Route Management' && '🗺️'}\n                {item.name === 'Student Management' && '👨‍🎓'}\n                {item.name === 'Trip Management' && '📅'}\n                {item.name === 'Attendance' && '✅'}\n                {item.name === 'Fee Management' && '💰'}\n                {item.name === 'Maintenance' && '🔧'}\n                {item.name === 'Live Tracking' && '📍'}\n                {item.name === 'Reports' && '📈'}\n              </span>\n              {item.name}\n            </Link>\n          );\n        })}\n      </nav>\n    </div>\n    \n    <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n      <div className=\"text-xs text-gray-500\">\n        Bus Management System v1.0\n      </div>\n    </div>\n  </div>\n);\n\nexport default SimpleLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACzE,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAEY,IAAI;IAAEC;EAAO,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClC,MAAMS,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMH,MAAM,CAAC,CAAC;IACdE,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAME,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ;EAAE,CAAC,EACvG;IAAEF,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EAC5D;IAAEF,IAAI,EAAE,mBAAmB;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EACjE;IAAEF,IAAI,EAAE,kBAAkB;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EAC/D;IAAEF,IAAI,EAAE,oBAAoB;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW;EAAE,CAAC,EAC1F;IAAEF,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW;EAAE,CAAC,EACpF;IAAEF,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW;EAAE,CAAC,EACpF;IAAEF,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EAC3D;IAAEF,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EAC/D;IAAEF,IAAI,EAAE,eAAe;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ;EAAE,CAAC,EAC1G;IAAEF,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,CACxD;EAED,MAAMC,kBAAkB,GAAGJ,UAAU,CAACK,MAAM,CAACC,IAAI,IAC/C,CAACA,IAAI,CAACH,KAAK,IAAIG,IAAI,CAACH,KAAK,CAACI,QAAQ,CAACZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,IAAI,CAC/C,CAAC;EAED,oBACElB,OAAA;IAAKmB,SAAS,EAAC,2CAA2C;IAAAC,QAAA,gBAExDpB,OAAA;MAAKmB,SAAS,EAAE,GAAGhB,WAAW,GAAG,OAAO,GAAG,QAAQ,oCAAqC;MAAAiB,QAAA,gBACtFpB,OAAA;QAAKmB,SAAS,EAAC,yCAAyC;QAACE,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,KAAK;MAAE;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrGzB,OAAA;QAAKmB,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrEpB,OAAA;UAAKmB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDpB,OAAA;YACEmB,SAAS,EAAC,gIAAgI;YAC1IE,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,KAAK,CAAE;YAAAgB,QAAA,EACtC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNzB,OAAA,CAAC0B,cAAc;UAAChB,UAAU,EAAEI,kBAAmB;UAACP,QAAQ,EAAEA;QAAS;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKmB,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eAC9CpB,OAAA;QAAKmB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCpB,OAAA,CAAC0B,cAAc;UAAChB,UAAU,EAAEI,kBAAmB;UAACP,QAAQ,EAAEA;QAAS;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKmB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBAEvDpB,OAAA;QAAKmB,SAAS,EAAC,uDAAuD;QAAAC,QAAA,gBACpEpB,OAAA;UACEmB,SAAS,EAAC,4HAA4H;UACtIE,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,IAAI,CAAE;UAAAgB,QAAA,EACrC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzB,OAAA;UAAKmB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CpB,OAAA;YAAKmB,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BpB,OAAA;cAAKmB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAClCpB,OAAA;gBAAKmB,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,gBACvEpB,OAAA;kBAAKmB,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,eAC9EpB,OAAA;oBAAMmB,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACNzB,OAAA;kBACEmB,SAAS,EAAC,yLAAyL;kBACnMQ,WAAW,EAAC,mCAAmC;kBAC/CC,IAAI,EAAC;gBAAQ;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzB,OAAA;YAAKmB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7CpB,OAAA;cAAKmB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BpB,OAAA;gBAAKmB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CpB,OAAA;kBAAKmB,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpB,OAAA;oBAAKmB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAC/Cf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,UAAU,EAAC,GAAC,EAACxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,SAAS;kBAAA;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACNzB,OAAA;oBAAKmB,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9Cf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENzB,OAAA,CAACL,IAAI;kBACHoC,EAAE,EAAC,UAAU;kBACbZ,SAAS,EAAC,qIAAqI;kBAAAC,QAAA,EAChJ;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAEPzB,OAAA;kBACEqB,OAAO,EAAEZ,YAAa;kBACtBU,SAAS,EAAC,qIAAqI;kBAAAC,QAAA,EAChJ;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzB,OAAA;QAAMmB,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eAClEpB,OAAA;UAAKmB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBpB,OAAA;YAAKmB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,eACrDpB,OAAA,CAACN,MAAM;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CA3HID,YAAY;EAAA,QAESH,OAAO,EACfF,WAAW,EACXC,WAAW;AAAA;AAAAmC,EAAA,GAJxB/B,YAAY;AA6HlB,MAAMyB,cAAc,GAAGA,CAAC;EAAEhB,UAAU;EAAEH;AAAS,CAAC,kBAC9CP,OAAA;EAAKmB,SAAS,EAAC,4DAA4D;EAAAC,QAAA,gBACzEpB,OAAA;IAAKmB,SAAS,EAAC,gDAAgD;IAAAC,QAAA,gBAC7DpB,OAAA;MAAKmB,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnDpB,OAAA;QAAKmB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCpB,OAAA;UAAMmB,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpCzB,OAAA;UAAImB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzB,OAAA;MAAKmB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EACxCV,UAAU,CAACuB,GAAG,CAAEjB,IAAI,IAAK;QACxB,MAAMkB,QAAQ,GAAG3B,QAAQ,CAAC4B,QAAQ,KAAKnB,IAAI,CAACJ,IAAI;QAEhD,oBACEZ,OAAA,CAACL,IAAI;UAEHoC,EAAE,EAAEf,IAAI,CAACJ,IAAK;UACdO,SAAS,EAAE,GACTe,QAAQ,GACJ,2CAA2C,GAC3C,uEAAuE,mEACT;UAAAd,QAAA,gBAEpEpB,OAAA;YAAMmB,SAAS,EAAC,cAAc;YAAAC,QAAA,GAC3BJ,IAAI,CAACL,IAAI,KAAK,WAAW,IAAI,IAAI,EACjCK,IAAI,CAACL,IAAI,KAAK,gBAAgB,IAAI,IAAI,EACtCK,IAAI,CAACL,IAAI,KAAK,mBAAmB,IAAI,OAAO,EAC5CK,IAAI,CAACL,IAAI,KAAK,kBAAkB,IAAI,KAAK,EACzCK,IAAI,CAACL,IAAI,KAAK,oBAAoB,IAAI,OAAO,EAC7CK,IAAI,CAACL,IAAI,KAAK,iBAAiB,IAAI,IAAI,EACvCK,IAAI,CAACL,IAAI,KAAK,YAAY,IAAI,GAAG,EACjCK,IAAI,CAACL,IAAI,KAAK,gBAAgB,IAAI,IAAI,EACtCK,IAAI,CAACL,IAAI,KAAK,aAAa,IAAI,IAAI,EACnCK,IAAI,CAACL,IAAI,KAAK,eAAe,IAAI,IAAI,EACrCK,IAAI,CAACL,IAAI,KAAK,SAAS,IAAI,IAAI;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,EACNT,IAAI,CAACL,IAAI;QAAA,GArBLK,IAAI,CAACL,IAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBV,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,eAENzB,OAAA;IAAKmB,SAAS,EAAC,iDAAiD;IAAAC,QAAA,eAC9DpB,OAAA;MAAKmB,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAAC;IAEvC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACW,GAAA,GAlDIV,cAAc;AAoDpB,eAAezB,YAAY;AAAC,IAAA+B,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Dice2 = createLucideIcon(\"Dice2\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}], [\"path\", {\n  d: \"M15 9h.01\",\n  key: \"x1ddxp\"\n}], [\"path\", {\n  d: \"M9 15h.01\",\n  key: \"fzyn71\"\n}]]);\nexport { Dice2 as default };", "map": {"version": 3, "names": ["Dice2", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\dice-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Dice2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNMTUgOWguMDEiIC8+CiAgPHBhdGggZD0iTTkgMTVoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dice-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Dice2 = createLucideIcon('Dice2', [\n  [\n    'rect',\n    {\n      width: '18',\n      height: '18',\n      x: '3',\n      y: '3',\n      rx: '2',\n      ry: '2',\n      key: '1m3agn',\n    },\n  ],\n  ['path', { d: 'M15 9h.01', key: 'x1ddxp' }],\n  ['path', { d: 'M9 15h.01', key: 'fzyn71' }],\n]);\n\nexport default Dice2;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
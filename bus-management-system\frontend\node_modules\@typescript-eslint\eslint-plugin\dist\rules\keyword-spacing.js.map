{"version": 3, "file": "keyword-spacing.js", "sourceRoot": "", "sources": ["../../src/rules/keyword-spacing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAE3E,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,iBAAiB,CAAC,CAAC;AAKtD,mEAAmE;AACnE,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;IACpD,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;AACzB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS;AAC3B,yHAAyH;AACzH,UAAU,EACV;IACE,UAAU,EAAE;QACV,SAAS,EAAE;YACT,UAAU,EAAE;gBACV,+GAA+G;gBAC/G,IAAI,EAAE,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM;aACxD;SACF;KACF;CACF,CACF,CAAC;AAEF,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,sDAAsD;YACnE,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,YAAY;QACrB,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,CAAC,MAAM,CAAC;QAChB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IAEpB,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;QACpC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3C,uCACK,SAAS,KACZ,cAAc,CAAC,IAAI;gBACjB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAC7B,UAAU,CAAC,aAAa,CACtB,IAAI,CAAC,UAAU,EACf,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,CAC9B,EACD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CACrD,CAAC;gBACF,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;gBAClC,uEAAuE;gBACvE,mEAAmE;gBACnE,mFAAmF;gBACnF,kFAAkF;gBAClF,OAAO,CAAC,IAAI,GAAG,uBAAe,CAAC,OAAO,CAAC;gBAEvC,qFAAqF;gBACrF,SAAS,CAAC,iBAAiB,CAAC,OAAgB,CAAC,CAAC;gBAE9C,+EAA+E;gBAC/E,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC;YAC9B,CAAC;YACD,oCAAoC,CAClC,IAAgC;;gBAEhC,MAAM,EAAE,IAAI,EAAE,kBAAkB,GAAG,EAAE,EAAE,GAAG,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,EAAE,CAAC;gBAC1D,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC/D,MAAM,eAAe,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAE,CAAC;gBAC7D,IACE,CAAA,MAAA,MAAA,IAAI,CAAC,UAAU,0CAAG,CAAC,CAAC,0CAAE,IAAI,MAAK,sBAAc,CAAC,sBAAsB,EACpE;oBACA,OAAO;iBACR;gBACD,MAAM,8BAA8B,GAClC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAChD,IACE,CAAC,MAAA,kBAAkB,CAAC,KAAK,mCAAI,KAAK,CAAC,KAAK,IAAI;oBAC5C,8BAA8B,KAAK,CAAC,EACpC;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,GAAG,EAAE,SAAS,CAAC,GAAG;wBAClB,SAAS,EAAE,eAAe;wBAC1B,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;wBACvB,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;wBAC/C,CAAC;qBACF,CAAC,CAAC;iBACJ;gBACD,IACE,CAAC,MAAA,kBAAkB,CAAC,KAAK,mCAAI,KAAK,CAAC,KAAK,KAAK;oBAC7C,8BAA8B,GAAG,CAAC,EAClC;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,GAAG,EAAE,SAAS,CAAC,GAAG;wBAClB,SAAS,EAAE,iBAAiB;wBAC5B,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;wBACvB,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gCAClB,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,8BAA8B;6BACpD,CAAC,CAAC;wBACL,CAAC;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}
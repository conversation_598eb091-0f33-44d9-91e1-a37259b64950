from datetime import datetime
from app import db, ma

class Enrollment(db.Model):
    """Student course enrollment model."""
    __tablename__ = 'enrollments'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('students.id'), nullable=False)
    course_id = db.Column(db.Integer, db.<PERSON>ey('courses.id'), nullable=False)
    enrollment_date = db.Column(db.Date, default=datetime.utcnow().date)
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Composite unique constraint
    __table_args__ = (db.UniqueConstraint('student_id', 'course_id', name='unique_student_course'),)
    
    def to_dict(self):
        """Convert enrollment to dictionary."""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'student_name': self.student.full_name if self.student else None,
            'student_roll': self.student.roll_number if self.student else None,
            'course_id': self.course_id,
            'course_name': self.course.name if self.course else None,
            'course_code': self.course.code if self.course else None,
            'enrollment_date': self.enrollment_date.isoformat() if self.enrollment_date else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Enrollment {self.student.roll_number} - {self.course.code}>'

class EnrollmentSchema(ma.SQLAlchemyAutoSchema):
    """Enrollment serialization schema."""
    class Meta:
        model = Enrollment
        load_instance = True
        include_fk = True

enrollment_schema = EnrollmentSchema()
enrollments_schema = EnrollmentSchema(many=True)

{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Film = createLucideIcon(\"Film\", [[\"rect\", {\n  width: \"20\",\n  height: \"20\",\n  x: \"2\",\n  y: \"2\",\n  rx: \"2.18\",\n  ry: \"2.18\",\n  key: \"vury4c\"\n}], [\"line\", {\n  x1: \"7\",\n  x2: \"7\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"t1e4qh\"\n}], [\"line\", {\n  x1: \"17\",\n  x2: \"17\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"1tliql\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1dnqot\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"7\",\n  y1: \"7\",\n  y2: \"7\",\n  key: \"1wdzzh\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"7\",\n  y1: \"17\",\n  y2: \"17\",\n  key: \"2fufxq\"\n}], [\"line\", {\n  x1: \"17\",\n  x2: \"22\",\n  y1: \"17\",\n  y2: \"17\",\n  key: \"1xg577\"\n}], [\"line\", {\n  x1: \"17\",\n  x2: \"22\",\n  y1: \"7\",\n  y2: \"7\",\n  key: \"acrv2l\"\n}]]);\nexport { Film as default };", "map": {"version": 3, "names": ["Film", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\film.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Film\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHg9IjIiIHk9IjIiIHJ4PSIyLjE4IiByeT0iMi4xOCIgLz4KICA8bGluZSB4MT0iNyIgeDI9IjciIHkxPSIyIiB5Mj0iMjIiIC8+CiAgPGxpbmUgeDE9IjE3IiB4Mj0iMTciIHkxPSIyIiB5Mj0iMjIiIC8+CiAgPGxpbmUgeDE9IjIiIHgyPSIyMiIgeTE9IjEyIiB5Mj0iMTIiIC8+CiAgPGxpbmUgeDE9IjIiIHgyPSI3IiB5MT0iNyIgeTI9IjciIC8+CiAgPGxpbmUgeDE9IjIiIHgyPSI3IiB5MT0iMTciIHkyPSIxNyIgLz4KICA8bGluZSB4MT0iMTciIHgyPSIyMiIgeTE9IjE3IiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE3IiB4Mj0iMjIiIHkxPSI3IiB5Mj0iNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/film\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Film = createLucideIcon('Film', [\n  [\n    'rect',\n    {\n      width: '20',\n      height: '20',\n      x: '2',\n      y: '2',\n      rx: '2.18',\n      ry: '2.18',\n      key: 'vury4c',\n    },\n  ],\n  ['line', { x1: '7', x2: '7', y1: '2', y2: '22', key: 't1e4qh' }],\n  ['line', { x1: '17', x2: '17', y1: '2', y2: '22', key: '1tliql' }],\n  ['line', { x1: '2', x2: '22', y1: '12', y2: '12', key: '1dnqot' }],\n  ['line', { x1: '2', x2: '7', y1: '7', y2: '7', key: '1wdzzh' }],\n  ['line', { x1: '2', x2: '7', y1: '17', y2: '17', key: '2fufxq' }],\n  ['line', { x1: '17', x2: '22', y1: '17', y2: '17', key: '1xg577' }],\n  ['line', { x1: '17', x2: '22', y1: '7', y2: '7', key: 'acrv2l' }],\n]);\n\nexport default Film;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": 3, "file": "getTypeName.js", "sourceRoot": "", "sources": ["../src/getTypeName.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC;;;;GAIG;AACH,SAAgB,WAAW,CACzB,WAA2B,EAC3B,IAAa;IAEb,0DAA0D;IAC1D,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;QAChD,OAAO,QAAQ,CAAC;KACjB;IAED,wEAAwE;IACxE,sEAAsE;IACtE,8BAA8B;IAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;QACnD,sEAAsE;QACtE,uEAAuE;QACvE,WAAW;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,eAAe,EAAE,CAAC;QACxC,MAAM,aAAa,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAG,CAAC,CAAgC,CAAC;QAChE,IACE,EAAE,CAAC,0BAA0B,CAAC,aAAa,CAAC;YAC5C,aAAa,CAAC,UAAU,IAAI,IAAI,EAChC;YACA,OAAO,WAAW,CAChB,WAAW,EACX,WAAW,CAAC,mBAAmB,CAAC,aAAa,CAAC,UAAU,CAAC,CAC1D,CAAC;SACH;KACF;IAED,qEAAqE;IACrE,gCAAgC;IAChC,2BAA2B;IAC3B,uCAAuC;IACvC,IACE,IAAI,CAAC,OAAO,EAAE;QACd,IAAI,CAAC,KAAK;aACP,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;aAC7C,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,EAC7B;QACA,OAAO,QAAQ,CAAC;KACjB;IAED,0EAA0E;IAC1E,uEAAuE;IACvE,IACE,IAAI,CAAC,cAAc,EAAE;QACrB,IAAI,CAAC,KAAK;aACP,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;aAC7C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,EAC5B;QACA,OAAO,QAAQ,CAAC;KACjB;IAED,OAAO,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC;AAvDD,kCAuDC"}
{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Feather = createLucideIcon(\"Feather\", [[\"path\", {\n  d: \"M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z\",\n  key: \"u4sw5n\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"2\",\n  y1: \"8\",\n  y2: \"22\",\n  key: \"1c47m2\"\n}], [\"line\", {\n  x1: \"17.5\",\n  x2: \"9\",\n  y1: \"15\",\n  y2: \"15\",\n  key: \"2fj3pr\"\n}]]);\nexport { Feather as default };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\feather.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Feather\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAuMjQgMTIuMjRhNiA2IDAgMCAwLTguNDktOC40OUw1IDEwLjVWMTloOC41eiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIyIiB5MT0iOCIgeTI9IjIyIiAvPgogIDxsaW5lIHgxPSIxNy41IiB4Mj0iOSIgeTE9IjE1IiB5Mj0iMTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/feather\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Feather = createLucideIcon('Feather', [\n  [\n    'path',\n    { d: 'M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z', key: 'u4sw5n' },\n  ],\n  ['line', { x1: '16', x2: '2', y1: '8', y2: '22', key: '1c47m2' }],\n  ['line', { x1: '17.5', x2: '9', y1: '15', y2: '15', key: '2fj3pr' }],\n]);\n\nexport default Feather;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EAAEC,CAAA,EAAG,iDAAmD;EAAAC,GAAA,EAAK;AAAS,EACxE,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
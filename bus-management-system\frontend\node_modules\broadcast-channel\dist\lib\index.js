"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "BroadcastChannel", {
  enumerable: true,
  get: function get() {
    return _broadcastChannel.BroadcastChannel;
  }
});
Object.defineProperty(exports, "clearNodeFolder", {
  enumerable: true,
  get: function get() {
    return _broadcastChannel.clearNodeFolder;
  }
});
Object.defineProperty(exports, "enforceOptions", {
  enumerable: true,
  get: function get() {
    return _broadcastChannel.enforceOptions;
  }
});
Object.defineProperty(exports, "createLeaderElection", {
  enumerable: true,
  get: function get() {
    return _leaderElection.createLeaderElection;
  }
});
Object.defineProperty(exports, "beLeader", {
  enumerable: true,
  get: function get() {
    return _leaderElection.beLeader;
  }
});

var _broadcastChannel = require("./broadcast-channel");

var _leaderElection = require("./leader-election");
{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Flower2 = createLucideIcon(\"Flower2\", [[\"path\", {\n  d: \"M12 5a3 3 0 1 1 3 3m-3-3a3 3 0 1 0-3 3m3-3v1M9 8a3 3 0 1 0 3 3M9 8h1m5 0a3 3 0 1 1-3 3m3-3h-1m-2 3v-1\",\n  key: \"3pnvol\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"1822b1\"\n}], [\"path\", {\n  d: \"M12 10v12\",\n  key: \"6ubwww\"\n}], [\"path\", {\n  d: \"M12 22c4.2 0 7-1.667 7-5-4.2 0-7 1.667-7 5Z\",\n  key: \"9hd38g\"\n}], [\"path\", {\n  d: \"M12 22c-4.2 0-7-1.667-7-5 4.2 0 7 1.667 7 5Z\",\n  key: \"ufn41s\"\n}]]);\nexport { Flower2 as default };", "map": {"version": 3, "names": ["Flower2", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\flower-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Flower2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNWEzIDMgMCAxIDEgMyAzbS0zLTNhMyAzIDAgMSAwLTMgM20zLTN2MU05IDhhMyAzIDAgMSAwIDMgM005IDhoMW01IDBhMyAzIDAgMSAxLTMgM20zLTNoLTFtLTIgM3YtMSIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjIiIC8+CiAgPHBhdGggZD0iTTEyIDEwdjEyIiAvPgogIDxwYXRoIGQ9Ik0xMiAyMmM0LjIgMCA3LTEuNjY3IDctNS00LjIgMC03IDEuNjY3LTcgNVoiIC8+CiAgPHBhdGggZD0iTTEyIDIyYy00LjIgMC03LTEuNjY3LTctNSA0LjIgMCA3IDEuNjY3IDcgNVoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/flower-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Flower2 = createLucideIcon('Flower2', [\n  [\n    'path',\n    {\n      d: 'M12 5a3 3 0 1 1 3 3m-3-3a3 3 0 1 0-3 3m3-3v1M9 8a3 3 0 1 0 3 3M9 8h1m5 0a3 3 0 1 1-3 3m3-3h-1m-2 3v-1',\n      key: '3pnvol',\n    },\n  ],\n  ['circle', { cx: '12', cy: '8', r: '2', key: '1822b1' }],\n  ['path', { d: 'M12 10v12', key: '6ubwww' }],\n  ['path', { d: 'M12 22c4.2 0 7-1.667 7-5-4.2 0-7 1.667-7 5Z', key: '9hd38g' }],\n  [\n    'path',\n    { d: 'M12 22c-4.2 0-7-1.667-7-5 4.2 0 7 1.667 7 5Z', key: 'ufn41s' },\n  ],\n]);\n\nexport default Flower2;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CACE,QACA;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAS,EACrE,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
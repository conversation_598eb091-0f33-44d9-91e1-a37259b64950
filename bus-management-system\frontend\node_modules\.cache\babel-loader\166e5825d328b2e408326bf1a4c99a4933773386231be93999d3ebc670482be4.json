{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Podcast = createLucideIcon(\"Podcast\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"11\",\n  r: \"1\",\n  key: \"1gvufo\"\n}], [\"path\", {\n  d: \"M11 17a1 1 0 0 1 2 0c0 .5-.34 3-.5 4.5a.5.5 0 0 1-1 0c-.16-1.5-.5-4-.5-4.5Z\",\n  key: \"1n5fvv\"\n}], [\"path\", {\n  d: \"M8 14a5 5 0 1 1 8 0\",\n  key: \"fc81rn\"\n}], [\"path\", {\n  d: \"M17 18.5a9 9 0 1 0-10 0\",\n  key: \"jqtxkf\"\n}]]);\nexport { Podcast as default };", "map": {"version": 3, "names": ["Podcast", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\podcast.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Podcast\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjExIiByPSIxIiAvPgogIDxwYXRoIGQ9Ik0xMSAxN2ExIDEgMCAwIDEgMiAwYzAgLjUtLjM0IDMtLjUgNC41YS41LjUgMCAwIDEtMSAwYy0uMTYtMS41LS41LTQtLjUtNC41WiIgLz4KICA8cGF0aCBkPSJNOCAxNGE1IDUgMCAxIDEgOCAwIiAvPgogIDxwYXRoIGQ9Ik0xNyAxOC41YTkgOSAwIDEgMC0xMCAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/podcast\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Podcast = createLucideIcon('Podcast', [\n  ['circle', { cx: '12', cy: '11', r: '1', key: '1gvufo' }],\n  [\n    'path',\n    {\n      d: 'M11 17a1 1 0 0 1 2 0c0 .5-.34 3-.5 4.5a.5.5 0 0 1-1 0c-.16-1.5-.5-4-.5-4.5Z',\n      key: '1n5fvv',\n    },\n  ],\n  ['path', { d: 'M8 14a5 5 0 1 1 8 0', key: 'fc81rn' }],\n  ['path', { d: 'M17 18.5a9 9 0 1 0-10 0', key: 'jqtxkf' }],\n]);\n\nexport default Podcast;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,qBAAuB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
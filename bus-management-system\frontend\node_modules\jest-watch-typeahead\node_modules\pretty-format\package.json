{"name": "pretty-format", "version": "28.1.3", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/pretty-format"}, "license": "MIT", "description": "Stringify any JavaScript value.", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json", "./ConvertAnsi": "./build/plugins/ConvertAnsi.js"}, "author": "<PERSON> <<EMAIL>>", "dependencies": {"@jest/schemas": "^28.1.3", "ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "devDependencies": {"@types/react": "^17.0.3", "@types/react-is": "^17.0.0", "@types/react-test-renderer": "17.0.2", "expect": "^28.1.3", "immutable": "^4.0.0", "jest-util": "^28.1.3", "react": "17.0.2", "react-dom": "^17.0.1", "react-test-renderer": "17.0.2"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1"}
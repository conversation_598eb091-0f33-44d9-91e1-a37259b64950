import React from 'react';
import { UserCheck, Plus, QrCode } from 'lucide-react';

const StudentManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Student Management</h1>
          <p className="text-gray-600">Manage student enrollment and route assignments</p>
        </div>
        <button className="btn btn-primary flex items-center space-x-2">
          <Plus className="h-5 w-5" />
          <span>Add Student</span>
        </button>
      </div>

      <div className="card text-center py-12">
        <UserCheck className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Student Management</h3>
        <p className="text-gray-600 mb-6">
          Complete student lifecycle management with auto-assignment features.
        </p>
        <div className="space-y-2 text-sm text-gray-500">
          <p>✓ Student registration and profile management</p>
          <p>✓ AI-powered nearest route assignment</p>
          <p>✓ QR code generation for attendance</p>
          <p>✓ Parent contact management</p>
          <p>✓ Fee calculation with scholarship support</p>
        </div>
      </div>
    </div>
  );
};

export default StudentManagement;

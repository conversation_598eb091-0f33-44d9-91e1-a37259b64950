{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ReactQueryDevtools } from 'react-query/devtools';\nimport { Toaster } from 'react-hot-toast';\nimport './index.css';\nimport App from './App';\n\n// Create a client\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5 minutes\n    }\n  }\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: [/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\",\n      toastOptions: {\n        duration: 4000,\n        style: {\n          background: '#fff',\n          color: '#363636',\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n          border: '1px solid #e5e7eb',\n          borderRadius: '0.5rem'\n        },\n        success: {\n          iconTheme: {\n            primary: '#10b981',\n            secondary: '#fff'\n          }\n        },\n        error: {\n          iconTheme: {\n            primary: '#ef4444',\n            secondary: '#fff'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ReactQueryDevtools, {\n      initialIsOpen: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 22,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "QueryClient", "QueryClientProvider", "ReactQueryDevtools", "Toaster", "App", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "client", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "toastOptions", "duration", "style", "background", "color", "boxShadow", "border", "borderRadius", "success", "iconTheme", "primary", "secondary", "error", "initialIsOpen"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ReactQueryDevtools } from 'react-query/devtools';\nimport { Toaster } from 'react-hot-toast';\nimport './index.css';\nimport App from './App';\n\n// Create a client\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    },\n  },\n});\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <QueryClientProvider client={queryClient}>\n      <App />\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: '#fff',\n            color: '#363636',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n            border: '1px solid #e5e7eb',\n            borderRadius: '0.5rem',\n          },\n          success: {\n            iconTheme: {\n              primary: '#10b981',\n              secondary: '#fff',\n            },\n          },\n          error: {\n            iconTheme: {\n              primary: '#ef4444',\n              secondary: '#fff',\n            },\n          },\n        }}\n      />\n      <ReactQueryDevtools initialIsOpen={false} />\n    </QueryClientProvider>\n  </React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIP,WAAW,CAAC;EAClCQ,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC5B;EACF;AACF,CAAC,CAAC;AAEF,MAAMC,IAAI,GAAGd,QAAQ,CAACe,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTX,OAAA,CAACR,KAAK,CAACoB,UAAU;EAAAC,QAAA,eACfb,OAAA,CAACL,mBAAmB;IAACmB,MAAM,EAAEb,WAAY;IAAAY,QAAA,gBACvCb,OAAA,CAACF,GAAG;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACPlB,OAAA,CAACH,OAAO;MACNsB,QAAQ,EAAC,WAAW;MACpBC,YAAY,EAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;UACLC,UAAU,EAAE,MAAM;UAClBC,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE,uEAAuE;UAClFC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE;QAChB,CAAC;QACDC,OAAO,EAAE;UACPC,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF,CAAC;QACDC,KAAK,EAAE;UACLH,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF;MACF;IAAE;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFlB,OAAA,CAACJ,kBAAkB;MAACqC,aAAa,EAAE;IAAM;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzB;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACN,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
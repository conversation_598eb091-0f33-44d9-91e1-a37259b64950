{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst TouchpadOff = createLucideIcon(\"TouchpadOff\", [[\"path\", {\n  d: \"M4 4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16\",\n  key: \"lnt0bk\"\n}], [\"path\", {\n  d: \"M2 14h12\",\n  key: \"d8icqz\"\n}], [\"path\", {\n  d: \"M22 14h-2\",\n  key: \"jrx26d\"\n}], [\"path\", {\n  d: \"M12 20v-6\",\n  key: \"1rm09r\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M22 16V6a2 2 0 0 0-2-2H10\",\n  key: \"11y8e4\"\n}]]);\nexport { TouchpadOff as default };", "map": {"version": 3, "names": ["TouchpadOff", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\touchpad-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name TouchpadOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCA0YTIgMiAwIDAgMC0yIDJ2MTJhMiAyIDAgMCAwIDIgMmgxNiIgLz4KICA8cGF0aCBkPSJNMiAxNGgxMiIgLz4KICA8cGF0aCBkPSJNMjIgMTRoLTIiIC8+CiAgPHBhdGggZD0iTTEyIDIwdi02IiAvPgogIDxwYXRoIGQ9Im0yIDIgMjAgMjAiIC8+CiAgPHBhdGggZD0iTTIyIDE2VjZhMiAyIDAgMCAwLTItMkgxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/touchpad-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TouchpadOff = createLucideIcon('TouchpadOff', [\n  ['path', { d: 'M4 4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16', key: 'lnt0bk' }],\n  ['path', { d: 'M2 14h12', key: 'd8icqz' }],\n  ['path', { d: 'M22 14h-2', key: 'jrx26d' }],\n  ['path', { d: 'M12 20v-6', key: '1rm09r' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'M22 16V6a2 2 0 0 0-2-2H10', key: '11y8e4' }],\n]);\n\nexport default TouchpadOff;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
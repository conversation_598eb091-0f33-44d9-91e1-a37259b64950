{"version": 3, "file": "workbox-precaching.dev.js", "sources": ["../_version.js", "../utils/createCacheKey.js", "../utils/PrecacheInstallReportPlugin.js", "../utils/PrecacheCacheKeyPlugin.js", "../utils/printCleanupDetails.js", "../utils/printInstallDetails.js", "../PrecacheStrategy.js", "../PrecacheController.js", "../utils/getOrCreatePrecacheController.js", "../addPlugins.js", "../utils/removeIgnoredSearchParams.js", "../utils/generateURLVariations.js", "../PrecacheRoute.js", "../addRoute.js", "../utils/deleteOutdatedCaches.js", "../cleanupOutdatedCaches.js", "../createHandlerBoundToURL.js", "../getCacheKeyForURL.js", "../matchPrecache.js", "../precache.js", "../precacheAndRoute.js", "../PrecacheFallbackPlugin.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:precaching:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport '../_version.js';\n// Name of the search parameter used to store revision info.\nconst REVISION_SEARCH_PARAM = '__WB_REVISION__';\n/**\n * Converts a manifest entry into a versioned URL suitable for precaching.\n *\n * @param {Object|string} entry\n * @return {string} A URL with versioning info.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function createCacheKey(entry) {\n    if (!entry) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If a precache manifest entry is a string, it's assumed to be a versioned\n    // URL, like '/app.abcd1234.js'. Return as-is.\n    if (typeof entry === 'string') {\n        const urlObject = new URL(entry, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    const { revision, url } = entry;\n    if (!url) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If there's just a URL and no revision, then it's also assumed to be a\n    // versioned URL.\n    if (!revision) {\n        const urlObject = new URL(url, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    // Otherwise, construct a properly versioned URL using the custom Workbox\n    // search parameter along with the revision info.\n    const cacheKeyURL = new URL(url, location.href);\n    const originalURL = new URL(url, location.href);\n    cacheKeyURL.searchParams.set(REVISION_SEARCH_PARAM, revision);\n    return {\n        cacheKey: cacheKeyURL.href,\n        url: originalURL.href,\n    };\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to determine the\n * of assets that were updated (or not updated) during the install event.\n *\n * @private\n */\nclass PrecacheInstallReportPlugin {\n    constructor() {\n        this.updatedURLs = [];\n        this.notUpdatedURLs = [];\n        this.handlerWillStart = async ({ request, state, }) => {\n            // TODO: `state` should never be undefined...\n            if (state) {\n                state.originalRequest = request;\n            }\n        };\n        this.cachedResponseWillBeUsed = async ({ event, state, cachedResponse, }) => {\n            if (event.type === 'install') {\n                if (state &&\n                    state.originalRequest &&\n                    state.originalRequest instanceof Request) {\n                    // TODO: `state` should never be undefined...\n                    const url = state.originalRequest.url;\n                    if (cachedResponse) {\n                        this.notUpdatedURLs.push(url);\n                    }\n                    else {\n                        this.updatedURLs.push(url);\n                    }\n                }\n            }\n            return cachedResponse;\n        };\n    }\n}\nexport { PrecacheInstallReportPlugin };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to translate URLs into\n * the corresponding cache key, based on the current revision info.\n *\n * @private\n */\nclass PrecacheCacheKeyPlugin {\n    constructor({ precacheController }) {\n        this.cacheKeyWillBeUsed = async ({ request, params, }) => {\n            // Params is type any, can't change right now.\n            /* eslint-disable */\n            const cacheKey = (params === null || params === void 0 ? void 0 : params.cacheKey) ||\n                this._precacheController.getCacheKeyForURL(request.url);\n            /* eslint-enable */\n            return cacheKey\n                ? new Request(cacheKey, { headers: request.headers })\n                : request;\n        };\n        this._precacheController = precacheController;\n    }\n}\nexport { PrecacheCacheKeyPlugin };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} deletedURLs\n *\n * @private\n */\nconst logGroup = (groupTitle, deletedURLs) => {\n    logger.groupCollapsed(groupTitle);\n    for (const url of deletedURLs) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n};\n/**\n * @param {Array<string>} deletedURLs\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printCleanupDetails(deletedURLs) {\n    const deletionCount = deletedURLs.length;\n    if (deletionCount > 0) {\n        logger.groupCollapsed(`During precaching cleanup, ` +\n            `${deletionCount} cached ` +\n            `request${deletionCount === 1 ? ' was' : 's were'} deleted.`);\n        logGroup('Deleted Cache Requests', deletedURLs);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} urls\n *\n * @private\n */\nfunction _nestedGroup(groupTitle, urls) {\n    if (urls.length === 0) {\n        return;\n    }\n    logger.groupCollapsed(groupTitle);\n    for (const url of urls) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n}\n/**\n * @param {Array<string>} urlsToPrecache\n * @param {Array<string>} urlsAlreadyPrecached\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printInstallDetails(urlsToPrecache, urlsAlreadyPrecached) {\n    const precachedCount = urlsToPrecache.length;\n    const alreadyPrecachedCount = urlsAlreadyPrecached.length;\n    if (precachedCount || alreadyPrecachedCount) {\n        let message = `Precaching ${precachedCount} file${precachedCount === 1 ? '' : 's'}.`;\n        if (alreadyPrecachedCount > 0) {\n            message +=\n                ` ${alreadyPrecachedCount} ` +\n                    `file${alreadyPrecachedCount === 1 ? ' is' : 's are'} already cached.`;\n        }\n        logger.groupCollapsed(message);\n        _nestedGroup(`View newly precached URLs.`, urlsToPrecache);\n        _nestedGroup(`View previously precached URLs.`, urlsAlreadyPrecached);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { copyResponse } from 'workbox-core/copyResponse.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from 'workbox-strategies/Strategy.js';\nimport './_version.js';\n/**\n * A {@link workbox-strategies.Strategy} implementation\n * specifically designed to work with\n * {@link workbox-precaching.PrecacheController}\n * to both cache and fetch precached assets.\n *\n * Note: an instance of this class is created automatically when creating a\n * `PrecacheController`; it's generally not necessary to create this yourself.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-precaching\n */\nclass PrecacheStrategy extends Strategy {\n    /**\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] {@link https://developers.google.com/web/tools/workbox/guides/using-plugins|Plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters|init}\n     * of all fetch() requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * {@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions|CacheQueryOptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor(options = {}) {\n        options.cacheName = cacheNames.getPrecacheName(options.cacheName);\n        super(options);\n        this._fallbackToNetwork =\n            options.fallbackToNetwork === false ? false : true;\n        // Redirected responses cannot be used to satisfy a navigation request, so\n        // any redirected response must be \"copied\" rather than cloned, so the new\n        // response doesn't contain the `redirected` flag. See:\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=669363&desc=2#c1\n        this.plugins.push(PrecacheStrategy.copyRedirectedCacheableResponsesPlugin);\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const response = await handler.cacheMatch(request);\n        if (response) {\n            return response;\n        }\n        // If this is an `install` event for an entry that isn't already cached,\n        // then populate the cache.\n        if (handler.event && handler.event.type === 'install') {\n            return await this._handleInstall(request, handler);\n        }\n        // Getting here means something went wrong. An entry that should have been\n        // precached wasn't found in the cache.\n        return await this._handleFetch(request, handler);\n    }\n    async _handleFetch(request, handler) {\n        let response;\n        const params = (handler.params || {});\n        // Fall back to the network if we're configured to do so.\n        if (this._fallbackToNetwork) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn(`The precached response for ` +\n                    `${getFriendlyURL(request.url)} in ${this.cacheName} was not ` +\n                    `found. Falling back to the network.`);\n            }\n            const integrityInManifest = params.integrity;\n            const integrityInRequest = request.integrity;\n            const noIntegrityConflict = !integrityInRequest || integrityInRequest === integrityInManifest;\n            // Do not add integrity if the original request is no-cors\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            response = await handler.fetch(new Request(request, {\n                integrity: request.mode !== 'no-cors'\n                    ? integrityInRequest || integrityInManifest\n                    : undefined,\n            }));\n            // It's only \"safe\" to repair the cache if we're using SRI to guarantee\n            // that the response matches the precache manifest's expectations,\n            // and there's either a) no integrity property in the incoming request\n            // or b) there is an integrity, and it matches the precache manifest.\n            // See https://github.com/GoogleChrome/workbox/issues/2858\n            // Also if the original request users no-cors we don't use integrity.\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            if (integrityInManifest &&\n                noIntegrityConflict &&\n                request.mode !== 'no-cors') {\n                this._useDefaultCacheabilityPluginIfNeeded();\n                const wasCached = await handler.cachePut(request, response.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    if (wasCached) {\n                        logger.log(`A response for ${getFriendlyURL(request.url)} ` +\n                            `was used to \"repair\" the precache.`);\n                    }\n                }\n            }\n        }\n        else {\n            // This shouldn't normally happen, but there are edge cases:\n            // https://github.com/GoogleChrome/workbox/issues/1441\n            throw new WorkboxError('missing-precache-entry', {\n                cacheName: this.cacheName,\n                url: request.url,\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            const cacheKey = params.cacheKey || (await handler.getCacheKey(request, 'read'));\n            // Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Precaching is responding to: ` + getFriendlyURL(request.url));\n            logger.log(`Serving the precached url: ${getFriendlyURL(cacheKey instanceof Request ? cacheKey.url : cacheKey)}`);\n            logger.groupCollapsed(`View request details here.`);\n            logger.log(request);\n            logger.groupEnd();\n            logger.groupCollapsed(`View response details here.`);\n            logger.log(response);\n            logger.groupEnd();\n            logger.groupEnd();\n        }\n        return response;\n    }\n    async _handleInstall(request, handler) {\n        this._useDefaultCacheabilityPluginIfNeeded();\n        const response = await handler.fetch(request);\n        // Make sure we defer cachePut() until after we know the response\n        // should be cached; see https://github.com/GoogleChrome/workbox/issues/2737\n        const wasCached = await handler.cachePut(request, response.clone());\n        if (!wasCached) {\n            // Throwing here will lead to the `install` handler failing, which\n            // we want to do if *any* of the responses aren't safe to cache.\n            throw new WorkboxError('bad-precaching-response', {\n                url: request.url,\n                status: response.status,\n            });\n        }\n        return response;\n    }\n    /**\n     * This method is complex, as there a number of things to account for:\n     *\n     * The `plugins` array can be set at construction, and/or it might be added to\n     * to at any time before the strategy is used.\n     *\n     * At the time the strategy is used (i.e. during an `install` event), there\n     * needs to be at least one plugin that implements `cacheWillUpdate` in the\n     * array, other than `copyRedirectedCacheableResponsesPlugin`.\n     *\n     * - If this method is called and there are no suitable `cacheWillUpdate`\n     * plugins, we need to add `defaultPrecacheCacheabilityPlugin`.\n     *\n     * - If this method is called and there is exactly one `cacheWillUpdate`, then\n     * we don't have to do anything (this might be a previously added\n     * `defaultPrecacheCacheabilityPlugin`, or it might be a custom plugin).\n     *\n     * - If this method is called and there is more than one `cacheWillUpdate`,\n     * then we need to check if one is `defaultPrecacheCacheabilityPlugin`. If so,\n     * we need to remove it. (This situation is unlikely, but it could happen if\n     * the strategy is used multiple times, the first without a `cacheWillUpdate`,\n     * and then later on after manually adding a custom `cacheWillUpdate`.)\n     *\n     * See https://github.com/GoogleChrome/workbox/issues/2737 for more context.\n     *\n     * @private\n     */\n    _useDefaultCacheabilityPluginIfNeeded() {\n        let defaultPluginIndex = null;\n        let cacheWillUpdatePluginCount = 0;\n        for (const [index, plugin] of this.plugins.entries()) {\n            // Ignore the copy redirected plugin when determining what to do.\n            if (plugin === PrecacheStrategy.copyRedirectedCacheableResponsesPlugin) {\n                continue;\n            }\n            // Save the default plugin's index, in case it needs to be removed.\n            if (plugin === PrecacheStrategy.defaultPrecacheCacheabilityPlugin) {\n                defaultPluginIndex = index;\n            }\n            if (plugin.cacheWillUpdate) {\n                cacheWillUpdatePluginCount++;\n            }\n        }\n        if (cacheWillUpdatePluginCount === 0) {\n            this.plugins.push(PrecacheStrategy.defaultPrecacheCacheabilityPlugin);\n        }\n        else if (cacheWillUpdatePluginCount > 1 && defaultPluginIndex !== null) {\n            // Only remove the default plugin; multiple custom plugins are allowed.\n            this.plugins.splice(defaultPluginIndex, 1);\n        }\n        // Nothing needs to be done if cacheWillUpdatePluginCount is 1\n    }\n}\nPrecacheStrategy.defaultPrecacheCacheabilityPlugin = {\n    async cacheWillUpdate({ response }) {\n        if (!response || response.status >= 400) {\n            return null;\n        }\n        return response;\n    },\n};\nPrecacheStrategy.copyRedirectedCacheableResponsesPlugin = {\n    async cacheWillUpdate({ response }) {\n        return response.redirected ? await copyResponse(response) : response;\n    },\n};\nexport { PrecacheStrategy };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { waitUntil } from 'workbox-core/_private/waitUntil.js';\nimport { createCacheKey } from './utils/createCacheKey.js';\nimport { PrecacheInstallReportPlugin } from './utils/PrecacheInstallReportPlugin.js';\nimport { PrecacheCacheKeyPlugin } from './utils/PrecacheCacheKeyPlugin.js';\nimport { printCleanupDetails } from './utils/printCleanupDetails.js';\nimport { printInstallDetails } from './utils/printInstallDetails.js';\nimport { PrecacheStrategy } from './PrecacheStrategy.js';\nimport './_version.js';\n/**\n * Performs efficient precaching of assets.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheController {\n    /**\n     * Create a new PrecacheController.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] The cache to use for precaching.\n     * @param {string} [options.plugins] Plugins to use when precaching as well\n     * as responding to fetch events for precached assets.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor({ cacheName, plugins = [], fallbackToNetwork = true, } = {}) {\n        this._urlsToCacheKeys = new Map();\n        this._urlsToCacheModes = new Map();\n        this._cacheKeysToIntegrities = new Map();\n        this._strategy = new PrecacheStrategy({\n            cacheName: cacheNames.getPrecacheName(cacheName),\n            plugins: [\n                ...plugins,\n                new PrecacheCacheKeyPlugin({ precacheController: this }),\n            ],\n            fallbackToNetwork,\n        });\n        // Bind the install and activate methods to the instance.\n        this.install = this.install.bind(this);\n        this.activate = this.activate.bind(this);\n    }\n    /**\n     * @type {workbox-precaching.PrecacheStrategy} The strategy created by this controller and\n     * used to cache assets and respond to fetch events.\n     */\n    get strategy() {\n        return this._strategy;\n    }\n    /**\n     * Adds items to the precache list, removing any duplicates and\n     * stores the files in the\n     * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n     * worker installs.\n     *\n     * This method can be called multiple times.\n     *\n     * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n     */\n    precache(entries) {\n        this.addToCacheList(entries);\n        if (!this._installAndActiveListenersAdded) {\n            self.addEventListener('install', this.install);\n            self.addEventListener('activate', this.activate);\n            this._installAndActiveListenersAdded = true;\n        }\n    }\n    /**\n     * This method will add items to the precache list, removing duplicates\n     * and ensuring the information is valid.\n     *\n     * @param {Array<workbox-precaching.PrecacheController.PrecacheEntry|string>} entries\n     *     Array of entries to precache.\n     */\n    addToCacheList(entries) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArray(entries, {\n                moduleName: 'workbox-precaching',\n                className: 'PrecacheController',\n                funcName: 'addToCacheList',\n                paramName: 'entries',\n            });\n        }\n        const urlsToWarnAbout = [];\n        for (const entry of entries) {\n            // See https://github.com/GoogleChrome/workbox/issues/2259\n            if (typeof entry === 'string') {\n                urlsToWarnAbout.push(entry);\n            }\n            else if (entry && entry.revision === undefined) {\n                urlsToWarnAbout.push(entry.url);\n            }\n            const { cacheKey, url } = createCacheKey(entry);\n            const cacheMode = typeof entry !== 'string' && entry.revision ? 'reload' : 'default';\n            if (this._urlsToCacheKeys.has(url) &&\n                this._urlsToCacheKeys.get(url) !== cacheKey) {\n                throw new WorkboxError('add-to-cache-list-conflicting-entries', {\n                    firstEntry: this._urlsToCacheKeys.get(url),\n                    secondEntry: cacheKey,\n                });\n            }\n            if (typeof entry !== 'string' && entry.integrity) {\n                if (this._cacheKeysToIntegrities.has(cacheKey) &&\n                    this._cacheKeysToIntegrities.get(cacheKey) !== entry.integrity) {\n                    throw new WorkboxError('add-to-cache-list-conflicting-integrities', {\n                        url,\n                    });\n                }\n                this._cacheKeysToIntegrities.set(cacheKey, entry.integrity);\n            }\n            this._urlsToCacheKeys.set(url, cacheKey);\n            this._urlsToCacheModes.set(url, cacheMode);\n            if (urlsToWarnAbout.length > 0) {\n                const warningMessage = `Workbox is precaching URLs without revision ` +\n                    `info: ${urlsToWarnAbout.join(', ')}\\nThis is generally NOT safe. ` +\n                    `Learn more at https://bit.ly/wb-precache`;\n                if (process.env.NODE_ENV === 'production') {\n                    // Use console directly to display this warning without bloating\n                    // bundle sizes by pulling in all of the logger codebase in prod.\n                    console.warn(warningMessage);\n                }\n                else {\n                    logger.warn(warningMessage);\n                }\n            }\n        }\n    }\n    /**\n     * Precaches new and updated assets. Call this method from the service worker\n     * install event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.InstallResult>}\n     */\n    install(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const installReportPlugin = new PrecacheInstallReportPlugin();\n            this.strategy.plugins.push(installReportPlugin);\n            // Cache entries one at a time.\n            // See https://github.com/GoogleChrome/workbox/issues/2528\n            for (const [url, cacheKey] of this._urlsToCacheKeys) {\n                const integrity = this._cacheKeysToIntegrities.get(cacheKey);\n                const cacheMode = this._urlsToCacheModes.get(url);\n                const request = new Request(url, {\n                    integrity,\n                    cache: cacheMode,\n                    credentials: 'same-origin',\n                });\n                await Promise.all(this.strategy.handleAll({\n                    params: { cacheKey },\n                    request,\n                    event,\n                }));\n            }\n            const { updatedURLs, notUpdatedURLs } = installReportPlugin;\n            if (process.env.NODE_ENV !== 'production') {\n                printInstallDetails(updatedURLs, notUpdatedURLs);\n            }\n            return { updatedURLs, notUpdatedURLs };\n        });\n    }\n    /**\n     * Deletes assets that are no longer present in the current precache manifest.\n     * Call this method from the service worker activate event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.CleanupResult>}\n     */\n    activate(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            const currentlyCachedRequests = await cache.keys();\n            const expectedCacheKeys = new Set(this._urlsToCacheKeys.values());\n            const deletedURLs = [];\n            for (const request of currentlyCachedRequests) {\n                if (!expectedCacheKeys.has(request.url)) {\n                    await cache.delete(request);\n                    deletedURLs.push(request.url);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                printCleanupDetails(deletedURLs);\n            }\n            return { deletedURLs };\n        });\n    }\n    /**\n     * Returns a mapping of a precached URL to the corresponding cache key, taking\n     * into account the revision information for the URL.\n     *\n     * @return {Map<string, string>} A URL to cache key mapping.\n     */\n    getURLsToCacheKeys() {\n        return this._urlsToCacheKeys;\n    }\n    /**\n     * Returns a list of all the URLs that have been precached by the current\n     * service worker.\n     *\n     * @return {Array<string>} The precached URLs.\n     */\n    getCachedURLs() {\n        return [...this._urlsToCacheKeys.keys()];\n    }\n    /**\n     * Returns the cache key used for storing a given URL. If that URL is\n     * unversioned, like `/index.html', then the cache key will be the original\n     * URL with a search parameter appended to it.\n     *\n     * @param {string} url A URL whose cache key you want to look up.\n     * @return {string} The versioned URL that corresponds to a cache key\n     * for the original URL, or undefined if that URL isn't precached.\n     */\n    getCacheKeyForURL(url) {\n        const urlObject = new URL(url, location.href);\n        return this._urlsToCacheKeys.get(urlObject.href);\n    }\n    /**\n     * @param {string} url A cache key whose SRI you want to look up.\n     * @return {string} The subresource integrity associated with the cache key,\n     * or undefined if it's not set.\n     */\n    getIntegrityForCacheKey(cacheKey) {\n        return this._cacheKeysToIntegrities.get(cacheKey);\n    }\n    /**\n     * This acts as a drop-in replacement for\n     * [`cache.match()`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/match)\n     * with the following differences:\n     *\n     * - It knows what the name of the precache is, and only checks in that cache.\n     * - It allows you to pass in an \"original\" URL without versioning parameters,\n     * and it will automatically look up the correct cache key for the currently\n     * active revision of that URL.\n     *\n     * E.g., `matchPrecache('index.html')` will find the correct precached\n     * response for the currently active service worker, even if the actual cache\n     * key is `'/index.html?__WB_REVISION__=1234abcd'`.\n     *\n     * @param {string|Request} request The key (without revisioning parameters)\n     * to look up in the precache.\n     * @return {Promise<Response|undefined>}\n     */\n    async matchPrecache(request) {\n        const url = request instanceof Request ? request.url : request;\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (cacheKey) {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            return cache.match(cacheKey);\n        }\n        return undefined;\n    }\n    /**\n     * Returns a function that looks up `url` in the precache (taking into\n     * account revision information), and returns the corresponding `Response`.\n     *\n     * @param {string} url The precached URL which will be used to lookup the\n     * `Response`.\n     * @return {workbox-routing~handlerCallback}\n     */\n    createHandlerBoundToURL(url) {\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (!cacheKey) {\n            throw new WorkboxError('non-precached-url', { url });\n        }\n        return (options) => {\n            options.request = new Request(url);\n            options.params = Object.assign({ cacheKey }, options.params);\n            return this.strategy.handle(options);\n        };\n    }\n}\nexport { PrecacheController };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { PrecacheController } from '../PrecacheController.js';\nimport '../_version.js';\nlet precacheController;\n/**\n * @return {PrecacheController}\n * @private\n */\nexport const getOrCreatePrecacheController = () => {\n    if (!precacheController) {\n        precacheController = new PrecacheController();\n    }\n    return precacheController;\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds plugins to the precaching strategy.\n *\n * @param {Array<Object>} plugins\n *\n * @memberof workbox-precaching\n */\nfunction addPlugins(plugins) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.strategy.plugins.push(...plugins);\n}\nexport { addPlugins };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Removes any URL search parameters that should be ignored.\n *\n * @param {URL} urlObject The original URL.\n * @param {Array<RegExp>} ignoreURLParametersMatching RegExps to test against\n * each search parameter name. Matches mean that the search parameter should be\n * ignored.\n * @return {URL} The URL with any ignored search parameters removed.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching = []) {\n    // Convert the iterable into an array at the start of the loop to make sure\n    // deletion doesn't mess up iteration.\n    for (const paramName of [...urlObject.searchParams.keys()]) {\n        if (ignoreURLParametersMatching.some((regExp) => regExp.test(paramName))) {\n            urlObject.searchParams.delete(paramName);\n        }\n    }\n    return urlObject;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { removeIgnoredSearchParams } from './removeIgnoredSearchParams.js';\nimport '../_version.js';\n/**\n * Generator function that yields possible variations on the original URL to\n * check, one at a time.\n *\n * @param {string} url\n * @param {Object} options\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function* generateURLVariations(url, { ignoreURLParametersMatching = [/^utm_/, /^fbclid$/], directoryIndex = 'index.html', cleanURLs = true, urlManipulation, } = {}) {\n    const urlObject = new URL(url, location.href);\n    urlObject.hash = '';\n    yield urlObject.href;\n    const urlWithoutIgnoredParams = removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching);\n    yield urlWithoutIgnoredParams.href;\n    if (directoryIndex && urlWithoutIgnoredParams.pathname.endsWith('/')) {\n        const directoryURL = new URL(urlWithoutIgnoredParams.href);\n        directoryURL.pathname += directoryIndex;\n        yield directoryURL.href;\n    }\n    if (cleanURLs) {\n        const cleanURL = new URL(urlWithoutIgnoredParams.href);\n        cleanURL.pathname += '.html';\n        yield cleanURL.href;\n    }\n    if (urlManipulation) {\n        const additionalURLs = urlManipulation({ url: urlObject });\n        for (const urlToAttempt of additionalURLs) {\n            yield urlToAttempt.href;\n        }\n    }\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { Route } from 'workbox-routing/Route.js';\nimport { generateURLVariations } from './utils/generateURLVariations.js';\nimport './_version.js';\n/**\n * A subclass of {@link workbox-routing.Route} that takes a\n * {@link workbox-precaching.PrecacheController}\n * instance and uses it to match incoming requests and handle fetching\n * responses from the precache.\n *\n * @memberof workbox-precaching\n * @extends workbox-routing.Route\n */\nclass PrecacheRoute extends Route {\n    /**\n     * @param {PrecacheController} precacheController A `PrecacheController`\n     * instance used to both match requests and respond to fetch events.\n     * @param {Object} [options] Options to control how requests are matched\n     * against the list of precached URLs.\n     * @param {string} [options.directoryIndex=index.html] The `directoryIndex` will\n     * check cache entries for a URLs ending with '/' to see if there is a hit when\n     * appending the `directoryIndex` value.\n     * @param {Array<RegExp>} [options.ignoreURLParametersMatching=[/^utm_/, /^fbclid$/]] An\n     * array of regex's to remove search params when looking for a cache match.\n     * @param {boolean} [options.cleanURLs=true] The `cleanURLs` option will\n     * check the cache for the URL with a `.html` added to the end of the end.\n     * @param {workbox-precaching~urlManipulation} [options.urlManipulation]\n     * This is a function that should take a URL and return an array of\n     * alternative URLs that should be checked for precache matches.\n     */\n    constructor(precacheController, options) {\n        const match = ({ request, }) => {\n            const urlsToCacheKeys = precacheController.getURLsToCacheKeys();\n            for (const possibleURL of generateURLVariations(request.url, options)) {\n                const cacheKey = urlsToCacheKeys.get(possibleURL);\n                if (cacheKey) {\n                    const integrity = precacheController.getIntegrityForCacheKey(cacheKey);\n                    return { cacheKey, integrity };\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Precaching did not find a match for ` + getFriendlyURL(request.url));\n            }\n            return;\n        };\n        super(match, precacheController.strategy);\n    }\n}\nexport { PrecacheRoute };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport { PrecacheRoute } from './PrecacheRoute.js';\nimport './_version.js';\n/**\n * Add a `fetch` listener to the service worker that will\n * respond to\n * [network requests]{@link https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers#Custom_responses_to_requests}\n * with precached assets.\n *\n * Requests for assets that aren't precached, the `FetchEvent` will not be\n * responded to, allowing the event to fall through to other `fetch` event\n * listeners.\n *\n * @param {Object} [options] See the {@link workbox-precaching.PrecacheRoute}\n * options.\n *\n * @memberof workbox-precaching\n */\nfunction addRoute(options) {\n    const precacheController = getOrCreatePrecacheController();\n    const precacheRoute = new PrecacheRoute(precacheController, options);\n    registerRoute(precacheRoute);\n}\nexport { addRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst SUBSTRING_TO_FIND = '-precache-';\n/**\n * Cleans up incompatible precaches that were created by older versions of\n * Workbox, by a service worker registered under the current scope.\n *\n * This is meant to be called as part of the `activate` event.\n *\n * This should be safe to use as long as you don't include `substringToFind`\n * (defaulting to `-precache-`) in your non-precache cache names.\n *\n * @param {string} currentPrecacheName The cache name currently in use for\n * precaching. This cache won't be deleted.\n * @param {string} [substringToFind='-precache-'] Cache names which include this\n * substring will be deleted (excluding `currentPrecacheName`).\n * @return {Array<string>} A list of all the cache names that were deleted.\n *\n * @private\n * @memberof workbox-precaching\n */\nconst deleteOutdatedCaches = async (currentPrecacheName, substringToFind = SUBSTRING_TO_FIND) => {\n    const cacheNames = await self.caches.keys();\n    const cacheNamesToDelete = cacheNames.filter((cacheName) => {\n        return (cacheName.includes(substringToFind) &&\n            cacheName.includes(self.registration.scope) &&\n            cacheName !== currentPrecacheName);\n    });\n    await Promise.all(cacheNamesToDelete.map((cacheName) => self.caches.delete(cacheName)));\n    return cacheNamesToDelete;\n};\nexport { deleteOutdatedCaches };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { deleteOutdatedCaches } from './utils/deleteOutdatedCaches.js';\nimport './_version.js';\n/**\n * Adds an `activate` event listener which will clean up incompatible\n * precaches that were created by older versions of Workbox.\n *\n * @memberof workbox-precaching\n */\nfunction cleanupOutdatedCaches() {\n    // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-436484705\n    self.addEventListener('activate', ((event) => {\n        const cacheName = cacheNames.getPrecacheName();\n        event.waitUntil(deleteOutdatedCaches(cacheName).then((cachesDeleted) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (cachesDeleted.length > 0) {\n                    logger.log(`The following out-of-date precaches were cleaned up ` +\n                        `automatically:`, cachesDeleted);\n                }\n            }\n        }));\n    }));\n}\nexport { cleanupOutdatedCaches };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#createHandlerBoundToURL} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call the\n * {@link PrecacheController#createHandlerBoundToURL} on that instance,\n * instead of using this function.\n *\n * @param {string} url The precached URL which will be used to lookup the\n * `Response`.\n * @param {boolean} [fallbackToNetwork=true] Whether to attempt to get the\n * response from the network if there's a precache miss.\n * @return {workbox-routing~handlerCallback}\n *\n * @memberof workbox-precaching\n */\nfunction createHandlerBoundToURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.createHandlerBoundToURL(url);\n}\nexport { createHandlerBoundToURL };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Takes in a URL, and returns the corresponding URL that could be used to\n * lookup the entry in the precache.\n *\n * If a relative URL is provided, the location of the service worker file will\n * be used as the base.\n *\n * For precached entries without revision information, the cache key will be the\n * same as the original URL.\n *\n * For precached entries with revision information, the cache key will be the\n * original URL with the addition of a query parameter used for keeping track of\n * the revision info.\n *\n * @param {string} url The URL whose cache key to look up.\n * @return {string} The cache key that corresponds to that URL.\n *\n * @memberof workbox-precaching\n */\nfunction getCacheKeyForURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.getCacheKeyForURL(url);\n}\nexport { getCacheKeyForURL };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#matchPrecache} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call\n * {@link PrecacheController#matchPrecache} on that instance,\n * instead of using this function.\n *\n * @param {string|Request} request The key (without revisioning parameters)\n * to look up in the precache.\n * @return {Promise<Response|undefined>}\n *\n * @memberof workbox-precaching\n */\nfunction matchPrecache(request) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.matchPrecache(request);\n}\nexport { matchPrecache };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds items to the precache list, removing any duplicates and\n * stores the files in the\n * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n * worker installs.\n *\n * This method can be called multiple times.\n *\n * Please note: This method **will not** serve any of the cached files for you.\n * It only precaches files. To respond to a network request you call\n * {@link workbox-precaching.addRoute}.\n *\n * If you have a single array of files to precache, you can just call\n * {@link workbox-precaching.precacheAndRoute}.\n *\n * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n *\n * @memberof workbox-precaching\n */\nfunction precache(entries) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.precache(entries);\n}\nexport { precache };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { addRoute } from './addRoute.js';\nimport { precache } from './precache.js';\nimport './_version.js';\n/**\n * This method will add entries to the precache list and add a route to\n * respond to fetch events.\n *\n * This is a convenience method that will call\n * {@link workbox-precaching.precache} and\n * {@link workbox-precaching.addRoute} in a single call.\n *\n * @param {Array<Object|string>} entries Array of entries to precache.\n * @param {Object} [options] See the\n * {@link workbox-precaching.PrecacheRoute} options.\n *\n * @memberof workbox-precaching\n */\nfunction precacheAndRoute(entries, options) {\n    precache(entries);\n    addRoute(options);\n}\nexport { precacheAndRoute };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * `PrecacheFallbackPlugin` allows you to specify an \"offline fallback\"\n * response to be used when a given strategy is unable to generate a response.\n *\n * It does this by intercepting the `handlerDidError` plugin callback\n * and returning a precached response, taking the expected revision parameter\n * into account automatically.\n *\n * Unless you explicitly pass in a `PrecacheController` instance to the\n * constructor, the default instance will be used. Generally speaking, most\n * developers will end up using the default.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheFallbackPlugin {\n    /**\n     * Constructs a new PrecacheFallbackPlugin with the associated fallbackURL.\n     *\n     * @param {Object} config\n     * @param {string} config.fallbackURL A precached URL to use as the fallback\n     *     if the associated strategy can't generate a response.\n     * @param {PrecacheController} [config.precacheController] An optional\n     *     PrecacheController instance. If not provided, the default\n     *     PrecacheController will be used.\n     */\n    constructor({ fallbackURL, precacheController, }) {\n        /**\n         * @return {Promise<Response>} The precache response for the fallback URL.\n         *\n         * @private\n         */\n        this.handlerDidError = () => this._precacheController.matchPrecache(this._fallbackURL);\n        this._fallbackURL = fallbackURL;\n        this._precacheController =\n            precacheController || getOrCreatePrecacheController();\n    }\n}\nexport { PrecacheFallbackPlugin };\n"], "names": ["self", "_", "e", "REVISION_SEARCH_PARAM", "createCacheKey", "entry", "WorkboxError", "urlObject", "URL", "location", "href", "cache<PERSON>ey", "url", "revision", "cacheKeyURL", "originalURL", "searchParams", "set", "PrecacheInstallReportPlugin", "constructor", "updatedURLs", "notUpdatedURLs", "handlerWillStart", "request", "state", "originalRequest", "cachedResponseWillBeUsed", "event", "cachedResponse", "type", "Request", "push", "PrecacheCacheKeyPlugin", "precacheController", "cacheKeyWillBeUsed", "params", "_precacheController", "getCacheKeyForURL", "headers", "logGroup", "groupTitle", "deletedURLs", "logger", "groupCollapsed", "log", "groupEnd", "printCleanupDetails", "deletionCount", "length", "_nestedGroup", "urls", "printInstallDetails", "urlsToPrecache", "urlsAlreadyPrecached", "precachedCount", "alreadyPrecachedCount", "message", "PrecacheStrategy", "Strategy", "options", "cacheName", "cacheNames", "getPrecacheName", "_fallbackToNetwork", "fallbackToNetwork", "plugins", "copyRedirectedCacheableResponsesPlugin", "_handle", "handler", "response", "cacheMatch", "_handleInstall", "_handleFetch", "warn", "getFriendlyURL", "integrityInManifest", "integrity", "integrityInRequest", "noIntegrityConflict", "fetch", "mode", "undefined", "_useDefaultCacheabilityPluginIfNeeded", "was<PERSON>ached", "cachePut", "clone", "get<PERSON><PERSON><PERSON><PERSON>", "status", "defaultPluginIndex", "cacheWillUpdatePluginCount", "index", "plugin", "entries", "defaultPrecacheCacheabilityPlugin", "cacheWillUpdate", "splice", "redirected", "copyResponse", "PrecacheController", "_urlsTo<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "_urlsToCacheModes", "_cacheKeysToIntegrities", "_strategy", "install", "bind", "activate", "strategy", "precache", "addToCacheList", "_installAndActiveListenersAdded", "addEventListener", "assert", "isArray", "moduleName", "className", "funcName", "paramName", "urlsToWarnAbout", "cacheMode", "has", "get", "firstEntry", "secondEntry", "warningMessage", "join", "waitUntil", "installReportPlugin", "cache", "credentials", "Promise", "all", "handleAll", "caches", "open", "currentlyCachedRequests", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Set", "values", "delete", "getURLsToCacheKeys", "getCachedURLs", "getIntegrityForCacheKey", "matchPrecache", "match", "createHandlerBoundToURL", "Object", "assign", "handle", "getOrCreatePrecacheController", "addPlugins", "removeIgnoredSearchParams", "ignoreURLParametersMatching", "some", "regExp", "test", "generateURLVariations", "directoryIndex", "cleanURLs", "urlManipulation", "hash", "urlWithoutIgnoredParams", "pathname", "endsWith", "directoryURL", "cleanURL", "additionalURLs", "urlToAttempt", "PrecacheRoute", "Route", "urlsTo<PERSON>ache<PERSON><PERSON>s", "possibleURL", "debug", "addRoute", "precacheRoute", "registerRoute", "SUBSTRING_TO_FIND", "deleteOutdatedCaches", "currentPrecacheName", "substringToFind", "cacheNamesToDelete", "filter", "includes", "registration", "scope", "map", "cleanupOutdatedCaches", "then", "cachesDeleted", "precacheAndRoute", "PrecacheFallbackPlugin", "fallbackURL", "handlerDidError", "_fallbackURL"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,0BAAD,CAAJ,IAAoCC,CAAC,EAArC;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;;IAIA,MAAMC,qBAAqB,GAAG,iBAA9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACO,SAASC,cAAT,CAAwBC,KAAxB,EAA+B;IAClC,MAAI,CAACA,KAAL,EAAY;IACR,UAAM,IAAIC,4BAAJ,CAAiB,mCAAjB,EAAsD;IAAED,MAAAA;IAAF,KAAtD,CAAN;IACH,GAHiC;IAKlC;;;IACA,MAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;IAC3B,UAAME,SAAS,GAAG,IAAIC,GAAJ,CAAQH,KAAR,EAAeI,QAAQ,CAACC,IAAxB,CAAlB;IACA,WAAO;IACHC,MAAAA,QAAQ,EAAEJ,SAAS,CAACG,IADjB;IAEHE,MAAAA,GAAG,EAAEL,SAAS,CAACG;IAFZ,KAAP;IAIH;;IACD,QAAM;IAAEG,IAAAA,QAAF;IAAYD,IAAAA;IAAZ,MAAoBP,KAA1B;;IACA,MAAI,CAACO,GAAL,EAAU;IACN,UAAM,IAAIN,4BAAJ,CAAiB,mCAAjB,EAAsD;IAAED,MAAAA;IAAF,KAAtD,CAAN;IACH,GAhBiC;IAkBlC;;;IACA,MAAI,CAACQ,QAAL,EAAe;IACX,UAAMN,SAAS,GAAG,IAAIC,GAAJ,CAAQI,GAAR,EAAaH,QAAQ,CAACC,IAAtB,CAAlB;IACA,WAAO;IACHC,MAAAA,QAAQ,EAAEJ,SAAS,CAACG,IADjB;IAEHE,MAAAA,GAAG,EAAEL,SAAS,CAACG;IAFZ,KAAP;IAIH,GAzBiC;IA2BlC;;;IACA,QAAMI,WAAW,GAAG,IAAIN,GAAJ,CAAQI,GAAR,EAAaH,QAAQ,CAACC,IAAtB,CAApB;IACA,QAAMK,WAAW,GAAG,IAAIP,GAAJ,CAAQI,GAAR,EAAaH,QAAQ,CAACC,IAAtB,CAApB;IACAI,EAAAA,WAAW,CAACE,YAAZ,CAAyBC,GAAzB,CAA6Bd,qBAA7B,EAAoDU,QAApD;IACA,SAAO;IACHF,IAAAA,QAAQ,EAAEG,WAAW,CAACJ,IADnB;IAEHE,IAAAA,GAAG,EAAEG,WAAW,CAACL;IAFd,GAAP;IAIH;;ICvDD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMQ,2BAAN,CAAkC;IAC9BC,EAAAA,WAAW,GAAG;IACV,SAAKC,WAAL,GAAmB,EAAnB;IACA,SAAKC,cAAL,GAAsB,EAAtB;;IACA,SAAKC,gBAAL,GAAwB,OAAO;IAAEC,MAAAA,OAAF;IAAWC,MAAAA;IAAX,KAAP,KAA+B;IACnD;IACA,UAAIA,KAAJ,EAAW;IACPA,QAAAA,KAAK,CAACC,eAAN,GAAwBF,OAAxB;IACH;IACJ,KALD;;IAMA,SAAKG,wBAAL,GAAgC,OAAO;IAAEC,MAAAA,KAAF;IAASH,MAAAA,KAAT;IAAgBI,MAAAA;IAAhB,KAAP,KAA6C;IACzE,UAAID,KAAK,CAACE,IAAN,KAAe,SAAnB,EAA8B;IAC1B,YAAIL,KAAK,IACLA,KAAK,CAACC,eADN,IAEAD,KAAK,CAACC,eAAN,YAAiCK,OAFrC,EAE8C;IAC1C;IACA,gBAAMlB,GAAG,GAAGY,KAAK,CAACC,eAAN,CAAsBb,GAAlC;;IACA,cAAIgB,cAAJ,EAAoB;IAChB,iBAAKP,cAAL,CAAoBU,IAApB,CAAyBnB,GAAzB;IACH,WAFD,MAGK;IACD,iBAAKQ,WAAL,CAAiBW,IAAjB,CAAsBnB,GAAtB;IACH;IACJ;IACJ;;IACD,aAAOgB,cAAP;IACH,KAhBD;IAiBH;;IA3B6B;;ICdlC;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMI,sBAAN,CAA6B;IACzBb,EAAAA,WAAW,CAAC;IAAEc,IAAAA;IAAF,GAAD,EAAyB;IAChC,SAAKC,kBAAL,GAA0B,OAAO;IAAEX,MAAAA,OAAF;IAAWY,MAAAA;IAAX,KAAP,KAAgC;IACtD;;IACA;IACA,YAAMxB,QAAQ,GAAG,CAACwB,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACxB,QAAxD,KACb,KAAKyB,mBAAL,CAAyBC,iBAAzB,CAA2Cd,OAAO,CAACX,GAAnD,CADJ;IAEA;;;IACA,aAAOD,QAAQ,GACT,IAAImB,OAAJ,CAAYnB,QAAZ,EAAsB;IAAE2B,QAAAA,OAAO,EAAEf,OAAO,CAACe;IAAnB,OAAtB,CADS,GAETf,OAFN;IAGH,KATD;;IAUA,SAAKa,mBAAL,GAA2BH,kBAA3B;IACH;;IAbwB;;ICd7B;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMM,QAAQ,GAAG,CAACC,UAAD,EAAaC,WAAb,KAA6B;IAC1CC,EAAAA,gBAAM,CAACC,cAAP,CAAsBH,UAAtB;;IACA,OAAK,MAAM5B,GAAX,IAAkB6B,WAAlB,EAA+B;IAC3BC,IAAAA,gBAAM,CAACE,GAAP,CAAWhC,GAAX;IACH;;IACD8B,EAAAA,gBAAM,CAACG,QAAP;IACH,CAND;IAOA;IACA;IACA;IACA;IACA;IACA;;;IACO,SAASC,mBAAT,CAA6BL,WAA7B,EAA0C;IAC7C,QAAMM,aAAa,GAAGN,WAAW,CAACO,MAAlC;;IACA,MAAID,aAAa,GAAG,CAApB,EAAuB;IACnBL,IAAAA,gBAAM,CAACC,cAAP,CAAuB,6BAAD,GACjB,GAAEI,aAAc,UADC,GAEjB,UAASA,aAAa,KAAK,CAAlB,GAAsB,MAAtB,GAA+B,QAAS,WAFtD;IAGAR,IAAAA,QAAQ,CAAC,wBAAD,EAA2BE,WAA3B,CAAR;IACAC,IAAAA,gBAAM,CAACG,QAAP;IACH;IACJ;;ICrCD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASI,YAAT,CAAsBT,UAAtB,EAAkCU,IAAlC,EAAwC;IACpC,MAAIA,IAAI,CAACF,MAAL,KAAgB,CAApB,EAAuB;IACnB;IACH;;IACDN,EAAAA,gBAAM,CAACC,cAAP,CAAsBH,UAAtB;;IACA,OAAK,MAAM5B,GAAX,IAAkBsC,IAAlB,EAAwB;IACpBR,IAAAA,gBAAM,CAACE,GAAP,CAAWhC,GAAX;IACH;;IACD8B,EAAAA,gBAAM,CAACG,QAAP;IACH;IACD;IACA;IACA;IACA;IACA;IACA;IACA;;;IACO,SAASM,mBAAT,CAA6BC,cAA7B,EAA6CC,oBAA7C,EAAmE;IACtE,QAAMC,cAAc,GAAGF,cAAc,CAACJ,MAAtC;IACA,QAAMO,qBAAqB,GAAGF,oBAAoB,CAACL,MAAnD;;IACA,MAAIM,cAAc,IAAIC,qBAAtB,EAA6C;IACzC,QAAIC,OAAO,GAAI,cAAaF,cAAe,QAAOA,cAAc,KAAK,CAAnB,GAAuB,EAAvB,GAA4B,GAAI,GAAlF;;IACA,QAAIC,qBAAqB,GAAG,CAA5B,EAA+B;IAC3BC,MAAAA,OAAO,IACF,IAAGD,qBAAsB,GAA1B,GACK,OAAMA,qBAAqB,KAAK,CAA1B,GAA8B,KAA9B,GAAsC,OAAQ,kBAF7D;IAGH;;IACDb,IAAAA,gBAAM,CAACC,cAAP,CAAsBa,OAAtB;;IACAP,IAAAA,YAAY,CAAE,4BAAF,EAA+BG,cAA/B,CAAZ;;IACAH,IAAAA,YAAY,CAAE,iCAAF,EAAoCI,oBAApC,CAAZ;;IACAX,IAAAA,gBAAM,CAACG,QAAP;IACH;IACJ;;IC/CD;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMY,gBAAN,SAA+BC,oBAA/B,CAAwC;IACpC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIvC,EAAAA,WAAW,CAACwC,OAAO,GAAG,EAAX,EAAe;IACtBA,IAAAA,OAAO,CAACC,SAAR,GAAoBC,wBAAU,CAACC,eAAX,CAA2BH,OAAO,CAACC,SAAnC,CAApB;IACA,UAAMD,OAAN;IACA,SAAKI,kBAAL,GACIJ,OAAO,CAACK,iBAAR,KAA8B,KAA9B,GAAsC,KAAtC,GAA8C,IADlD,CAHsB;IAMtB;IACA;IACA;;IACA,SAAKC,OAAL,CAAalC,IAAb,CAAkB0B,gBAAgB,CAACS,sCAAnC;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMC,OAAN,CAAc5C,OAAd,EAAuB6C,OAAvB,EAAgC;IAC5B,UAAMC,QAAQ,GAAG,MAAMD,OAAO,CAACE,UAAR,CAAmB/C,OAAnB,CAAvB;;IACA,QAAI8C,QAAJ,EAAc;IACV,aAAOA,QAAP;IACH,KAJ2B;IAM5B;;;IACA,QAAID,OAAO,CAACzC,KAAR,IAAiByC,OAAO,CAACzC,KAAR,CAAcE,IAAd,KAAuB,SAA5C,EAAuD;IACnD,aAAO,MAAM,KAAK0C,cAAL,CAAoBhD,OAApB,EAA6B6C,OAA7B,CAAb;IACH,KAT2B;IAW5B;;;IACA,WAAO,MAAM,KAAKI,YAAL,CAAkBjD,OAAlB,EAA2B6C,OAA3B,CAAb;IACH;;IACD,QAAMI,YAAN,CAAmBjD,OAAnB,EAA4B6C,OAA5B,EAAqC;IACjC,QAAIC,QAAJ;IACA,UAAMlC,MAAM,GAAIiC,OAAO,CAACjC,MAAR,IAAkB,EAAlC,CAFiC;;IAIjC,QAAI,KAAK4B,kBAAT,EAA6B;IACzB,MAA2C;IACvCrB,QAAAA,gBAAM,CAAC+B,IAAP,CAAa,6BAAD,GACP,GAAEC,gCAAc,CAACnD,OAAO,CAACX,GAAT,CAAc,OAAM,KAAKgD,SAAU,WAD5C,GAEP,qCAFL;IAGH;;IACD,YAAMe,mBAAmB,GAAGxC,MAAM,CAACyC,SAAnC;IACA,YAAMC,kBAAkB,GAAGtD,OAAO,CAACqD,SAAnC;IACA,YAAME,mBAAmB,GAAG,CAACD,kBAAD,IAAuBA,kBAAkB,KAAKF,mBAA1E,CARyB;IAUzB;;IACAN,MAAAA,QAAQ,GAAG,MAAMD,OAAO,CAACW,KAAR,CAAc,IAAIjD,OAAJ,CAAYP,OAAZ,EAAqB;IAChDqD,QAAAA,SAAS,EAAErD,OAAO,CAACyD,IAAR,KAAiB,SAAjB,GACLH,kBAAkB,IAAIF,mBADjB,GAELM;IAH0C,OAArB,CAAd,CAAjB,CAXyB;IAiBzB;IACA;IACA;IACA;IACA;IACA;;IACA,UAAIN,mBAAmB,IACnBG,mBADA,IAEAvD,OAAO,CAACyD,IAAR,KAAiB,SAFrB,EAEgC;IAC5B,aAAKE,qCAAL;;IACA,cAAMC,SAAS,GAAG,MAAMf,OAAO,CAACgB,QAAR,CAAiB7D,OAAjB,EAA0B8C,QAAQ,CAACgB,KAAT,EAA1B,CAAxB;;IACA,QAA2C;IACvC,cAAIF,SAAJ,EAAe;IACXzC,YAAAA,gBAAM,CAACE,GAAP,CAAY,kBAAiB8B,gCAAc,CAACnD,OAAO,CAACX,GAAT,CAAc,GAA9C,GACN,oCADL;IAEH;IACJ;IACJ;IACJ,KAnCD,MAoCK;IACD;IACA;IACA,YAAM,IAAIN,4BAAJ,CAAiB,wBAAjB,EAA2C;IAC7CsD,QAAAA,SAAS,EAAE,KAAKA,SAD6B;IAE7ChD,QAAAA,GAAG,EAAEW,OAAO,CAACX;IAFgC,OAA3C,CAAN;IAIH;;IACD,IAA2C;IACvC,YAAMD,QAAQ,GAAGwB,MAAM,CAACxB,QAAP,KAAoB,MAAMyD,OAAO,CAACkB,WAAR,CAAoB/D,OAApB,EAA6B,MAA7B,CAA1B,CAAjB,CADuC;IAGvC;;IACAmB,MAAAA,gBAAM,CAACC,cAAP,CAAuB,+BAAD,GAAkC+B,gCAAc,CAACnD,OAAO,CAACX,GAAT,CAAtE;IACA8B,MAAAA,gBAAM,CAACE,GAAP,CAAY,8BAA6B8B,gCAAc,CAAC/D,QAAQ,YAAYmB,OAApB,GAA8BnB,QAAQ,CAACC,GAAvC,GAA6CD,QAA9C,CAAwD,EAA/G;IACA+B,MAAAA,gBAAM,CAACC,cAAP,CAAuB,4BAAvB;IACAD,MAAAA,gBAAM,CAACE,GAAP,CAAWrB,OAAX;IACAmB,MAAAA,gBAAM,CAACG,QAAP;IACAH,MAAAA,gBAAM,CAACC,cAAP,CAAuB,6BAAvB;IACAD,MAAAA,gBAAM,CAACE,GAAP,CAAWyB,QAAX;IACA3B,MAAAA,gBAAM,CAACG,QAAP;IACAH,MAAAA,gBAAM,CAACG,QAAP;IACH;;IACD,WAAOwB,QAAP;IACH;;IACD,QAAME,cAAN,CAAqBhD,OAArB,EAA8B6C,OAA9B,EAAuC;IACnC,SAAKc,qCAAL;;IACA,UAAMb,QAAQ,GAAG,MAAMD,OAAO,CAACW,KAAR,CAAcxD,OAAd,CAAvB,CAFmC;IAInC;;IACA,UAAM4D,SAAS,GAAG,MAAMf,OAAO,CAACgB,QAAR,CAAiB7D,OAAjB,EAA0B8C,QAAQ,CAACgB,KAAT,EAA1B,CAAxB;;IACA,QAAI,CAACF,SAAL,EAAgB;IACZ;IACA;IACA,YAAM,IAAI7E,4BAAJ,CAAiB,yBAAjB,EAA4C;IAC9CM,QAAAA,GAAG,EAAEW,OAAO,CAACX,GADiC;IAE9C2E,QAAAA,MAAM,EAAElB,QAAQ,CAACkB;IAF6B,OAA5C,CAAN;IAIH;;IACD,WAAOlB,QAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIa,EAAAA,qCAAqC,GAAG;IACpC,QAAIM,kBAAkB,GAAG,IAAzB;IACA,QAAIC,0BAA0B,GAAG,CAAjC;;IACA,SAAK,MAAM,CAACC,KAAD,EAAQC,MAAR,CAAX,IAA8B,KAAK1B,OAAL,CAAa2B,OAAb,EAA9B,EAAsD;IAClD;IACA,UAAID,MAAM,KAAKlC,gBAAgB,CAACS,sCAAhC,EAAwE;IACpE;IACH,OAJiD;;;IAMlD,UAAIyB,MAAM,KAAKlC,gBAAgB,CAACoC,iCAAhC,EAAmE;IAC/DL,QAAAA,kBAAkB,GAAGE,KAArB;IACH;;IACD,UAAIC,MAAM,CAACG,eAAX,EAA4B;IACxBL,QAAAA,0BAA0B;IAC7B;IACJ;;IACD,QAAIA,0BAA0B,KAAK,CAAnC,EAAsC;IAClC,WAAKxB,OAAL,CAAalC,IAAb,CAAkB0B,gBAAgB,CAACoC,iCAAnC;IACH,KAFD,MAGK,IAAIJ,0BAA0B,GAAG,CAA7B,IAAkCD,kBAAkB,KAAK,IAA7D,EAAmE;IACpE;IACA,WAAKvB,OAAL,CAAa8B,MAAb,CAAoBP,kBAApB,EAAwC,CAAxC;IACH,KAtBmC;;IAwBvC;;IArLmC;;IAuLxC/B,gBAAgB,CAACoC,iCAAjB,GAAqD;IACjD,QAAMC,eAAN,CAAsB;IAAEzB,IAAAA;IAAF,GAAtB,EAAoC;IAChC,QAAI,CAACA,QAAD,IAAaA,QAAQ,CAACkB,MAAT,IAAmB,GAApC,EAAyC;IACrC,aAAO,IAAP;IACH;;IACD,WAAOlB,QAAP;IACH;;IANgD,CAArD;IAQAZ,gBAAgB,CAACS,sCAAjB,GAA0D;IACtD,QAAM4B,eAAN,CAAsB;IAAEzB,IAAAA;IAAF,GAAtB,EAAoC;IAChC,WAAOA,QAAQ,CAAC2B,UAAT,GAAsB,MAAMC,4BAAY,CAAC5B,QAAD,CAAxC,GAAqDA,QAA5D;IACH;;IAHqD,CAA1D;;ICzNA;IACA;AACA;IACA;IACA;IACA;IACA;IAaA;IACA;IACA;IACA;IACA;;IACA,MAAM6B,kBAAN,CAAyB;IACrB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI/E,EAAAA,WAAW,CAAC;IAAEyC,IAAAA,SAAF;IAAaK,IAAAA,OAAO,GAAG,EAAvB;IAA2BD,IAAAA,iBAAiB,GAAG;IAA/C,MAAyD,EAA1D,EAA8D;IACrE,SAAKmC,gBAAL,GAAwB,IAAIC,GAAJ,EAAxB;IACA,SAAKC,iBAAL,GAAyB,IAAID,GAAJ,EAAzB;IACA,SAAKE,uBAAL,GAA+B,IAAIF,GAAJ,EAA/B;IACA,SAAKG,SAAL,GAAiB,IAAI9C,gBAAJ,CAAqB;IAClCG,MAAAA,SAAS,EAAEC,wBAAU,CAACC,eAAX,CAA2BF,SAA3B,CADuB;IAElCK,MAAAA,OAAO,EAAE,CACL,GAAGA,OADE,EAEL,IAAIjC,sBAAJ,CAA2B;IAAEC,QAAAA,kBAAkB,EAAE;IAAtB,OAA3B,CAFK,CAFyB;IAMlC+B,MAAAA;IANkC,KAArB,CAAjB,CAJqE;;IAarE,SAAKwC,OAAL,GAAe,KAAKA,OAAL,CAAaC,IAAb,CAAkB,IAAlB,CAAf;IACA,SAAKC,QAAL,GAAgB,KAAKA,QAAL,CAAcD,IAAd,CAAmB,IAAnB,CAAhB;IACH;IACD;IACJ;IACA;IACA;;;IACI,MAAIE,QAAJ,GAAe;IACX,WAAO,KAAKJ,SAAZ;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIK,EAAAA,QAAQ,CAAChB,OAAD,EAAU;IACd,SAAKiB,cAAL,CAAoBjB,OAApB;;IACA,QAAI,CAAC,KAAKkB,+BAAV,EAA2C;IACvC9G,MAAAA,IAAI,CAAC+G,gBAAL,CAAsB,SAAtB,EAAiC,KAAKP,OAAtC;IACAxG,MAAAA,IAAI,CAAC+G,gBAAL,CAAsB,UAAtB,EAAkC,KAAKL,QAAvC;IACA,WAAKI,+BAAL,GAAuC,IAAvC;IACH;IACJ;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;;;IACID,EAAAA,cAAc,CAACjB,OAAD,EAAU;IACpB,IAA2C;IACvCoB,MAAAA,gBAAM,CAACC,OAAP,CAAerB,OAAf,EAAwB;IACpBsB,QAAAA,UAAU,EAAE,oBADQ;IAEpBC,QAAAA,SAAS,EAAE,oBAFS;IAGpBC,QAAAA,QAAQ,EAAE,gBAHU;IAIpBC,QAAAA,SAAS,EAAE;IAJS,OAAxB;IAMH;;IACD,UAAMC,eAAe,GAAG,EAAxB;;IACA,SAAK,MAAMjH,KAAX,IAAoBuF,OAApB,EAA6B;IACzB;IACA,UAAI,OAAOvF,KAAP,KAAiB,QAArB,EAA+B;IAC3BiH,QAAAA,eAAe,CAACvF,IAAhB,CAAqB1B,KAArB;IACH,OAFD,MAGK,IAAIA,KAAK,IAAIA,KAAK,CAACQ,QAAN,KAAmBoE,SAAhC,EAA2C;IAC5CqC,QAAAA,eAAe,CAACvF,IAAhB,CAAqB1B,KAAK,CAACO,GAA3B;IACH;;IACD,YAAM;IAAED,QAAAA,QAAF;IAAYC,QAAAA;IAAZ,UAAoBR,cAAc,CAACC,KAAD,CAAxC;IACA,YAAMkH,SAAS,GAAG,OAAOlH,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,CAACQ,QAAnC,GAA8C,QAA9C,GAAyD,SAA3E;;IACA,UAAI,KAAKsF,gBAAL,CAAsBqB,GAAtB,CAA0B5G,GAA1B,KACA,KAAKuF,gBAAL,CAAsBsB,GAAtB,CAA0B7G,GAA1B,MAAmCD,QADvC,EACiD;IAC7C,cAAM,IAAIL,4BAAJ,CAAiB,uCAAjB,EAA0D;IAC5DoH,UAAAA,UAAU,EAAE,KAAKvB,gBAAL,CAAsBsB,GAAtB,CAA0B7G,GAA1B,CADgD;IAE5D+G,UAAAA,WAAW,EAAEhH;IAF+C,SAA1D,CAAN;IAIH;;IACD,UAAI,OAAON,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,CAACuE,SAAvC,EAAkD;IAC9C,YAAI,KAAK0B,uBAAL,CAA6BkB,GAA7B,CAAiC7G,QAAjC,KACA,KAAK2F,uBAAL,CAA6BmB,GAA7B,CAAiC9G,QAAjC,MAA+CN,KAAK,CAACuE,SADzD,EACoE;IAChE,gBAAM,IAAItE,4BAAJ,CAAiB,2CAAjB,EAA8D;IAChEM,YAAAA;IADgE,WAA9D,CAAN;IAGH;;IACD,aAAK0F,uBAAL,CAA6BrF,GAA7B,CAAiCN,QAAjC,EAA2CN,KAAK,CAACuE,SAAjD;IACH;;IACD,WAAKuB,gBAAL,CAAsBlF,GAAtB,CAA0BL,GAA1B,EAA+BD,QAA/B;;IACA,WAAK0F,iBAAL,CAAuBpF,GAAvB,CAA2BL,GAA3B,EAAgC2G,SAAhC;;IACA,UAAID,eAAe,CAACtE,MAAhB,GAAyB,CAA7B,EAAgC;IAC5B,cAAM4E,cAAc,GAAI,8CAAD,GAClB,SAAQN,eAAe,CAACO,IAAhB,CAAqB,IAArB,CAA2B,gCADjB,GAElB,0CAFL;;IAGA,QAKK;IACDnF,UAAAA,gBAAM,CAAC+B,IAAP,CAAYmD,cAAZ;IACH;IACJ;IACJ;IACJ;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIpB,EAAAA,OAAO,CAAC7E,KAAD,EAAQ;IACX;IACA;IACA,WAAOmG,sBAAS,CAACnG,KAAD,EAAQ,YAAY;IAChC,YAAMoG,mBAAmB,GAAG,IAAI7G,2BAAJ,EAA5B;IACA,WAAKyF,QAAL,CAAc1C,OAAd,CAAsBlC,IAAtB,CAA2BgG,mBAA3B,EAFgC;IAIhC;;IACA,WAAK,MAAM,CAACnH,GAAD,EAAMD,QAAN,CAAX,IAA8B,KAAKwF,gBAAnC,EAAqD;IACjD,cAAMvB,SAAS,GAAG,KAAK0B,uBAAL,CAA6BmB,GAA7B,CAAiC9G,QAAjC,CAAlB;;IACA,cAAM4G,SAAS,GAAG,KAAKlB,iBAAL,CAAuBoB,GAAvB,CAA2B7G,GAA3B,CAAlB;;IACA,cAAMW,OAAO,GAAG,IAAIO,OAAJ,CAAYlB,GAAZ,EAAiB;IAC7BgE,UAAAA,SAD6B;IAE7BoD,UAAAA,KAAK,EAAET,SAFsB;IAG7BU,UAAAA,WAAW,EAAE;IAHgB,SAAjB,CAAhB;IAKA,cAAMC,OAAO,CAACC,GAAR,CAAY,KAAKxB,QAAL,CAAcyB,SAAd,CAAwB;IACtCjG,UAAAA,MAAM,EAAE;IAAExB,YAAAA;IAAF,WAD8B;IAEtCY,UAAAA,OAFsC;IAGtCI,UAAAA;IAHsC,SAAxB,CAAZ,CAAN;IAKH;;IACD,YAAM;IAAEP,QAAAA,WAAF;IAAeC,QAAAA;IAAf,UAAkC0G,mBAAxC;;IACA,MAA2C;IACvC5E,QAAAA,mBAAmB,CAAC/B,WAAD,EAAcC,cAAd,CAAnB;IACH;;IACD,aAAO;IAAED,QAAAA,WAAF;IAAeC,QAAAA;IAAf,OAAP;IACH,KAxBe,CAAhB;IAyBH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIqF,EAAAA,QAAQ,CAAC/E,KAAD,EAAQ;IACZ;IACA;IACA,WAAOmG,sBAAS,CAACnG,KAAD,EAAQ,YAAY;IAChC,YAAMqG,KAAK,GAAG,MAAMhI,IAAI,CAACqI,MAAL,CAAYC,IAAZ,CAAiB,KAAK3B,QAAL,CAAc/C,SAA/B,CAApB;IACA,YAAM2E,uBAAuB,GAAG,MAAMP,KAAK,CAACQ,IAAN,EAAtC;IACA,YAAMC,iBAAiB,GAAG,IAAIC,GAAJ,CAAQ,KAAKvC,gBAAL,CAAsBwC,MAAtB,EAAR,CAA1B;IACA,YAAMlG,WAAW,GAAG,EAApB;;IACA,WAAK,MAAMlB,OAAX,IAAsBgH,uBAAtB,EAA+C;IAC3C,YAAI,CAACE,iBAAiB,CAACjB,GAAlB,CAAsBjG,OAAO,CAACX,GAA9B,CAAL,EAAyC;IACrC,gBAAMoH,KAAK,CAACY,MAAN,CAAarH,OAAb,CAAN;IACAkB,UAAAA,WAAW,CAACV,IAAZ,CAAiBR,OAAO,CAACX,GAAzB;IACH;IACJ;;IACD,MAA2C;IACvCkC,QAAAA,mBAAmB,CAACL,WAAD,CAAnB;IACH;;IACD,aAAO;IAAEA,QAAAA;IAAF,OAAP;IACH,KAfe,CAAhB;IAgBH;IACD;IACJ;IACA;IACA;IACA;IACA;;;IACIoG,EAAAA,kBAAkB,GAAG;IACjB,WAAO,KAAK1C,gBAAZ;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;;;IACI2C,EAAAA,aAAa,GAAG;IACZ,WAAO,CAAC,GAAG,KAAK3C,gBAAL,CAAsBqC,IAAtB,EAAJ,CAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACInG,EAAAA,iBAAiB,CAACzB,GAAD,EAAM;IACnB,UAAML,SAAS,GAAG,IAAIC,GAAJ,CAAQI,GAAR,EAAaH,QAAQ,CAACC,IAAtB,CAAlB;IACA,WAAO,KAAKyF,gBAAL,CAAsBsB,GAAtB,CAA0BlH,SAAS,CAACG,IAApC,CAAP;IACH;IACD;IACJ;IACA;IACA;IACA;;;IACIqI,EAAAA,uBAAuB,CAACpI,QAAD,EAAW;IAC9B,WAAO,KAAK2F,uBAAL,CAA6BmB,GAA7B,CAAiC9G,QAAjC,CAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMqI,aAAN,CAAoBzH,OAApB,EAA6B;IACzB,UAAMX,GAAG,GAAGW,OAAO,YAAYO,OAAnB,GAA6BP,OAAO,CAACX,GAArC,GAA2CW,OAAvD;IACA,UAAMZ,QAAQ,GAAG,KAAK0B,iBAAL,CAAuBzB,GAAvB,CAAjB;;IACA,QAAID,QAAJ,EAAc;IACV,YAAMqH,KAAK,GAAG,MAAMhI,IAAI,CAACqI,MAAL,CAAYC,IAAZ,CAAiB,KAAK3B,QAAL,CAAc/C,SAA/B,CAApB;IACA,aAAOoE,KAAK,CAACiB,KAAN,CAAYtI,QAAZ,CAAP;IACH;;IACD,WAAOsE,SAAP;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIiE,EAAAA,uBAAuB,CAACtI,GAAD,EAAM;IACzB,UAAMD,QAAQ,GAAG,KAAK0B,iBAAL,CAAuBzB,GAAvB,CAAjB;;IACA,QAAI,CAACD,QAAL,EAAe;IACX,YAAM,IAAIL,4BAAJ,CAAiB,mBAAjB,EAAsC;IAAEM,QAAAA;IAAF,OAAtC,CAAN;IACH;;IACD,WAAQ+C,OAAD,IAAa;IAChBA,MAAAA,OAAO,CAACpC,OAAR,GAAkB,IAAIO,OAAJ,CAAYlB,GAAZ,CAAlB;IACA+C,MAAAA,OAAO,CAACxB,MAAR,GAAiBgH,MAAM,CAACC,MAAP,CAAc;IAAEzI,QAAAA;IAAF,OAAd,EAA4BgD,OAAO,CAACxB,MAApC,CAAjB;IACA,aAAO,KAAKwE,QAAL,CAAc0C,MAAd,CAAqB1F,OAArB,CAAP;IACH,KAJD;IAKH;;IAzQoB;;ICxBzB;IACA;AACA;IACA;IACA;IACA;IACA;IAGA,IAAI1B,kBAAJ;IACA;IACA;IACA;IACA;;IACO,MAAMqH,6BAA6B,GAAG,MAAM;IAC/C,MAAI,CAACrH,kBAAL,EAAyB;IACrBA,IAAAA,kBAAkB,GAAG,IAAIiE,kBAAJ,EAArB;IACH;;IACD,SAAOjE,kBAAP;IACH,CALM;;ICdP;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASsH,UAAT,CAAoBtF,OAApB,EAA6B;IACzB,QAAMhC,kBAAkB,GAAGqH,6BAA6B,EAAxD;IACArH,EAAAA,kBAAkB,CAAC0E,QAAnB,CAA4B1C,OAA5B,CAAoClC,IAApC,CAAyC,GAAGkC,OAA5C;IACH;;ICnBD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACO,SAASuF,yBAAT,CAAmCjJ,SAAnC,EAA8CkJ,2BAA2B,GAAG,EAA5E,EAAgF;IACnF;IACA;IACA,OAAK,MAAMpC,SAAX,IAAwB,CAAC,GAAG9G,SAAS,CAACS,YAAV,CAAuBwH,IAAvB,EAAJ,CAAxB,EAA4D;IACxD,QAAIiB,2BAA2B,CAACC,IAA5B,CAAkCC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAYvC,SAAZ,CAA7C,CAAJ,EAA0E;IACtE9G,MAAAA,SAAS,CAACS,YAAV,CAAuB4H,MAAvB,CAA8BvB,SAA9B;IACH;IACJ;;IACD,SAAO9G,SAAP;IACH;;IC7BD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACO,UAAUsJ,qBAAV,CAAgCjJ,GAAhC,EAAqC;IAAE6I,EAAAA,2BAA2B,GAAG,CAAC,OAAD,EAAU,UAAV,CAAhC;IAAuDK,EAAAA,cAAc,GAAG,YAAxE;IAAsFC,EAAAA,SAAS,GAAG,IAAlG;IAAwGC,EAAAA;IAAxG,IAA6H,EAAlK,EAAsK;IACzK,QAAMzJ,SAAS,GAAG,IAAIC,GAAJ,CAAQI,GAAR,EAAaH,QAAQ,CAACC,IAAtB,CAAlB;IACAH,EAAAA,SAAS,CAAC0J,IAAV,GAAiB,EAAjB;IACA,QAAM1J,SAAS,CAACG,IAAhB;IACA,QAAMwJ,uBAAuB,GAAGV,yBAAyB,CAACjJ,SAAD,EAAYkJ,2BAAZ,CAAzD;IACA,QAAMS,uBAAuB,CAACxJ,IAA9B;;IACA,MAAIoJ,cAAc,IAAII,uBAAuB,CAACC,QAAxB,CAAiCC,QAAjC,CAA0C,GAA1C,CAAtB,EAAsE;IAClE,UAAMC,YAAY,GAAG,IAAI7J,GAAJ,CAAQ0J,uBAAuB,CAACxJ,IAAhC,CAArB;IACA2J,IAAAA,YAAY,CAACF,QAAb,IAAyBL,cAAzB;IACA,UAAMO,YAAY,CAAC3J,IAAnB;IACH;;IACD,MAAIqJ,SAAJ,EAAe;IACX,UAAMO,QAAQ,GAAG,IAAI9J,GAAJ,CAAQ0J,uBAAuB,CAACxJ,IAAhC,CAAjB;IACA4J,IAAAA,QAAQ,CAACH,QAAT,IAAqB,OAArB;IACA,UAAMG,QAAQ,CAAC5J,IAAf;IACH;;IACD,MAAIsJ,eAAJ,EAAqB;IACjB,UAAMO,cAAc,GAAGP,eAAe,CAAC;IAAEpJ,MAAAA,GAAG,EAAEL;IAAP,KAAD,CAAtC;;IACA,SAAK,MAAMiK,YAAX,IAA2BD,cAA3B,EAA2C;IACvC,YAAMC,YAAY,CAAC9J,IAAnB;IACH;IACJ;IACJ;;ICzCD;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAM+J,aAAN,SAA4BC,cAA5B,CAAkC;IAC9B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIvJ,EAAAA,WAAW,CAACc,kBAAD,EAAqB0B,OAArB,EAA8B;IACrC,UAAMsF,KAAK,GAAG,CAAC;IAAE1H,MAAAA;IAAF,KAAD,KAAkB;IAC5B,YAAMoJ,eAAe,GAAG1I,kBAAkB,CAAC4G,kBAAnB,EAAxB;;IACA,WAAK,MAAM+B,WAAX,IAA0Bf,qBAAqB,CAACtI,OAAO,CAACX,GAAT,EAAc+C,OAAd,CAA/C,EAAuE;IACnE,cAAMhD,QAAQ,GAAGgK,eAAe,CAAClD,GAAhB,CAAoBmD,WAApB,CAAjB;;IACA,YAAIjK,QAAJ,EAAc;IACV,gBAAMiE,SAAS,GAAG3C,kBAAkB,CAAC8G,uBAAnB,CAA2CpI,QAA3C,CAAlB;IACA,iBAAO;IAAEA,YAAAA,QAAF;IAAYiE,YAAAA;IAAZ,WAAP;IACH;IACJ;;IACD,MAA2C;IACvClC,QAAAA,gBAAM,CAACmI,KAAP,CAAc,sCAAD,GAAyCnG,gCAAc,CAACnD,OAAO,CAACX,GAAT,CAApE;IACH;;IACD;IACH,KAbD;;IAcA,UAAMqI,KAAN,EAAahH,kBAAkB,CAAC0E,QAAhC;IACH;;IAjC6B;;ICrBlC;IACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASmE,QAAT,CAAkBnH,OAAlB,EAA2B;IACvB,QAAM1B,kBAAkB,GAAGqH,6BAA6B,EAAxD;IACA,QAAMyB,aAAa,GAAG,IAAIN,aAAJ,CAAkBxI,kBAAlB,EAAsC0B,OAAtC,CAAtB;IACAqH,EAAAA,8BAAa,CAACD,aAAD,CAAb;IACH;;IC7BD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA,MAAME,iBAAiB,GAAG,YAA1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMC,oBAAoB,GAAG,OAAOC,mBAAP,EAA4BC,eAAe,GAAGH,iBAA9C,KAAoE;IAC7F,QAAMpH,UAAU,GAAG,MAAM7D,IAAI,CAACqI,MAAL,CAAYG,IAAZ,EAAzB;IACA,QAAM6C,kBAAkB,GAAGxH,UAAU,CAACyH,MAAX,CAAmB1H,SAAD,IAAe;IACxD,WAAQA,SAAS,CAAC2H,QAAV,CAAmBH,eAAnB,KACJxH,SAAS,CAAC2H,QAAV,CAAmBvL,IAAI,CAACwL,YAAL,CAAkBC,KAArC,CADI,IAEJ7H,SAAS,KAAKuH,mBAFlB;IAGH,GAJ0B,CAA3B;IAKA,QAAMjD,OAAO,CAACC,GAAR,CAAYkD,kBAAkB,CAACK,GAAnB,CAAwB9H,SAAD,IAAe5D,IAAI,CAACqI,MAAL,CAAYO,MAAZ,CAAmBhF,SAAnB,CAAtC,CAAZ,CAAN;IACA,SAAOyH,kBAAP;IACH,CATD;;IC3BA;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASM,qBAAT,GAAiC;IAC7B;IACA3L,EAAAA,IAAI,CAAC+G,gBAAL,CAAsB,UAAtB,EAAoCpF,KAAD,IAAW;IAC1C,UAAMiC,SAAS,GAAGC,wBAAU,CAACC,eAAX,EAAlB;IACAnC,IAAAA,KAAK,CAACmG,SAAN,CAAgBoD,oBAAoB,CAACtH,SAAD,CAApB,CAAgCgI,IAAhC,CAAsCC,aAAD,IAAmB;IACpE,MAA2C;IACvC,YAAIA,aAAa,CAAC7I,MAAd,GAAuB,CAA3B,EAA8B;IAC1BN,UAAAA,gBAAM,CAACE,GAAP,CAAY,sDAAD,GACN,gBADL,EACsBiJ,aADtB;IAEH;IACJ;IACJ,KAPe,CAAhB;IAQH,GAVD;IAWH;;IC9BD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAAS3C,uBAAT,CAAiCtI,GAAjC,EAAsC;IAClC,QAAMqB,kBAAkB,GAAGqH,6BAA6B,EAAxD;IACA,SAAOrH,kBAAkB,CAACiH,uBAAnB,CAA2CtI,GAA3C,CAAP;IACH;;IC7BD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASyB,iBAAT,CAA2BzB,GAA3B,EAAgC;IAC5B,QAAMqB,kBAAkB,GAAGqH,6BAA6B,EAAxD;IACA,SAAOrH,kBAAkB,CAACI,iBAAnB,CAAqCzB,GAArC,CAAP;IACH;;IC/BD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASoI,aAAT,CAAuBzH,OAAvB,EAAgC;IAC5B,QAAMU,kBAAkB,GAAGqH,6BAA6B,EAAxD;IACA,SAAOrH,kBAAkB,CAAC+G,aAAnB,CAAiCzH,OAAjC,CAAP;IACH;;IC3BD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASqF,QAAT,CAAkBhB,OAAlB,EAA2B;IACvB,QAAM3D,kBAAkB,GAAGqH,6BAA6B,EAAxD;IACArH,EAAAA,kBAAkB,CAAC2E,QAAnB,CAA4BhB,OAA5B;IACH;;IC/BD;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASkG,gBAAT,CAA0BlG,OAA1B,EAAmCjC,OAAnC,EAA4C;IACxCiD,EAAAA,QAAQ,CAAChB,OAAD,CAAR;IACAkF,EAAAA,QAAQ,CAACnH,OAAD,CAAR;IACH;;IC3BD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMoI,sBAAN,CAA6B;IACzB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI5K,EAAAA,WAAW,CAAC;IAAE6K,IAAAA,WAAF;IAAe/J,IAAAA;IAAf,GAAD,EAAuC;IAC9C;IACR;IACA;IACA;IACA;IACQ,SAAKgK,eAAL,GAAuB,MAAM,KAAK7J,mBAAL,CAAyB4G,aAAzB,CAAuC,KAAKkD,YAA5C,CAA7B;;IACA,SAAKA,YAAL,GAAoBF,WAApB;IACA,SAAK5J,mBAAL,GACIH,kBAAkB,IAAIqH,6BAA6B,EADvD;IAEH;;IArBwB;;;;;;;;;;;;;;;;;;;;;"}
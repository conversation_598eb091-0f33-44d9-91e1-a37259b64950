{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\pages\\\\Reports.js\";\nimport React from 'react';\nimport { BarChart3, Download, Filter } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Reports = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Reports & Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Comprehensive reporting with AI-powered insights\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(Download, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Export Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"Reports & Analytics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-6\",\n        children: \"Advanced analytics and reporting with AI-powered insights and recommendations.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2 text-sm text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 Attendance and performance reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 Financial and fee collection analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 Route efficiency and optimization reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 Driver performance analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 Maintenance cost analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 AI-powered insights and recommendations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "BarChart3", "Download", "Filter", "jsxDEV", "_jsxDEV", "Reports", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/pages/Reports.js"], "sourcesContent": ["import React from 'react';\nimport { BarChart3, Download, Filter } from 'lucide-react';\n\nconst Reports = () => {\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Reports & Analytics</h1>\n          <p className=\"text-gray-600\">Comprehensive reporting with AI-powered insights</p>\n        </div>\n        <button className=\"btn btn-primary flex items-center space-x-2\">\n          <Download className=\"h-5 w-5\" />\n          <span>Export Report</span>\n        </button>\n      </div>\n\n      <div className=\"card text-center py-12\">\n        <BarChart3 className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Reports & Analytics</h3>\n        <p className=\"text-gray-600 mb-6\">\n          Advanced analytics and reporting with AI-powered insights and recommendations.\n        </p>\n        <div className=\"space-y-2 text-sm text-gray-500\">\n          <p>✓ Attendance and performance reports</p>\n          <p>✓ Financial and fee collection analytics</p>\n          <p>✓ Route efficiency and optimization reports</p>\n          <p>✓ Driver performance analytics</p>\n          <p>✓ Maintenance cost analysis</p>\n          <p>✓ AI-powered insights and recommendations</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,oBACED,OAAA;IAAKE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBH,OAAA;MAAKE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDH,OAAA;QAAAG,QAAA,gBACEH,OAAA;UAAIE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEP,OAAA;UAAGE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,eACNP,OAAA;QAAQE,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC7DH,OAAA,CAACH,QAAQ;UAACK,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChCP,OAAA;UAAAG,QAAA,EAAM;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCH,OAAA,CAACJ,SAAS;QAACM,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9DP,OAAA;QAAIE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/EP,OAAA;QAAGE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA;QAAKE,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CH,OAAA;UAAAG,QAAA,EAAG;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3CP,OAAA;UAAAG,QAAA,EAAG;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/CP,OAAA;UAAAG,QAAA,EAAG;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClDP,OAAA;UAAAG,QAAA,EAAG;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrCP,OAAA;UAAAG,QAAA,EAAG;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClCP,OAAA;UAAAG,QAAA,EAAG;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GA/BIP,OAAO;AAiCb,eAAeA,OAAO;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
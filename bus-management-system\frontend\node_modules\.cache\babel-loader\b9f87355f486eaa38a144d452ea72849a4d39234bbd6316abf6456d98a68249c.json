{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Magnet = createLucideIcon(\"Magnet\", [[\"path\", {\n  d: \"m6 15-4-4 6.75-6.77a7.79 7.79 0 0 1 11 11L13 22l-4-4 6.39-6.36a2.14 2.14 0 0 0-3-3L6 15\",\n  key: \"1i3lhw\"\n}], [\"path\", {\n  d: \"m5 8 4 4\",\n  key: \"j6kj7e\"\n}], [\"path\", {\n  d: \"m12 15 4 4\",\n  key: \"lnac28\"\n}]]);\nexport { <PERSON>gne<PERSON> as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\magnet.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Magnet\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiAxNS00LTQgNi43NS02Ljc3YTcuNzkgNy43OSAwIDAgMSAxMSAxMUwxMyAyMmwtNC00IDYuMzktNi4zNmEyLjE0IDIuMTQgMCAwIDAtMy0zTDYgMTUiIC8+CiAgPHBhdGggZD0ibTUgOCA0IDQiIC8+CiAgPHBhdGggZD0ibTEyIDE1IDQgNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/magnet\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Magnet = createLucideIcon('Magnet', [\n  [\n    'path',\n    {\n      d: 'm6 15-4-4 6.75-6.77a7.79 7.79 0 0 1 11 11L13 22l-4-4 6.39-6.36a2.14 2.14 0 0 0-3-3L6 15',\n      key: '1i3lhw',\n    },\n  ],\n  ['path', { d: 'm5 8 4 4', key: 'j6kj7e' }],\n  ['path', { d: 'm12 15 4 4', key: 'lnac28' }],\n]);\n\nexport default Magnet;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
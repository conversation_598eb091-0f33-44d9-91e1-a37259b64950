from datetime import datetime
from app import db, ma

class Grade(db.Model):
    """Student grades model."""
    __tablename__ = 'grades'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('students.id'), nullable=False)
    course_id = db.Column(db.Integer, db.<PERSON>('courses.id'), nullable=False)
    faculty_id = db.<PERSON>umn(db.Integer, db.<PERSON>('faculty.id'), nullable=False)
    exam_type = db.Column(db.Enum('Internal', 'Semester', 'Assignment', 'Quiz', 'Project', 
                                 name='exam_types'), nullable=False)
    max_marks = db.Column(db.Integer, nullable=False)
    obtained_marks = db.Column(db.Integer, nullable=False)
    exam_date = db.Column(db.Date)
    remarks = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    @property
    def percentage(self):
        """Calculate percentage."""
        if self.max_marks > 0:
            return round((self.obtained_marks / self.max_marks) * 100, 2)
        return 0
    
    @property
    def grade_letter(self):
        """Get letter grade based on percentage."""
        percentage = self.percentage
        if percentage >= 90:
            return 'A+'
        elif percentage >= 80:
            return 'A'
        elif percentage >= 70:
            return 'B+'
        elif percentage >= 60:
            return 'B'
        elif percentage >= 50:
            return 'C+'
        elif percentage >= 40:
            return 'C'
        else:
            return 'F'
    
    def to_dict(self):
        """Convert grade to dictionary."""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'student_name': self.student.full_name if self.student else None,
            'student_roll': self.student.roll_number if self.student else None,
            'course_id': self.course_id,
            'course_name': self.course.name if self.course else None,
            'course_code': self.course.code if self.course else None,
            'faculty_id': self.faculty_id,
            'faculty_name': self.faculty.full_name if self.faculty else None,
            'exam_type': self.exam_type,
            'max_marks': self.max_marks,
            'obtained_marks': self.obtained_marks,
            'percentage': self.percentage,
            'grade_letter': self.grade_letter,
            'exam_date': self.exam_date.isoformat() if self.exam_date else None,
            'remarks': self.remarks,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @staticmethod
    def calculate_cgpa(student_id):
        """Calculate CGPA for a student."""
        grades = Grade.query.filter_by(student_id=student_id, exam_type='Semester').all()
        if not grades:
            return 0
        
        total_points = 0
        total_credits = 0
        
        for grade in grades:
            grade_points = {
                'A+': 10, 'A': 9, 'B+': 8, 'B': 7, 
                'C+': 6, 'C': 5, 'F': 0
            }.get(grade.grade_letter, 0)
            
            course_credits = grade.course.credits if grade.course else 3
            total_points += grade_points * course_credits
            total_credits += course_credits
        
        return round(total_points / total_credits, 2) if total_credits > 0 else 0
    
    def __repr__(self):
        return f'<Grade {self.student.roll_number} - {self.course.code} - {self.exam_type}>'

class GradeSchema(ma.SQLAlchemyAutoSchema):
    """Grade serialization schema."""
    class Meta:
        model = Grade
        load_instance = True
        include_fk = True

grade_schema = GradeSchema()
grades_schema = GradeSchema(many=True)

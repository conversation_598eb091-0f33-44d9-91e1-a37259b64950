{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\pages\\\\TripManagement.js\";\nimport React from 'react';\nimport { Calendar, Plus, Clock } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TripManagement = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Trip Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Schedule and track daily bus operations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Schedule Trip\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Calendar, {\n        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"Trip Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-6\",\n        children: \"Daily trip scheduling with AI-powered delay predictions.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2 text-sm text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 Daily trip scheduling and management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 Real-time trip tracking and updates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 AI-powered delay prediction using traffic data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 Automatic parent notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u2713 Trip performance analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = TripManagement;\nexport default TripManagement;\nvar _c;\n$RefreshReg$(_c, \"TripManagement\");", "map": {"version": 3, "names": ["React", "Calendar", "Plus", "Clock", "jsxDEV", "_jsxDEV", "TripManagement", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/pages/TripManagement.js"], "sourcesContent": ["import React from 'react';\nimport { Calendar, Plus, Clock } from 'lucide-react';\n\nconst TripManagement = () => {\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Trip Management</h1>\n          <p className=\"text-gray-600\">Schedule and track daily bus operations</p>\n        </div>\n        <button className=\"btn btn-primary flex items-center space-x-2\">\n          <Plus className=\"h-5 w-5\" />\n          <span>Schedule Trip</span>\n        </button>\n      </div>\n\n      <div className=\"card text-center py-12\">\n        <Calendar className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Trip Management</h3>\n        <p className=\"text-gray-600 mb-6\">\n          Daily trip scheduling with AI-powered delay predictions.\n        </p>\n        <div className=\"space-y-2 text-sm text-gray-500\">\n          <p>✓ Daily trip scheduling and management</p>\n          <p>✓ Real-time trip tracking and updates</p>\n          <p>✓ AI-powered delay prediction using traffic data</p>\n          <p>✓ Automatic parent notifications</p>\n          <p>✓ Trip performance analytics</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TripManagement;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,oBACED,OAAA;IAAKE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBH,OAAA;MAAKE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDH,OAAA;QAAAG,QAAA,gBACEH,OAAA;UAAIE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEP,OAAA;UAAGE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eACNP,OAAA;QAAQE,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC7DH,OAAA,CAACH,IAAI;UAACK,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BP,OAAA;UAAAG,QAAA,EAAM;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCH,OAAA,CAACJ,QAAQ;QAACM,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DP,OAAA;QAAIE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3EP,OAAA;QAAGE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA;QAAKE,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CH,OAAA;UAAAG,QAAA,EAAG;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7CP,OAAA;UAAAG,QAAA,EAAG;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5CP,OAAA;UAAAG,QAAA,EAAG;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvDP,OAAA;UAAAG,QAAA,EAAG;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvCP,OAAA;UAAAG,QAAA,EAAG;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GA9BIP,cAAc;AAgCpB,eAAeA,cAAc;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
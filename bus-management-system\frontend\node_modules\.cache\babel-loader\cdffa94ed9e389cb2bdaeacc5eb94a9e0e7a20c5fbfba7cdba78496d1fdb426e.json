{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Bus, Eye, EyeOff } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'student',\n    first_name: '',\n    last_name: '',\n    phone: '',\n    address: '',\n    // Role-specific fields\n    license_number: '',\n    class_name: '',\n    parent_name: '',\n    parent_phone: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const {\n    register\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      toast.error('Passwords do not match');\n      setIsLoading(false);\n      return;\n    }\n    if (formData.password.length < 6) {\n      toast.error('Password must be at least 6 characters long');\n      setIsLoading(false);\n      return;\n    }\n    try {\n      const result = await register(formData);\n      if (result.success) {\n        toast.success('Registration successful! Please login with your credentials.');\n        navigate('/login');\n      }\n    } catch (error) {\n      // Error is handled by the auth context\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const roleFields = {\n    driver: ['license_number'],\n    conductor: ['license_number'],\n    student: ['class_name', 'parent_name', 'parent_phone']\n  };\n  const currentRoleFields = roleFields[formData.role] || [];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-100 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Bus, {\n              className: \"h-12 w-12 text-primary-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"ml-2 text-3xl font-bold text-gray-900\",\n              children: \"BusMS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n          children: \"Create your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600\",\n          children: \"Join the Bus Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"first_name\",\n                className: \"form-label\",\n                children: \"First Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"first_name\",\n                name: \"first_name\",\n                type: \"text\",\n                required: true,\n                className: \"input\",\n                value: formData.first_name,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"last_name\",\n                className: \"form-label\",\n                children: \"Last Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"last_name\",\n                name: \"last_name\",\n                type: \"text\",\n                required: true,\n                className: \"input\",\n                value: formData.last_name,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"username\",\n              className: \"form-label\",\n              children: \"Username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"username\",\n              name: \"username\",\n              type: \"text\",\n              required: true,\n              className: \"input\",\n              value: formData.username,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"form-label\",\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              name: \"email\",\n              type: \"email\",\n              required: true,\n              className: \"input\",\n              value: formData.email,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"role\",\n              className: \"form-label\",\n              children: \"Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"role\",\n              name: \"role\",\n              required: true,\n              className: \"input\",\n              value: formData.role,\n              onChange: handleChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"student\",\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"driver\",\n                children: \"Driver\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"conductor\",\n                children: \"Conductor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"parent\",\n                children: \"Parent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phone\",\n              className: \"form-label\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"phone\",\n              name: \"phone\",\n              type: \"tel\",\n              className: \"input\",\n              value: formData.phone,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), currentRoleFields.includes('license_number') && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"license_number\",\n              className: \"form-label\",\n              children: \"License Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"license_number\",\n              name: \"license_number\",\n              type: \"text\",\n              required: true,\n              className: \"input\",\n              value: formData.license_number,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this), currentRoleFields.includes('class_name') && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"class_name\",\n              className: \"form-label\",\n              children: \"Class/Grade\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"class_name\",\n              name: \"class_name\",\n              type: \"text\",\n              className: \"input\",\n              value: formData.class_name,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this), currentRoleFields.includes('parent_name') && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"parent_name\",\n                className: \"form-label\",\n                children: \"Parent/Guardian Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"parent_name\",\n                name: \"parent_name\",\n                type: \"text\",\n                className: \"input\",\n                value: formData.parent_name,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"parent_phone\",\n                className: \"form-label\",\n                children: \"Parent/Guardian Phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"parent_phone\",\n                name: \"parent_phone\",\n                type: \"tel\",\n                className: \"input\",\n                value: formData.parent_phone,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"address\",\n              className: \"form-label\",\n              children: \"Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"address\",\n              name: \"address\",\n              rows: 3,\n              className: \"input\",\n              value: formData.address,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"form-label\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"password\",\n              name: \"password\",\n              type: showPassword ? 'text' : 'password',\n              required: true,\n              className: \"input pr-10\",\n              value: formData.password,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"absolute inset-y-0 right-0 pr-3 flex items-center mt-6\",\n              onClick: () => setShowPassword(!showPassword),\n              children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                className: \"h-5 w-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                className: \"h-5 w-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              className: \"form-label\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"confirmPassword\",\n              name: \"confirmPassword\",\n              type: showConfirmPassword ? 'text' : 'password',\n              required: true,\n              className: \"input pr-10\",\n              value: formData.confirmPassword,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"absolute inset-y-0 right-0 pr-3 flex items-center mt-6\",\n              onClick: () => setShowConfirmPassword(!showConfirmPassword),\n              children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                className: \"h-5 w-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                className: \"h-5 w-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading,\n            className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"loading-spinner h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this), \"Creating account...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this) : 'Create Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-primary-600 hover:text-primary-500\",\n            children: \"Already have an account? Sign in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"l+pLDNNKMG5xmvwcxMCe4/X+T7Y=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "Bus", "Eye", "Eye<PERSON>ff", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "formData", "setFormData", "username", "email", "password", "confirmPassword", "role", "first_name", "last_name", "phone", "address", "license_number", "class_name", "parent_name", "parent_phone", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "isLoading", "setIsLoading", "register", "navigate", "handleChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "error", "length", "result", "success", "roleFields", "driver", "conductor", "student", "currentRoleFields", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "type", "required", "onChange", "includes", "rows", "onClick", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/pages/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Bus, Eye, EyeOff } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst Register = () => {\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'student',\n    first_name: '',\n    last_name: '',\n    phone: '',\n    address: '',\n    // Role-specific fields\n    license_number: '',\n    class_name: '',\n    parent_name: '',\n    parent_phone: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const { register } = useAuth();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsLoading(true);\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      toast.error('Passwords do not match');\n      setIsLoading(false);\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      toast.error('Password must be at least 6 characters long');\n      setIsLoading(false);\n      return;\n    }\n\n    try {\n      const result = await register(formData);\n      if (result.success) {\n        toast.success('Registration successful! Please login with your credentials.');\n        navigate('/login');\n      }\n    } catch (error) {\n      // Error is handled by the auth context\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const roleFields = {\n    driver: ['license_number'],\n    conductor: ['license_number'],\n    student: ['class_name', 'parent_name', 'parent_phone'],\n  };\n\n  const currentRoleFields = roleFields[formData.role] || [];\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-100 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"flex justify-center\">\n            <div className=\"flex items-center\">\n              <Bus className=\"h-12 w-12 text-primary-600\" />\n              <h1 className=\"ml-2 text-3xl font-bold text-gray-900\">BusMS</h1>\n            </div>\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Join the Bus Management System\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            {/* Basic Information */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label htmlFor=\"first_name\" className=\"form-label\">\n                  First Name\n                </label>\n                <input\n                  id=\"first_name\"\n                  name=\"first_name\"\n                  type=\"text\"\n                  required\n                  className=\"input\"\n                  value={formData.first_name}\n                  onChange={handleChange}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"last_name\" className=\"form-label\">\n                  Last Name\n                </label>\n                <input\n                  id=\"last_name\"\n                  name=\"last_name\"\n                  type=\"text\"\n                  required\n                  className=\"input\"\n                  value={formData.last_name}\n                  onChange={handleChange}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"username\" className=\"form-label\">\n                Username\n              </label>\n              <input\n                id=\"username\"\n                name=\"username\"\n                type=\"text\"\n                required\n                className=\"input\"\n                value={formData.username}\n                onChange={handleChange}\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"form-label\">\n                Email Address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                className=\"input\"\n                value={formData.email}\n                onChange={handleChange}\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"role\" className=\"form-label\">\n                Role\n              </label>\n              <select\n                id=\"role\"\n                name=\"role\"\n                required\n                className=\"input\"\n                value={formData.role}\n                onChange={handleChange}\n              >\n                <option value=\"student\">Student</option>\n                <option value=\"driver\">Driver</option>\n                <option value=\"conductor\">Conductor</option>\n                <option value=\"parent\">Parent</option>\n              </select>\n            </div>\n\n            <div>\n              <label htmlFor=\"phone\" className=\"form-label\">\n                Phone Number\n              </label>\n              <input\n                id=\"phone\"\n                name=\"phone\"\n                type=\"tel\"\n                className=\"input\"\n                value={formData.phone}\n                onChange={handleChange}\n              />\n            </div>\n\n            {/* Role-specific fields */}\n            {currentRoleFields.includes('license_number') && (\n              <div>\n                <label htmlFor=\"license_number\" className=\"form-label\">\n                  License Number\n                </label>\n                <input\n                  id=\"license_number\"\n                  name=\"license_number\"\n                  type=\"text\"\n                  required\n                  className=\"input\"\n                  value={formData.license_number}\n                  onChange={handleChange}\n                />\n              </div>\n            )}\n\n            {currentRoleFields.includes('class_name') && (\n              <div>\n                <label htmlFor=\"class_name\" className=\"form-label\">\n                  Class/Grade\n                </label>\n                <input\n                  id=\"class_name\"\n                  name=\"class_name\"\n                  type=\"text\"\n                  className=\"input\"\n                  value={formData.class_name}\n                  onChange={handleChange}\n                />\n              </div>\n            )}\n\n            {currentRoleFields.includes('parent_name') && (\n              <>\n                <div>\n                  <label htmlFor=\"parent_name\" className=\"form-label\">\n                    Parent/Guardian Name\n                  </label>\n                  <input\n                    id=\"parent_name\"\n                    name=\"parent_name\"\n                    type=\"text\"\n                    className=\"input\"\n                    value={formData.parent_name}\n                    onChange={handleChange}\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"parent_phone\" className=\"form-label\">\n                    Parent/Guardian Phone\n                  </label>\n                  <input\n                    id=\"parent_phone\"\n                    name=\"parent_phone\"\n                    type=\"tel\"\n                    className=\"input\"\n                    value={formData.parent_phone}\n                    onChange={handleChange}\n                  />\n                </div>\n              </>\n            )}\n\n            <div>\n              <label htmlFor=\"address\" className=\"form-label\">\n                Address\n              </label>\n              <textarea\n                id=\"address\"\n                name=\"address\"\n                rows={3}\n                className=\"input\"\n                value={formData.address}\n                onChange={handleChange}\n              />\n            </div>\n\n            {/* Password fields */}\n            <div className=\"relative\">\n              <label htmlFor=\"password\" className=\"form-label\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type={showPassword ? 'text' : 'password'}\n                required\n                className=\"input pr-10\"\n                value={formData.password}\n                onChange={handleChange}\n              />\n              <button\n                type=\"button\"\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center mt-6\"\n                onClick={() => setShowPassword(!showPassword)}\n              >\n                {showPassword ? (\n                  <EyeOff className=\"h-5 w-5 text-gray-400\" />\n                ) : (\n                  <Eye className=\"h-5 w-5 text-gray-400\" />\n                )}\n              </button>\n            </div>\n\n            <div className=\"relative\">\n              <label htmlFor=\"confirmPassword\" className=\"form-label\">\n                Confirm Password\n              </label>\n              <input\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                type={showConfirmPassword ? 'text' : 'password'}\n                required\n                className=\"input pr-10\"\n                value={formData.confirmPassword}\n                onChange={handleChange}\n              />\n              <button\n                type=\"button\"\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center mt-6\"\n                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n              >\n                {showConfirmPassword ? (\n                  <EyeOff className=\"h-5 w-5 text-gray-400\" />\n                ) : (\n                  <Eye className=\"h-5 w-5 text-gray-400\" />\n                )}\n              </button>\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"loading-spinner h-4 w-4 mr-2\"></div>\n                  Creating account...\n                </div>\n              ) : (\n                'Create Account'\n              )}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <Link\n              to=\"/login\"\n              className=\"font-medium text-primary-600 hover:text-primary-500\"\n            >\n              Already have an account? Sign in\n            </Link>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,GAAG,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AAC/C,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IACvCgB,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE,SAAS;IACfC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACX;IACAC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM;IAAEmC;EAAS,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAC9B,MAAMiC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAE9B,MAAMmC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC1B,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBV,YAAY,CAAC,IAAI,CAAC;;IAElB;IACA,IAAIpB,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClDZ,KAAK,CAACsC,KAAK,CAAC,wBAAwB,CAAC;MACrCX,YAAY,CAAC,KAAK,CAAC;MACnB;IACF;IAEA,IAAIpB,QAAQ,CAACI,QAAQ,CAAC4B,MAAM,GAAG,CAAC,EAAE;MAChCvC,KAAK,CAACsC,KAAK,CAAC,6CAA6C,CAAC;MAC1DX,YAAY,CAAC,KAAK,CAAC;MACnB;IACF;IAEA,IAAI;MACF,MAAMa,MAAM,GAAG,MAAMZ,QAAQ,CAACrB,QAAQ,CAAC;MACvC,IAAIiC,MAAM,CAACC,OAAO,EAAE;QAClBzC,KAAK,CAACyC,OAAO,CAAC,8DAA8D,CAAC;QAC7EZ,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACd;IAAA,CACD,SAAS;MACRX,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMe,UAAU,GAAG;IACjBC,MAAM,EAAE,CAAC,gBAAgB,CAAC;IAC1BC,SAAS,EAAE,CAAC,gBAAgB,CAAC;IAC7BC,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,cAAc;EACvD,CAAC;EAED,MAAMC,iBAAiB,GAAGJ,UAAU,CAACnC,QAAQ,CAACM,IAAI,CAAC,IAAI,EAAE;EAEzD,oBACEX,OAAA;IAAK6C,SAAS,EAAC,6HAA6H;IAAAC,QAAA,eAC1I9C,OAAA;MAAK6C,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxC9C,OAAA;QAAA8C,QAAA,gBACE9C,OAAA;UAAK6C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC9C,OAAA;YAAK6C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC9C,OAAA,CAACL,GAAG;cAACkD,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9ClD,OAAA;cAAI6C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA;UAAI6C,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlD,OAAA;UAAG6C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENlD,OAAA;QAAM6C,SAAS,EAAC,gBAAgB;QAACM,QAAQ,EAAEjB,YAAa;QAAAY,QAAA,gBACtD9C,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB9C,OAAA;YAAK6C,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC9C,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAOoD,OAAO,EAAC,YAAY;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAEnD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlD,OAAA;gBACEqD,EAAE,EAAC,YAAY;gBACfvB,IAAI,EAAC,YAAY;gBACjBwB,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACRV,SAAS,EAAC,OAAO;gBACjBd,KAAK,EAAE1B,QAAQ,CAACO,UAAW;gBAC3B4C,QAAQ,EAAE5B;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlD,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAOoD,OAAO,EAAC,WAAW;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlD,OAAA;gBACEqD,EAAE,EAAC,WAAW;gBACdvB,IAAI,EAAC,WAAW;gBAChBwB,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACRV,SAAS,EAAC,OAAO;gBACjBd,KAAK,EAAE1B,QAAQ,CAACQ,SAAU;gBAC1B2C,QAAQ,EAAE5B;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAOoD,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACEqD,EAAE,EAAC,UAAU;cACbvB,IAAI,EAAC,UAAU;cACfwB,IAAI,EAAC,MAAM;cACXC,QAAQ;cACRV,SAAS,EAAC,OAAO;cACjBd,KAAK,EAAE1B,QAAQ,CAACE,QAAS;cACzBiD,QAAQ,EAAE5B;YAAa;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAOoD,OAAO,EAAC,OAAO;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE9C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACEqD,EAAE,EAAC,OAAO;cACVvB,IAAI,EAAC,OAAO;cACZwB,IAAI,EAAC,OAAO;cACZC,QAAQ;cACRV,SAAS,EAAC,OAAO;cACjBd,KAAK,EAAE1B,QAAQ,CAACG,KAAM;cACtBgD,QAAQ,EAAE5B;YAAa;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAOoD,OAAO,EAAC,MAAM;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACEqD,EAAE,EAAC,MAAM;cACTvB,IAAI,EAAC,MAAM;cACXyB,QAAQ;cACRV,SAAS,EAAC,OAAO;cACjBd,KAAK,EAAE1B,QAAQ,CAACM,IAAK;cACrB6C,QAAQ,EAAE5B,YAAa;cAAAkB,QAAA,gBAEvB9C,OAAA;gBAAQ+B,KAAK,EAAC,SAAS;gBAAAe,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxClD,OAAA;gBAAQ+B,KAAK,EAAC,QAAQ;gBAAAe,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtClD,OAAA;gBAAQ+B,KAAK,EAAC,WAAW;gBAAAe,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ClD,OAAA;gBAAQ+B,KAAK,EAAC,QAAQ;gBAAAe,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAOoD,OAAO,EAAC,OAAO;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE9C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACEqD,EAAE,EAAC,OAAO;cACVvB,IAAI,EAAC,OAAO;cACZwB,IAAI,EAAC,KAAK;cACVT,SAAS,EAAC,OAAO;cACjBd,KAAK,EAAE1B,QAAQ,CAACS,KAAM;cACtB0C,QAAQ,EAAE5B;YAAa;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGLN,iBAAiB,CAACa,QAAQ,CAAC,gBAAgB,CAAC,iBAC3CzD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAOoD,OAAO,EAAC,gBAAgB;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACEqD,EAAE,EAAC,gBAAgB;cACnBvB,IAAI,EAAC,gBAAgB;cACrBwB,IAAI,EAAC,MAAM;cACXC,QAAQ;cACRV,SAAS,EAAC,OAAO;cACjBd,KAAK,EAAE1B,QAAQ,CAACW,cAAe;cAC/BwC,QAAQ,EAAE5B;YAAa;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAEAN,iBAAiB,CAACa,QAAQ,CAAC,YAAY,CAAC,iBACvCzD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAOoD,OAAO,EAAC,YAAY;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACEqD,EAAE,EAAC,YAAY;cACfvB,IAAI,EAAC,YAAY;cACjBwB,IAAI,EAAC,MAAM;cACXT,SAAS,EAAC,OAAO;cACjBd,KAAK,EAAE1B,QAAQ,CAACY,UAAW;cAC3BuC,QAAQ,EAAE5B;YAAa;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAEAN,iBAAiB,CAACa,QAAQ,CAAC,aAAa,CAAC,iBACxCzD,OAAA,CAAAE,SAAA;YAAA4C,QAAA,gBACE9C,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAOoD,OAAO,EAAC,aAAa;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlD,OAAA;gBACEqD,EAAE,EAAC,aAAa;gBAChBvB,IAAI,EAAC,aAAa;gBAClBwB,IAAI,EAAC,MAAM;gBACXT,SAAS,EAAC,OAAO;gBACjBd,KAAK,EAAE1B,QAAQ,CAACa,WAAY;gBAC5BsC,QAAQ,EAAE5B;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlD,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAOoD,OAAO,EAAC,cAAc;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlD,OAAA;gBACEqD,EAAE,EAAC,cAAc;gBACjBvB,IAAI,EAAC,cAAc;gBACnBwB,IAAI,EAAC,KAAK;gBACVT,SAAS,EAAC,OAAO;gBACjBd,KAAK,EAAE1B,QAAQ,CAACc,YAAa;gBAC7BqC,QAAQ,EAAE5B;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,eACN,CACH,eAEDlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAOoD,OAAO,EAAC,SAAS;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEhD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACEqD,EAAE,EAAC,SAAS;cACZvB,IAAI,EAAC,SAAS;cACd4B,IAAI,EAAE,CAAE;cACRb,SAAS,EAAC,OAAO;cACjBd,KAAK,EAAE1B,QAAQ,CAACU,OAAQ;cACxByC,QAAQ,EAAE5B;YAAa;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNlD,OAAA;YAAK6C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB9C,OAAA;cAAOoD,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACEqD,EAAE,EAAC,UAAU;cACbvB,IAAI,EAAC,UAAU;cACfwB,IAAI,EAAElC,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCmC,QAAQ;cACRV,SAAS,EAAC,aAAa;cACvBd,KAAK,EAAE1B,QAAQ,CAACI,QAAS;cACzB+C,QAAQ,EAAE5B;YAAa;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACFlD,OAAA;cACEsD,IAAI,EAAC,QAAQ;cACbT,SAAS,EAAC,wDAAwD;cAClEc,OAAO,EAAEA,CAAA,KAAMtC,eAAe,CAAC,CAACD,YAAY,CAAE;cAAA0B,QAAA,EAE7C1B,YAAY,gBACXpB,OAAA,CAACH,MAAM;gBAACgD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE5ClD,OAAA,CAACJ,GAAG;gBAACiD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACzC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlD,OAAA;YAAK6C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB9C,OAAA;cAAOoD,OAAO,EAAC,iBAAiB;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlD,OAAA;cACEqD,EAAE,EAAC,iBAAiB;cACpBvB,IAAI,EAAC,iBAAiB;cACtBwB,IAAI,EAAEhC,mBAAmB,GAAG,MAAM,GAAG,UAAW;cAChDiC,QAAQ;cACRV,SAAS,EAAC,aAAa;cACvBd,KAAK,EAAE1B,QAAQ,CAACK,eAAgB;cAChC8C,QAAQ,EAAE5B;YAAa;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACFlD,OAAA;cACEsD,IAAI,EAAC,QAAQ;cACbT,SAAS,EAAC,wDAAwD;cAClEc,OAAO,EAAEA,CAAA,KAAMpC,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;cAAAwB,QAAA,EAE3DxB,mBAAmB,gBAClBtB,OAAA,CAACH,MAAM;gBAACgD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE5ClD,OAAA,CAACJ,GAAG;gBAACiD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACzC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA;UAAA8C,QAAA,eACE9C,OAAA;YACEsD,IAAI,EAAC,QAAQ;YACbM,QAAQ,EAAEpC,SAAU;YACpBqB,SAAS,EAAC,wRAAwR;YAAAC,QAAA,EAEjStB,SAAS,gBACRxB,OAAA;cAAK6C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9C,OAAA;gBAAK6C,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,uBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlD,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B9C,OAAA,CAACR,IAAI;YACHqE,EAAE,EAAC,QAAQ;YACXhB,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAChE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA3VID,QAAQ;EAAA,QAqBST,OAAO,EACXD,WAAW;AAAA;AAAAqE,EAAA,GAtBxB3D,QAAQ;AA6Vd,eAAeA,QAAQ;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
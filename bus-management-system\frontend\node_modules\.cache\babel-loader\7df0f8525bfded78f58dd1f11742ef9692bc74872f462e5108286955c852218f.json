{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FolderClock = createLucideIcon(\"FolderClock\", [[\"path\", {\n  d: \"M7 20H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H20a2 2 0 0 1 2 2\",\n  key: \"1p0xo9\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"16\",\n  r: \"6\",\n  key: \"qoo3c4\"\n}], [\"path\", {\n  d: \"M16 14v2l1 1\",\n  key: \"xth2jh\"\n}]]);\nexport { FolderClock as default };", "map": {"version": 3, "names": ["FolderClock", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\folder-clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FolderClock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyAyMEg0YTIgMiAwIDAgMS0yLTJWNWMwLTEuMS45LTIgMi0yaDMuOTNhMiAyIDAgMCAxIDEuNjYuOWwuODIgMS4yYTIgMiAwIDAgMCAxLjY2LjlIMjBhMiAyIDAgMCAxIDIgMiIgLz4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSI2IiAvPgogIDxwYXRoIGQ9Ik0xNiAxNHYybDEgMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/folder-clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FolderClock = createLucideIcon('FolderClock', [\n  [\n    'path',\n    {\n      d: 'M7 20H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H20a2 2 0 0 1 2 2',\n      key: '1p0xo9',\n    },\n  ],\n  ['circle', { cx: '16', cy: '16', r: '6', key: 'qoo3c4' }],\n  ['path', { d: 'M16 14v2l1 1', key: 'xth2jh' }],\n]);\n\nexport default FolderClock;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
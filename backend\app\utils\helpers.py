import re
from datetime import datetime, date
from flask import jsonify

def validate_email(email):
    """Validate email format."""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone):
    """Validate phone number format."""
    if not phone:
        return True  # Phone is optional
    pattern = r'^\+?[\d\s\-\(\)]{10,15}$'
    return re.match(pattern, phone) is not None

def validate_password(password):
    """Validate password strength."""
    if len(password) < 6:
        return False, "Password must be at least 6 characters long"
    return True, "Password is valid"

def success_response(message, data=None, status_code=200):
    """Create success response."""
    response = {'success': True, 'message': message}
    if data is not None:
        response['data'] = data
    return jsonify(response), status_code

def error_response(message, status_code=400):
    """Create error response."""
    return jsonify({'success': False, 'error': message}), status_code

def paginate_query(query, page, per_page=10):
    """Paginate a SQLAlchemy query."""
    try:
        page = int(page) if page else 1
        per_page = int(per_page) if per_page else 10
        per_page = min(per_page, 100)  # Limit max items per page
        
        paginated = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        return {
            'items': paginated.items,
            'total': paginated.total,
            'pages': paginated.pages,
            'current_page': paginated.page,
            'per_page': paginated.per_page,
            'has_next': paginated.has_next,
            'has_prev': paginated.has_prev
        }
    except ValueError:
        return None

def generate_roll_number(department_code, year, sequence):
    """Generate student roll number."""
    current_year = datetime.now().year
    year_suffix = str(current_year)[-2:]
    return f"{department_code}{year_suffix}{year:01d}{sequence:03d}"

def generate_employee_id(department_code, sequence):
    """Generate faculty employee ID."""
    current_year = datetime.now().year
    year_suffix = str(current_year)[-2:]
    return f"FAC{department_code}{year_suffix}{sequence:03d}"

def calculate_age(birth_date):
    """Calculate age from birth date."""
    if not birth_date:
        return None
    
    if isinstance(birth_date, str):
        birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
    
    today = date.today()
    return today.year - birth_date.year - (
        (today.month, today.day) < (birth_date.month, birth_date.day)
    )

def format_currency(amount):
    """Format amount as currency."""
    if amount is None:
        return "₹0.00"
    return f"₹{float(amount):,.2f}"

def get_academic_year():
    """Get current academic year."""
    now = datetime.now()
    if now.month >= 6:  # Academic year starts in June
        return f"{now.year}-{now.year + 1}"
    else:
        return f"{now.year - 1}-{now.year}"

def get_current_semester():
    """Get current semester based on date."""
    now = datetime.now()
    if 6 <= now.month <= 11:  # June to November - Odd semester
        return 1
    else:  # December to May - Even semester
        return 2

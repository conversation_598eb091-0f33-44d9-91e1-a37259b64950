/* global __react_refresh_polyfill_url__ */

/**
 * @typedef {Object} UrlAPIs
 * @property {typeof URL} URL
 * @property {typeof URLSearchParams} URLSearchParams
 */

/**
 * Runs a callback with patched the DOM URL APIs.
 * @param {function(UrlAPIs): void} callback The code to run with patched URL globals.
 * @returns {void}
 */
function runWithPatchedUrl(callback) {
  var __originalURL;
  var __originalURLSearchParams;

  // Polyfill the DOM URL and URLSearchParams constructors
  if (__react_refresh_polyfill_url__ || !window.URL) {
    __originalURL = window.URL;
    window.URL = require('core-js-pure/web/url');
  }
  if (__react_refresh_polyfill_url__ || !window.URLSearchParams) {
    __originalURLSearchParams = window.URLSearchParams;
    window.URLSearchParams = require('core-js-pure/web/url-search-params');
  }

  // Pass in URL APIs in case they are needed
  callback({ URL: window.URL, URLSearchParams: window.URLSearchParams });

  // Restore polyfill-ed APIs to their original state
  if (__originalURL) {
    window.URL = __originalURL;
  }
  if (__originalURLSearchParams) {
    window.URLSearchParams = __originalURLSearchParams;
  }
}

module.exports = runWithPatchedUrl;

{"name": "@types/d3-shape", "version": "3.1.7", "description": "TypeScript definitions for d3-shape", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-shape", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "githubUsername": "gust<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "denis<PERSON>", "githubUsername": "denis<PERSON>", "url": "https://github.com/denisname"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}, {"name": "Fil", "githubUsername": "Fil", "url": "https://github.com/Fil"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-shape"}, "scripts": {}, "dependencies": {"@types/d3-path": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "799d9815b06c799db603989ab56ae5a207ba179664cd07cbc5d754a2e542d4ca", "typeScriptVersion": "5.0"}
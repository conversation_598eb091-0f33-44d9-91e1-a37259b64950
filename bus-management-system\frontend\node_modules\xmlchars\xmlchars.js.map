{"version": 3, "file": "xmlchars.js", "sourceRoot": "", "sources": ["../../src/xmlchars.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;AAEH,mCAAqC;AACrC,mCAAqC;AACrC,uCAAyC;AAEzC,sCAAsC;AACtC,OAAO,CAAC,IAAI,CAAC;kEACqD,CAAC,CAAC;AAEpE;;GAEG;AACH,wCAAwC;AACxC,IAAiB,OAAO,CAsHvB;AAtHD,WAAiB,OAAO;IACtB;;OAEG;IACH,IAAiB,GAAG,CAwEnB;IAxED,WAAiB,GAAG;QAClB;;;WAGG;QACH,IAAiB,SAAS,CAKzB;QALD,WAAiB,SAAS;YACX,cAAI,GAAG,GAAG,CAAC,IAAI,CAAC;YAChB,WAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YACV,yBAAe,GAAG,GAAG,CAAC,eAAe,CAAC;YACtC,mBAAS,GAAG,GAAG,CAAC,SAAS,CAAC;QACzC,CAAC,EALgB,SAAS,GAAT,aAAS,KAAT,aAAS,QAKzB;QAED;;;WAGG;QACH,IAAiB,OAAO,CAOvB;QAPD,WAAiB,OAAO;YACT,YAAI,GAAG,GAAG,CAAC,OAAO,CAAC;YACnB,SAAC,GAAG,GAAG,CAAC,IAAI,CAAC;YACb,uBAAe,GAAG,GAAG,CAAC,kBAAkB,CAAC;YACzC,iBAAS,GAAG,GAAG,CAAC,YAAY,CAAC;YAC7B,YAAI,GAAG,GAAG,CAAC,OAAO,CAAC;YACnB,eAAO,GAAG,GAAG,CAAC,UAAU,CAAC;QACxC,CAAC,EAPgB,OAAO,GAAP,WAAO,KAAP,WAAO,QAOvB;QAED;;;;;;WAMG;QACH,IAAiB,KAAK,CAErB;QAFD,WAAiB,KAAK;YACP,OAAC,GAAG,GAAG,CAAC,MAAM,CAAC;QAC9B,CAAC,EAFgB,KAAK,GAAL,SAAK,KAAL,SAAK,QAErB;QAED;;;;;;WAMG;QACU,UAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAEjC;;;;;;WAMG;QACU,OAAG,GAAG,GAAG,CAAC,GAAG,CAAC;QAE3B;;;;;;;WAOG;QACU,mBAAe,GAAG,GAAG,CAAC,eAAe,CAAC;QAEnD;;;;;;WAMG;QACU,cAAU,GAAG,GAAG,CAAC,UAAU,CAAC;IAC3C,CAAC,EAxEgB,GAAG,GAAH,WAAG,KAAH,WAAG,QAwEnB;IAED;;;;OAIG;IACH,IAAiB,GAAG,CAkCnB;IAlCD,WAAiB,GAAG;QAClB;;;WAGG;QACH,IAAiB,SAAS,CAUzB;QAVD,WAAiB,SAAS;YACX,cAAI,GAAG,GAAG,CAAC,IAAI,CAAC;YAChB,WAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YACV,mBAAS,GAAG,GAAG,CAAC,SAAS,CAAC;YAC1B,qBAAW,GAAG,GAAG,CAAC,WAAW,CAAC;YAC9B,wBAAc,GAAG,GAAG,CAAC,cAAc,CAAC;YACpC,eAAK,GAAG,GAAG,CAAC,KAAK,CAAC;YAClB,kBAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;YACxB,gBAAM,GAAG,GAAG,CAAC,MAAM,CAAC;YACpB,mBAAS,GAAG,GAAG,CAAC,SAAS,CAAC;QACzC,CAAC,EAVgB,SAAS,GAAT,aAAS,KAAT,aAAS,QAUzB;QAED;;;WAGG;QACH,IAAiB,OAAO,CAYvB;QAZD,WAAiB,OAAO;YACT,YAAI,GAAG,GAAG,CAAC,OAAO,CAAC;YACnB,SAAC,GAAG,GAAG,CAAC,IAAI,CAAC;YACb,iBAAS,GAAG,GAAG,CAAC,YAAY,CAAC;YAC7B,mBAAW,GAAG,GAAG,CAAC,cAAc,CAAC;YACjC,sBAAc,GAAG,GAAG,CAAC,iBAAiB,CAAC;YACvC,aAAK,GAAG,GAAG,CAAC,QAAQ,CAAC;YACrB,gBAAQ,GAAG,GAAG,CAAC,WAAW,CAAC;YAC3B,cAAM,GAAG,GAAG,CAAC,SAAS,CAAC;YACvB,iBAAS,GAAG,GAAG,CAAC,YAAY,CAAC;YAC7B,YAAI,GAAG,GAAG,CAAC,OAAO,CAAC;YACnB,eAAO,GAAG,GAAG,CAAC,UAAU,CAAC;QACxC,CAAC,EAZgB,OAAO,GAAP,WAAO,KAAP,WAAO,QAYvB;IACH,CAAC,EAlCgB,GAAG,GAAH,WAAG,KAAH,WAAG,QAkCnB;AACH,CAAC,EAtHgB,OAAO,GAAP,eAAO,KAAP,eAAO,QAsHvB;AAED;;GAEG;AACH,wCAAwC;AACxC,IAAiB,SAAS,CA4CzB;AA5CD,WAAiB,SAAS;IAExB;;OAEG;IACH,IAAiB,GAAG,CAsCnB;IAtCD,WAAiB,GAAG;QAClB;;;WAGG;QACH,IAAiB,SAAS,CAGzB;QAHD,WAAiB,SAAS;YACX,4BAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC;YAC9C,sBAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QACjD,CAAC,EAHgB,SAAS,GAAT,aAAS,KAAT,aAAS,QAGzB;QAED;;;WAGG;QACH,IAAiB,OAAO,CAIvB;QAJD,WAAiB,OAAO;YACT,0BAAkB,GAAG,KAAK,CAAC,qBAAqB,CAAC;YACjD,oBAAY,GAAG,KAAK,CAAC,eAAe,CAAC;YACrC,eAAO,GAAG,KAAK,CAAC,UAAU,CAAC;QAC1C,CAAC,EAJgB,OAAO,GAAP,WAAO,KAAP,WAAO,QAIvB;QAED;;;;;;;WAOG;QACU,qBAAiB,GAAG,KAAK,CAAC,iBAAiB,CAAC;QAEzD;;;;;;WAMG;QACU,gBAAY,GAAG,KAAK,CAAC,YAAY,CAAC;IACjD,CAAC,EAtCgB,GAAG,GAAH,aAAG,KAAH,aAAG,QAsCnB;AACH,CAAC,EA5CgB,SAAS,GAAT,iBAAS,KAAT,iBAAS,QA4CzB"}
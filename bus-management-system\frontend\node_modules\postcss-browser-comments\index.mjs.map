{"version": 3, "file": "index.mjs", "sources": ["index.js"], "sourcesContent": ["import browserslist from 'browserslist'\n\nconst plugin = opts => {\n\treturn {\n\t\tpostcssPlugin: 'postcss-browser-comments',\n\t\tOnce(root) {\n\t\t\t// client browserslist\n\t\t\tconst clientBrowserList = browserslist(\n\t\t\t\tObject(opts).browsers || null,\n\t\t\t\t{ path: root.source && root.source.input && root.source.input.file }\n\t\t\t)\n\n\t\t\t// root children references\n\t\t\tconst references = root.nodes.slice(0)\n\n\t\t\t// for each child node of the root children references\n\t\t\tfor (const node of references) {\n\t\t\t\t// if the node is a comment browser comment node\n\t\t\t\tif (isBrowserCommentNode(node)) {\n\t\t\t\t\t// rule following the browser comment\n\t\t\t\t\tconst rule = node.next()\n\n\t\t\t\t\t// browser data\n\t\t\t\t\tconst browserdata = getBrowserData(node.text)\n\n\t\t\t\t\tif (browserdata.isNumbered) {\n\t\t\t\t\t\trule.nodes.filter(isBrowserReferenceCommentNode).map(\n\t\t\t\t\t\t\tcomment => {\n\t\t\t\t\t\t\t\tconst browserdataIndex = parseFloat(comment.text) - 1\n\t\t\t\t\t\t\t\tconst browserslistPart = browserdata.browserslist[browserdataIndex]\n\n\t\t\t\t\t\t\t\t// whether to remove the rule if the comment browserslist does not match the client browserslist\n\t\t\t\t\t\t\t\tconst removeRule = !clientBrowserList.some(\n\t\t\t\t\t\t\t\t\tclientBrowser => browserslist(browserslistPart).some(\n\t\t\t\t\t\t\t\t\t\tcommentBrowser => commentBrowser === clientBrowser\n\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t)\n\n\t\t\t\t\t\t\t\t// conditionally remove the declaration and reference comment\n\t\t\t\t\t\t\t\tif (removeRule) {\n\t\t\t\t\t\t\t\t\tcomment.prev().remove()\n\t\t\t\t\t\t\t\t\tcomment.remove()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t)\n\n\t\t\t\t\t\t// conditionally remove the empty rule and comment\n\t\t\t\t\t\tif (!rule.nodes.length) {\n\t\t\t\t\t\t\trule.remove()\n\t\t\t\t\t\t\tnode.remove()\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// whether to remove the rule if the comment browserslist does not match the client browserslist\n\t\t\t\t\t\tconst removeRule = !clientBrowserList.some(\n\t\t\t\t\t\t\tclientBrowser => browserslist(browserdata.browserslist).some(\n\t\t\t\t\t\t\t\tcommentBrowser => commentBrowser === clientBrowser\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t)\n\n\t\t\t\t\t\t// conditionally remove the rule and comment\n\t\t\t\t\t\tif (removeRule) {\n\t\t\t\t\t\t\trule.remove()\n\t\t\t\t\t\t\tnode.remove()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nplugin.postcss = true\n\nexport default plugin\n\n// returns whether a node is a browser comment\nconst isBrowserCommentNode = node => node.type === 'comment' && isBrowserCommentNodeRegExp.test(node.text) && node.next().type === 'rule'\nconst isBrowserCommentNodeRegExp = /^\\*\\n * /\n\n// returns whether a node is a browser reference comment\nconst isBrowserReferenceCommentNode = node => node.type === 'comment' && isBrowserReferenceCommentNodeRegExp.test(node.text)\nconst isBrowserReferenceCommentNodeRegExp = /^\\d+$/\n\n// returns browser data from comment text\nconst getBrowserData = text => {\n\tconst browserDataNumbered = text.match(browserDataMutliRegExp)\n\tconst isNumbered = Boolean(browserDataNumbered)\n\n\treturn {\n\t\tbrowserslist: isNumbered\n\t\t\t? browserDataNumbered.map(\n\t\t\t\tbrowserslistPart => getBrowsersList(browserslistPart.replace(browserDataNumberedNewlineRegExp, '$1'))\n\t\t\t)\n\t\t: getBrowsersList(\n\t\t\ttext.replace(browserDataNewlineRegExp, '')\n\t\t),\n\t\tisNumbered\n\t}\n}\nconst browserDataMutliRegExp = /(\\n \\* \\d+\\. (?:[^\\n]+|\\n \\* {4,})+)/g\nconst browserDataNewlineRegExp = /^\\*\\n \\* ?|\\n \\*/g\nconst browserDataNumberedNewlineRegExp = /\\n \\* (?:( )\\s*)?/g\n\n// returns a browserlist from comment text\nconst getBrowsersList = text => text.split(getBrowsersListInSplitRegExp).slice(1).map(\n\tpart => part.split(getBrowsersListAndSplitRegExp).filter(part2 => part2)\n).reduce(\n\t(acc, val) => acc.concat(val), [])\n.map(\n\tpart => part.replace(\n\t\tgetBrowsersListQueryRegExp,\n\t\t($0, browser, query) => browser === 'all'\n\t\t\t? '> 0%'\n\t\t: `${browser}${\n\t\t\tquery\n\t\t\t\t? /^((?:\\d*\\.)?\\d+)-$/.test(query)\n\t\t\t\t\t? ` <= ${query.slice(0, -1)}`\n\t\t\t\t: ` ${query}`\n\t\t\t: ' > 0'\n\t\t}`\n\t).toLowerCase()\n)\nconst getBrowsersListInSplitRegExp = /\\s+in\\s+/\nconst getBrowsersListAndSplitRegExp = /(?: and|, and|,)/\nconst getBrowsersListQueryRegExp = /^\\s*(\\w+)(?: ((?:(?:\\d*\\.)?\\d+-)?(?:\\d*\\.)?\\d+[+-]?))?.*$/\n"], "names": ["plugin", "opts", "postcssPlugin", "Once", "root", "clientBrowserList", "browserslist", "Object", "browsers", "path", "source", "input", "file", "references", "nodes", "slice", "node", "isBrowserCommentNode", "rule", "next", "browserdata", "getBrowserData", "text", "isNumbered", "filter", "isBrowserReferenceCommentNode", "map", "comment", "browserdataIndex", "parseFloat", "browserslistPart", "removeRule", "some", "clientBrowser", "<PERSON><PERSON><PERSON><PERSON>", "prev", "remove", "length", "postcss", "type", "isBrowserCommentNodeRegExp", "test", "isBrowserReferenceCommentNodeRegExp", "browserDataNumbered", "match", "browserDataMutliRegExp", "Boolean", "getBrowsersList", "replace", "browserDataNumberedNewlineRegExp", "browserDataNewlineRegExp", "split", "getBrowsersListInSplitRegExp", "part", "getBrowsersListAndSplitRegExp", "part2", "reduce", "acc", "val", "concat", "getBrowsersListQueryRegExp", "$0", "browser", "query", "toLowerCase"], "mappings": ";;MAEMA,MAAM,GAAGC,IAAI,IAAI;AACtB,SAAO;AACNC,IAAAA,aAAa,EAAE,0BADT;;AAENC,IAAAA,IAAI,CAACC,IAAD,EAAO;AACV;AACA,YAAMC,iBAAiB,GAAGC,YAAY,CACrCC,MAAM,CAACN,IAAD,CAAN,CAAaO,QAAb,IAAyB,IADY,EAErC;AAAEC,QAAAA,IAAI,EAAEL,IAAI,CAACM,MAAL,IAAeN,IAAI,CAACM,MAAL,CAAYC,KAA3B,IAAoCP,IAAI,CAACM,MAAL,CAAYC,KAAZ,CAAkBC;AAA9D,OAFqC,CAAtC,CAFU;;AAQV,YAAMC,UAAU,GAAGT,IAAI,CAACU,KAAL,CAAWC,KAAX,CAAiB,CAAjB,CAAnB,CARU;;AAWV,WAAK,MAAMC,IAAX,IAAmBH,UAAnB,EAA+B;AAC9B;AACA,YAAII,oBAAoB,CAACD,IAAD,CAAxB,EAAgC;AAC/B;AACA,gBAAME,IAAI,GAAGF,IAAI,CAACG,IAAL,EAAb,CAF+B;;AAK/B,gBAAMC,WAAW,GAAGC,cAAc,CAACL,IAAI,CAACM,IAAN,CAAlC;;AAEA,cAAIF,WAAW,CAACG,UAAhB,EAA4B;AAC3BL,YAAAA,IAAI,CAACJ,KAAL,CAAWU,MAAX,CAAkBC,6BAAlB,EAAiDC,GAAjD,CACCC,OAAO,IAAI;AACV,oBAAMC,gBAAgB,GAAGC,UAAU,CAACF,OAAO,CAACL,IAAT,CAAV,GAA2B,CAApD;AACA,oBAAMQ,gBAAgB,GAAGV,WAAW,CAACd,YAAZ,CAAyBsB,gBAAzB,CAAzB,CAFU;;AAKV,oBAAMG,UAAU,GAAG,CAAC1B,iBAAiB,CAAC2B,IAAlB,CACnBC,aAAa,IAAI3B,YAAY,CAACwB,gBAAD,CAAZ,CAA+BE,IAA/B,CAChBE,cAAc,IAAIA,cAAc,KAAKD,aADrB,CADE,CAApB,CALU;;AAYV,kBAAIF,UAAJ,EAAgB;AACfJ,gBAAAA,OAAO,CAACQ,IAAR,GAAeC,MAAf;AACAT,gBAAAA,OAAO,CAACS,MAAR;AACA;AACD,aAjBF,EAD2B;;AAsB3B,gBAAI,CAAClB,IAAI,CAACJ,KAAL,CAAWuB,MAAhB,EAAwB;AACvBnB,cAAAA,IAAI,CAACkB,MAAL;AACApB,cAAAA,IAAI,CAACoB,MAAL;AACA;AACD,WA1BD,MA0BO;AACN;AACA,kBAAML,UAAU,GAAG,CAAC1B,iBAAiB,CAAC2B,IAAlB,CACnBC,aAAa,IAAI3B,YAAY,CAACc,WAAW,CAACd,YAAb,CAAZ,CAAuC0B,IAAvC,CAChBE,cAAc,IAAIA,cAAc,KAAKD,aADrB,CADE,CAApB,CAFM;;AASN,gBAAIF,UAAJ,EAAgB;AACfb,cAAAA,IAAI,CAACkB,MAAL;AACApB,cAAAA,IAAI,CAACoB,MAAL;AACA;AACD;AACD;AACD;AACD;;AAhEK,GAAP;AAkEA;;AAEDpC,MAAM,CAACsC,OAAP,GAAiB,IAAjB;;AAKA,MAAMrB,oBAAoB,GAAGD,IAAI,IAAIA,IAAI,CAACuB,IAAL,KAAc,SAAd,IAA2BC,0BAA0B,CAACC,IAA3B,CAAgCzB,IAAI,CAACM,IAArC,CAA3B,IAAyEN,IAAI,CAACG,IAAL,GAAYoB,IAAZ,KAAqB,MAAnI;;AACA,MAAMC,0BAA0B,GAAG,UAAnC;;AAGA,MAAMf,6BAA6B,GAAGT,IAAI,IAAIA,IAAI,CAACuB,IAAL,KAAc,SAAd,IAA2BG,mCAAmC,CAACD,IAApC,CAAyCzB,IAAI,CAACM,IAA9C,CAAzE;;AACA,MAAMoB,mCAAmC,GAAG,OAA5C;;AAGA,MAAMrB,cAAc,GAAGC,IAAI,IAAI;AAC9B,QAAMqB,mBAAmB,GAAGrB,IAAI,CAACsB,KAAL,CAAWC,sBAAX,CAA5B;AACA,QAAMtB,UAAU,GAAGuB,OAAO,CAACH,mBAAD,CAA1B;AAEA,SAAO;AACNrC,IAAAA,YAAY,EAAEiB,UAAU,GACrBoB,mBAAmB,CAACjB,GAApB,CACDI,gBAAgB,IAAIiB,eAAe,CAACjB,gBAAgB,CAACkB,OAAjB,CAAyBC,gCAAzB,EAA2D,IAA3D,CAAD,CADlC,CADqB,GAItBF,eAAe,CAChBzB,IAAI,CAAC0B,OAAL,CAAaE,wBAAb,EAAuC,EAAvC,CADgB,CALX;AAQN3B,IAAAA;AARM,GAAP;AAUA,CAdD;;AAeA,MAAMsB,sBAAsB,GAAG,uCAA/B;AACA,MAAMK,wBAAwB,GAAG,mBAAjC;AACA,MAAMD,gCAAgC,GAAG,oBAAzC;;AAGA,MAAMF,eAAe,GAAGzB,IAAI,IAAIA,IAAI,CAAC6B,KAAL,CAAWC,4BAAX,EAAyCrC,KAAzC,CAA+C,CAA/C,EAAkDW,GAAlD,CAC/B2B,IAAI,IAAIA,IAAI,CAACF,KAAL,CAAWG,6BAAX,EAA0C9B,MAA1C,CAAiD+B,KAAK,IAAIA,KAA1D,CADuB,EAE9BC,MAF8B,CAG/B,CAACC,GAAD,EAAMC,GAAN,KAAcD,GAAG,CAACE,MAAJ,CAAWD,GAAX,CAHiB,EAGA,EAHA,EAI/BhC,GAJ+B,CAK/B2B,IAAI,IAAIA,IAAI,CAACL,OAAL,CACPY,0BADO,EAEP,CAACC,EAAD,EAAKC,OAAL,EAAcC,KAAd,KAAwBD,OAAO,KAAK,KAAZ,GACrB,MADqB,GAErB,GAAEA,OAAQ,GACZC,KAAK,GACF,qBAAqBtB,IAArB,CAA0BsB,KAA1B,IACE,OAAMA,KAAK,CAAChD,KAAN,CAAY,CAAZ,EAAe,CAAC,CAAhB,CAAmB,EAD3B,GAEC,IAAGgD,KAAM,EAHR,GAIH,MACF,EAVM,EAWNC,WAXM,EALuB,CAAhC;;AAkBA,MAAMZ,4BAA4B,GAAG,UAArC;AACA,MAAME,6BAA6B,GAAG,kBAAtC;AACA,MAAMM,0BAA0B,GAAG,2DAAnC;;;;"}
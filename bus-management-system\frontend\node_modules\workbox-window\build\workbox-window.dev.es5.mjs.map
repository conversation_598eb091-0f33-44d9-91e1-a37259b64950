{"version": 3, "file": "workbox-window.dev.es5.mjs", "sources": ["../_version.js", "../messageSW.js", "../../workbox-core/_version.js", "../../workbox-core/_private/Deferred.js", "../../workbox-core/_private/dontWaitFor.js", "../../workbox-core/_private/logger.js", "../utils/WorkboxEventTarget.js", "../utils/urlsMatch.js", "../utils/WorkboxEvent.js", "../Workbox.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:window:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Sends a data object to a service worker via `postMessage` and resolves with\n * a response (if any).\n *\n * A response can be set in a message handler in the service worker by\n * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n * returned by `messageSW()`. If no response is set, the promise will not\n * resolve.\n *\n * @param {ServiceWorker} sw The service worker to send the message to.\n * @param {Object} data An object to send to the service worker.\n * @return {Promise<Object|undefined>}\n * @memberof workbox-window\n */\n// Better not change type of data.\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction messageSW(sw, data) {\n    return new Promise((resolve) => {\n        const messageChannel = new MessageChannel();\n        messageChannel.port1.onmessage = (event) => {\n            resolve(event.data);\n        };\n        sw.postMessage(data, [messageChannel.port2]);\n    });\n}\nexport { messageSW };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst logger = (process.env.NODE_ENV === 'production'\n    ? null\n    : (() => {\n        // Don't overwrite this value if it's already set.\n        // See https://github.com/GoogleChrome/workbox/pull/2284#issuecomment-560470923\n        if (!('__WB_DISABLE_DEV_LOGS' in globalThis)) {\n            self.__WB_DISABLE_DEV_LOGS = false;\n        }\n        let inGroup = false;\n        const methodToColorMap = {\n            debug: `#7f8c8d`,\n            log: `#2ecc71`,\n            warn: `#f39c12`,\n            error: `#c0392b`,\n            groupCollapsed: `#3498db`,\n            groupEnd: null, // No colored prefix on groupEnd\n        };\n        const print = function (method, args) {\n            if (self.__WB_DISABLE_DEV_LOGS) {\n                return;\n            }\n            if (method === 'groupCollapsed') {\n                // Safari doesn't print all console.groupCollapsed() arguments:\n                // https://bugs.webkit.org/show_bug.cgi?id=182754\n                if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n                    console[method](...args);\n                    return;\n                }\n            }\n            const styles = [\n                `background: ${methodToColorMap[method]}`,\n                `border-radius: 0.5em`,\n                `color: white`,\n                `font-weight: bold`,\n                `padding: 2px 0.5em`,\n            ];\n            // When in a group, the workbox prefix is not displayed.\n            const logPrefix = inGroup ? [] : ['%cworkbox', styles.join(';')];\n            console[method](...logPrefix, ...args);\n            if (method === 'groupCollapsed') {\n                inGroup = true;\n            }\n            if (method === 'groupEnd') {\n                inGroup = false;\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const api = {};\n        const loggerMethods = Object.keys(methodToColorMap);\n        for (const key of loggerMethods) {\n            const method = key;\n            api[method] = (...args) => {\n                print(method, args);\n            };\n        }\n        return api;\n    })());\nexport { logger };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\n/**\n * A minimal `EventTarget` shim.\n * This is necessary because not all browsers support constructable\n * `EventTarget`, so using a real `EventTarget` will error.\n * @private\n */\nexport class WorkboxEventTarget {\n    constructor() {\n        this._eventListenerRegistry = new Map();\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    addEventListener(type, listener) {\n        const foo = this._getEventListenersByType(type);\n        foo.add(listener);\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    removeEventListener(type, listener) {\n        this._getEventListenersByType(type).delete(listener);\n    }\n    /**\n     * @param {Object} event\n     * @private\n     */\n    dispatchEvent(event) {\n        event.target = this;\n        const listeners = this._getEventListenersByType(event.type);\n        for (const listener of listeners) {\n            listener(event);\n        }\n    }\n    /**\n     * Returns a Set of listeners associated with the passed event type.\n     * If no handlers have been registered, an empty Set is returned.\n     *\n     * @param {string} type The event type.\n     * @return {Set<ListenerCallback>} An array of handler functions.\n     * @private\n     */\n    _getEventListenersByType(type) {\n        if (!this._eventListenerRegistry.has(type)) {\n            this._eventListenerRegistry.set(type, new Set());\n        }\n        return this._eventListenerRegistry.get(type);\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns true if two URLs have the same `.href` property. The URLS can be\n * relative, and if they are the current location href is used to resolve URLs.\n *\n * @private\n * @param {string} url1\n * @param {string} url2\n * @return {boolean}\n */\nexport function urlsMatch(url1, url2) {\n    const { href } = location;\n    return new URL(url1, href).href === new URL(url2, href).href;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A minimal `Event` subclass shim.\n * This doesn't *actually* subclass `Event` because not all browsers support\n * constructable `EventTarget`, and using a real `Event` will error.\n * @private\n */\nexport class WorkboxEvent {\n    constructor(type, props) {\n        this.type = type;\n        Object.assign(this, props);\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { messageSW } from './messageSW.js';\nimport { WorkboxEventTarget } from './utils/WorkboxEventTarget.js';\nimport { urlsMatch } from './utils/urlsMatch.js';\nimport { WorkboxEvent } from './utils/WorkboxEvent.js';\nimport './_version.js';\n// The time a SW must be in the waiting phase before we can conclude\n// `skipWaiting()` wasn't called. This 200 amount wasn't scientifically\n// chosen, but it seems to avoid false positives in my testing.\nconst WAITING_TIMEOUT_DURATION = 200;\n// The amount of time after a registration that we can reasonably conclude\n// that the registration didn't trigger an update.\nconst REGISTRATION_TIMEOUT_DURATION = 60000;\n// The de facto standard message that a service worker should be listening for\n// to trigger a call to skipWaiting().\nconst SKIP_WAITING_MESSAGE = { type: 'SKIP_WAITING' };\n/**\n * A class to aid in handling service worker registration, updates, and\n * reacting to service worker lifecycle events.\n *\n * @fires {@link workbox-window.Workbox#message}\n * @fires {@link workbox-window.Workbox#installed}\n * @fires {@link workbox-window.Workbox#waiting}\n * @fires {@link workbox-window.Workbox#controlling}\n * @fires {@link workbox-window.Workbox#activated}\n * @fires {@link workbox-window.Workbox#redundant}\n * @memberof workbox-window\n */\nclass Workbox extends WorkboxEventTarget {\n    /**\n     * Creates a new Workbox instance with a script URL and service worker\n     * options. The script URL and options are the same as those used when\n     * calling [navigator.serviceWorker.register(scriptURL, options)](https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register).\n     *\n     * @param {string|TrustedScriptURL} scriptURL The service worker script\n     *     associated with this instance. Using a\n     *     [`TrustedScriptURL`](https://web.dev/trusted-types/) is supported.\n     * @param {Object} [registerOptions] The service worker options associated\n     *     with this instance.\n     */\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    constructor(scriptURL, registerOptions = {}) {\n        super();\n        this._registerOptions = {};\n        this._updateFoundCount = 0;\n        // Deferreds we can resolve later.\n        this._swDeferred = new Deferred();\n        this._activeDeferred = new Deferred();\n        this._controllingDeferred = new Deferred();\n        this._registrationTime = 0;\n        this._ownSWs = new Set();\n        /**\n         * @private\n         */\n        this._onUpdateFound = () => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const installingSW = registration.installing;\n            // If the script URL passed to `navigator.serviceWorker.register()` is\n            // different from the current controlling SW's script URL, we know any\n            // successful registration calls will trigger an `updatefound` event.\n            // But if the registered script URL is the same as the current controlling\n            // SW's script URL, we'll only get an `updatefound` event if the file\n            // changed since it was last registered. This can be a problem if the user\n            // opens up the same page in a different tab, and that page registers\n            // a SW that triggers an update. It's a problem because this page has no\n            // good way of knowing whether the `updatefound` event came from the SW\n            // script it registered or from a registration attempt made by a newer\n            // version of the page running in another tab.\n            // To minimize the possibility of a false positive, we use the logic here:\n            const updateLikelyTriggeredExternally = \n            // Since we enforce only calling `register()` once, and since we don't\n            // add the `updatefound` event listener until the `register()` call, if\n            // `_updateFoundCount` is > 0 then it means this method has already\n            // been called, thus this SW must be external\n            this._updateFoundCount > 0 ||\n                // If the script URL of the installing SW is different from this\n                // instance's script URL, we know it's definitely not from our\n                // registration.\n                !urlsMatch(installingSW.scriptURL, this._scriptURL.toString()) ||\n                // If all of the above are false, then we use a time-based heuristic:\n                // Any `updatefound` event that occurs long after our registration is\n                // assumed to be external.\n                performance.now() > this._registrationTime + REGISTRATION_TIMEOUT_DURATION\n                ? // If any of the above are not true, we assume the update was\n                    // triggered by this instance.\n                    true\n                : false;\n            if (updateLikelyTriggeredExternally) {\n                this._externalSW = installingSW;\n                registration.removeEventListener('updatefound', this._onUpdateFound);\n            }\n            else {\n                // If the update was not triggered externally we know the installing\n                // SW is the one we registered, so we set it.\n                this._sw = installingSW;\n                this._ownSWs.add(installingSW);\n                this._swDeferred.resolve(installingSW);\n                // The `installing` state isn't something we have a dedicated\n                // callback for, but we do log messages for it in development.\n                if (process.env.NODE_ENV !== 'production') {\n                    if (navigator.serviceWorker.controller) {\n                        logger.log('Updated service worker found. Installing now...');\n                    }\n                    else {\n                        logger.log('Service worker is installing...');\n                    }\n                }\n            }\n            // Increment the `updatefound` count, so future invocations of this\n            // method can be sure they were triggered externally.\n            ++this._updateFoundCount;\n            // Add a `statechange` listener regardless of whether this update was\n            // triggered externally, since we have callbacks for both.\n            installingSW.addEventListener('statechange', this._onStateChange);\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onStateChange = (originalEvent) => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const sw = originalEvent.target;\n            const { state } = sw;\n            const isExternal = sw === this._externalSW;\n            const eventProps = {\n                sw,\n                isExternal,\n                originalEvent,\n            };\n            if (!isExternal && this._isUpdate) {\n                eventProps.isUpdate = true;\n            }\n            this.dispatchEvent(new WorkboxEvent(state, eventProps));\n            if (state === 'installed') {\n                // This timeout is used to ignore cases where the service worker calls\n                // `skipWaiting()` in the install event, thus moving it directly in the\n                // activating state. (Since all service workers *must* go through the\n                // waiting phase, the only way to detect `skipWaiting()` called in the\n                // install event is to observe that the time spent in the waiting phase\n                // is very short.)\n                // NOTE: we don't need separate timeouts for the own and external SWs\n                // since they can't go through these phases at the same time.\n                this._waitingTimeout = self.setTimeout(() => {\n                    // Ensure the SW is still waiting (it may now be redundant).\n                    if (state === 'installed' && registration.waiting === sw) {\n                        this.dispatchEvent(new WorkboxEvent('waiting', eventProps));\n                        if (process.env.NODE_ENV !== 'production') {\n                            if (isExternal) {\n                                logger.warn('An external service worker has installed but is ' +\n                                    'waiting for this client to close before activating...');\n                            }\n                            else {\n                                logger.warn('The service worker has installed but is waiting ' +\n                                    'for existing clients to close before activating...');\n                            }\n                        }\n                    }\n                }, WAITING_TIMEOUT_DURATION);\n            }\n            else if (state === 'activating') {\n                clearTimeout(this._waitingTimeout);\n                if (!isExternal) {\n                    this._activeDeferred.resolve(sw);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                switch (state) {\n                    case 'installed':\n                        if (isExternal) {\n                            logger.warn('An external service worker has installed. ' +\n                                'You may want to suggest users reload this page.');\n                        }\n                        else {\n                            logger.log('Registered service worker installed.');\n                        }\n                        break;\n                    case 'activated':\n                        if (isExternal) {\n                            logger.warn('An external service worker has activated.');\n                        }\n                        else {\n                            logger.log('Registered service worker activated.');\n                            if (sw !== navigator.serviceWorker.controller) {\n                                logger.warn('The registered service worker is active but ' +\n                                    'not yet controlling the page. Reload or run ' +\n                                    '`clients.claim()` in the service worker.');\n                            }\n                        }\n                        break;\n                    case 'redundant':\n                        if (sw === this._compatibleControllingSW) {\n                            logger.log('Previously controlling service worker now redundant!');\n                        }\n                        else if (!isExternal) {\n                            logger.log('Registered service worker now redundant!');\n                        }\n                        break;\n                }\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onControllerChange = (originalEvent) => {\n            const sw = this._sw;\n            const isExternal = sw !== navigator.serviceWorker.controller;\n            // Unconditionally dispatch the controlling event, with isExternal set\n            // to distinguish between controller changes due to the initial registration\n            // vs. an update-check or other tab's registration.\n            // See https://github.com/GoogleChrome/workbox/issues/2786\n            this.dispatchEvent(new WorkboxEvent('controlling', {\n                isExternal,\n                originalEvent,\n                sw,\n                isUpdate: this._isUpdate,\n            }));\n            if (!isExternal) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log('Registered service worker now controlling this page.');\n                }\n                this._controllingDeferred.resolve(sw);\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onMessage = async (originalEvent) => {\n            // Can't change type 'any' of data.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const { data, ports, source } = originalEvent;\n            // Wait until there's an \"own\" service worker. This is used to buffer\n            // `message` events that may be received prior to calling `register()`.\n            await this.getSW();\n            // If the service worker that sent the message is in the list of own\n            // service workers for this instance, dispatch a `message` event.\n            // NOTE: we check for all previously owned service workers rather than\n            // just the current one because some messages (e.g. cache updates) use\n            // a timeout when sent and may be delayed long enough for a service worker\n            // update to be found.\n            if (this._ownSWs.has(source)) {\n                this.dispatchEvent(new WorkboxEvent('message', {\n                    // Can't change type 'any' of data.\n                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                    data,\n                    originalEvent,\n                    ports,\n                    sw: source,\n                }));\n            }\n        };\n        this._scriptURL = scriptURL;\n        this._registerOptions = registerOptions;\n        // Add a message listener immediately since messages received during\n        // page load are buffered only until the DOMContentLoaded event:\n        // https://github.com/GoogleChrome/workbox/issues/2202\n        navigator.serviceWorker.addEventListener('message', this._onMessage);\n    }\n    /**\n     * Registers a service worker for this instances script URL and service\n     * worker options. By default this method delays registration until after\n     * the window has loaded.\n     *\n     * @param {Object} [options]\n     * @param {Function} [options.immediate=false] Setting this to true will\n     *     register the service worker immediately, even if the window has\n     *     not loaded (not recommended).\n     */\n    async register({ immediate = false } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._registrationTime) {\n                logger.error('Cannot re-register a Workbox instance after it has ' +\n                    'been registered. Create a new instance instead.');\n                return;\n            }\n        }\n        if (!immediate && document.readyState !== 'complete') {\n            await new Promise((res) => window.addEventListener('load', res));\n        }\n        // Set this flag to true if any service worker was controlling the page\n        // at registration time.\n        this._isUpdate = Boolean(navigator.serviceWorker.controller);\n        // Before registering, attempt to determine if a SW is already controlling\n        // the page, and if that SW script (and version, if specified) matches this\n        // instance's script.\n        this._compatibleControllingSW = this._getControllingSWIfCompatible();\n        this._registration = await this._registerScript();\n        // If we have a compatible controller, store the controller as the \"own\"\n        // SW, resolve active/controlling deferreds and add necessary listeners.\n        if (this._compatibleControllingSW) {\n            this._sw = this._compatibleControllingSW;\n            this._activeDeferred.resolve(this._compatibleControllingSW);\n            this._controllingDeferred.resolve(this._compatibleControllingSW);\n            this._compatibleControllingSW.addEventListener('statechange', this._onStateChange, { once: true });\n        }\n        // If there's a waiting service worker with a matching URL before the\n        // `updatefound` event fires, it likely means that this site is open\n        // in another tab, or the user refreshed the page (and thus the previous\n        // page wasn't fully unloaded before this page started loading).\n        // https://developers.google.com/web/fundamentals/primers/service-workers/lifecycle#waiting\n        const waitingSW = this._registration.waiting;\n        if (waitingSW &&\n            urlsMatch(waitingSW.scriptURL, this._scriptURL.toString())) {\n            // Store the waiting SW as the \"own\" Sw, even if it means overwriting\n            // a compatible controller.\n            this._sw = waitingSW;\n            // Run this in the next microtask, so any code that adds an event\n            // listener after awaiting `register()` will get this event.\n            dontWaitFor(Promise.resolve().then(() => {\n                this.dispatchEvent(new WorkboxEvent('waiting', {\n                    sw: waitingSW,\n                    wasWaitingBeforeRegister: true,\n                }));\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.warn('A service worker was already waiting to activate ' +\n                        'before this script was registered...');\n                }\n            }));\n        }\n        // If an \"own\" SW is already set, resolve the deferred.\n        if (this._sw) {\n            this._swDeferred.resolve(this._sw);\n            this._ownSWs.add(this._sw);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log('Successfully registered service worker.', this._scriptURL.toString());\n            if (navigator.serviceWorker.controller) {\n                if (this._compatibleControllingSW) {\n                    logger.debug('A service worker with the same script URL ' +\n                        'is already controlling this page.');\n                }\n                else {\n                    logger.debug('A service worker with a different script URL is ' +\n                        'currently controlling the page. The browser is now fetching ' +\n                        'the new script now...');\n                }\n            }\n            const currentPageIsOutOfScope = () => {\n                const scopeURL = new URL(this._registerOptions.scope || this._scriptURL.toString(), document.baseURI);\n                const scopeURLBasePath = new URL('./', scopeURL.href).pathname;\n                return !location.pathname.startsWith(scopeURLBasePath);\n            };\n            if (currentPageIsOutOfScope()) {\n                logger.warn('The current page is not in scope for the registered ' +\n                    'service worker. Was this a mistake?');\n            }\n        }\n        this._registration.addEventListener('updatefound', this._onUpdateFound);\n        navigator.serviceWorker.addEventListener('controllerchange', this._onControllerChange);\n        return this._registration;\n    }\n    /**\n     * Checks for updates of the registered service worker.\n     */\n    async update() {\n        if (!this._registration) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error('Cannot update a Workbox instance without ' +\n                    'being registered. Register the Workbox instance first.');\n            }\n            return;\n        }\n        // Try to update registration\n        await this._registration.update();\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is active. If a service worker was already controlling at registration\n     * time then it will resolve to that if the script URLs (and optionally\n     * script versions) match, otherwise it will wait until an update is found\n     * and activates.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get active() {\n        return this._activeDeferred.promise;\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is controlling the page. If a service worker was already controlling at\n     * registration time then it will resolve to that if the script URLs (and\n     * optionally script versions) match, otherwise it will wait until an update\n     * is found and starts controlling the page.\n     * Note: the first time a service worker is installed it will active but\n     * not start controlling the page unless `clients.claim()` is called in the\n     * service worker.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get controlling() {\n        return this._controllingDeferred.promise;\n    }\n    /**\n     * Resolves with a reference to a service worker that matches the script URL\n     * of this instance, as soon as it's available.\n     *\n     * If, at registration time, there's already an active or waiting service\n     * worker with a matching script URL, it will be used (with the waiting\n     * service worker taking precedence over the active service worker if both\n     * match, since the waiting service worker would have been registered more\n     * recently).\n     * If there's no matching active or waiting service worker at registration\n     * time then the promise will not resolve until an update is found and starts\n     * installing, at which point the installing service worker is used.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    getSW() {\n        // If `this._sw` is set, resolve with that as we want `getSW()` to\n        // return the correct (new) service worker if an update is found.\n        return this._sw !== undefined\n            ? Promise.resolve(this._sw)\n            : this._swDeferred.promise;\n    }\n    /**\n     * Sends the passed data object to the service worker registered by this\n     * instance (via {@link workbox-window.Workbox#getSW}) and resolves\n     * with a response (if any).\n     *\n     * A response can be set in a message handler in the service worker by\n     * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n     * returned by `messageSW()`. If no response is set, the promise will never\n     * resolve.\n     *\n     * @param {Object} data An object to send to the service worker\n     * @return {Promise<Object>}\n     */\n    // We might be able to change the 'data' type to Record<string, unknown> in the future.\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    async messageSW(data) {\n        const sw = await this.getSW();\n        return messageSW(sw, data);\n    }\n    /**\n     * Sends a `{type: 'SKIP_WAITING'}` message to the service worker that's\n     * currently in the `waiting` state associated with the current registration.\n     *\n     * If there is no current registration or no service worker is `waiting`,\n     * calling this will have no effect.\n     */\n    messageSkipWaiting() {\n        if (this._registration && this._registration.waiting) {\n            void messageSW(this._registration.waiting, SKIP_WAITING_MESSAGE);\n        }\n    }\n    /**\n     * Checks for a service worker already controlling the page and returns\n     * it if its script URL matches.\n     *\n     * @private\n     * @return {ServiceWorker|undefined}\n     */\n    _getControllingSWIfCompatible() {\n        const controller = navigator.serviceWorker.controller;\n        if (controller &&\n            urlsMatch(controller.scriptURL, this._scriptURL.toString())) {\n            return controller;\n        }\n        else {\n            return undefined;\n        }\n    }\n    /**\n     * Registers a service worker for this instances script URL and register\n     * options and tracks the time registration was complete.\n     *\n     * @private\n     */\n    async _registerScript() {\n        try {\n            // this._scriptURL may be a TrustedScriptURL, but there's no support for\n            // passing that to register() in lib.dom right now.\n            // https://github.com/GoogleChrome/workbox/issues/2855\n            const reg = await navigator.serviceWorker.register(this._scriptURL, this._registerOptions);\n            // Keep track of when registration happened, so it can be used in the\n            // `this._onUpdateFound` heuristic. Also use the presence of this\n            // property as a way to see if `.register()` has been called.\n            this._registrationTime = performance.now();\n            return reg;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(error);\n            }\n            // Re-throw the error.\n            throw error;\n        }\n    }\n}\nexport { Workbox };\n// The jsdoc comments below outline the events this instance may dispatch:\n// -----------------------------------------------------------------------\n/**\n * The `message` event is dispatched any time a `postMessage` is received.\n *\n * @event workbox-window.Workbox#message\n * @type {WorkboxEvent}\n * @property {*} data The `data` property from the original `message` event.\n * @property {Event} originalEvent The original [`message`]{@link https://developer.mozilla.org/en-US/docs/Web/API/MessageEvent}\n *     event.\n * @property {string} type `message`.\n * @property {MessagePort[]} ports The `ports` value from `originalEvent`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `installed` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `installed`.\n *\n * Then can happen either the very first time a service worker is installed,\n * or after an update to the current service worker is found. In the case\n * of an update being found, the event's `isUpdate` property will be `true`.\n *\n * @event workbox-window.Workbox#installed\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `installed`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `waiting` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `installed` and then doesn't immediately change to `activating`.\n * It may also be dispatched if a service worker with the same\n * [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * was already waiting when the {@link workbox-window.Workbox#register}\n * method was called.\n *\n * @event workbox-window.Workbox#waiting\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event|undefined} originalEvent The original\n *    [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event, or `undefined` in the case where the service worker was waiting\n *     to before `.register()` was called.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {boolean|undefined} wasWaitingBeforeRegister True if a service worker with\n *     a matching `scriptURL` was already waiting when this `Workbox`\n *     instance called `register()`.\n * @property {string} type `waiting`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `controlling` event is dispatched if a\n * [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n * fires on the service worker [container]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer}\n * and the [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * of the new [controller]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/controller}\n * matches the `scriptURL` of the `Workbox` instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}.\n *\n * @event workbox-window.Workbox#controlling\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this service worker was registered.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `controlling`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `activated` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `activated`.\n *\n * @event workbox-window.Workbox#activated\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `activated`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `redundant` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `redundant`.\n *\n * @event workbox-window.Workbox#redundant\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {string} type `redundant`.\n * @property {Workbox} target The `Workbox` instance.\n */\n"], "names": ["self", "_", "e", "messageSW", "sw", "data", "Promise", "resolve", "messageChannel", "MessageChannel", "port1", "onmessage", "event", "postMessage", "port2", "Deferred", "promise", "reject", "dontWait<PERSON>or", "then", "logger", "globalThis", "__WB_DISABLE_DEV_LOGS", "inGroup", "methodToColorMap", "debug", "log", "warn", "error", "groupCollapsed", "groupEnd", "print", "method", "args", "test", "navigator", "userAgent", "console", "styles", "logPrefix", "join", "api", "loggerMethods", "Object", "keys", "key", "WorkboxEventTarget", "_eventListenerRegistry", "Map", "addEventListener", "type", "listener", "foo", "_getEventListenersByType", "add", "removeEventListener", "delete", "dispatchEvent", "target", "listeners", "has", "set", "Set", "get", "urlsMatch", "url1", "url2", "location", "href", "URL", "WorkboxEvent", "props", "assign", "_await", "value", "direct", "WAITING_TIMEOUT_DURATION", "_async", "f", "i", "arguments", "length", "apply", "REGISTRATION_TIMEOUT_DURATION", "_empty", "SKIP_WAITING_MESSAGE", "_awaitIgnored", "Workbox", "scriptURL", "registerOptions", "_registerOptions", "_updateFoundCount", "_swDeferred", "_activeD<PERSON><PERSON>red", "_controlling<PERSON><PERSON><PERSON><PERSON>", "_registrationTime", "_ownSWs", "_onUpdateFound", "registration", "_registration", "installingSW", "installing", "updateLikelyTriggeredExternally", "_scriptURL", "toString", "performance", "now", "_externalSW", "_sw", "serviceWorker", "controller", "_onStateChange", "originalEvent", "state", "isExternal", "eventProps", "_isUpdate", "isUpdate", "_waitingTimeout", "setTimeout", "waiting", "clearTimeout", "_compatibleControllingSW", "_onControllerChange", "_onMessage", "ports", "source", "getSW", "register", "immediate", "process", "document", "readyState", "res", "window", "Boolean", "_getControllingSWIfCompatible", "_registerScript", "once", "waitingSW", "wasWaitingBeforeRegister", "currentPageIsOutOfScope", "scopeURL", "scope", "baseURI", "scopeURLBasePath", "pathname", "startsWith", "update", "undefined", "messageSkipWaiting", "reg", "body", "result", "recover"], "mappings": "AAEA,IAAI;AACAA,EAAAA,IAAI,CAAC,sBAAD,CAAJ,IAAgCC,CAAC,EAAjC;AACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,SAAT,CAAmBC,EAAnB,EAAuBC,IAAvB,EAA6B;AACzB,SAAO,IAAIC,OAAJ,CAAY,UAACC,OAAD,EAAa;AAC5B,QAAMC,cAAc,GAAG,IAAIC,cAAJ,EAAvB;;AACAD,IAAAA,cAAc,CAACE,KAAf,CAAqBC,SAArB,GAAiC,UAACC,KAAD,EAAW;AACxCL,MAAAA,OAAO,CAACK,KAAK,CAACP,IAAP,CAAP;AACH,KAFD;;AAGAD,IAAAA,EAAE,CAACS,WAAH,CAAeR,IAAf,EAAqB,CAACG,cAAc,CAACM,KAAhB,CAArB;AACH,GANM,CAAP;AAOH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9BD,IAAI;AACAd,EAAAA,IAAI,CAAC,oBAAD,CAAJ,IAA8BC,CAAC,EAA/B;AACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACMa;AACF;AACJ;AACA;AACI,oBAAc;AAAA;;AACV,OAAKC,OAAL,GAAe,IAAIV,OAAJ,CAAY,UAACC,OAAD,EAAUU,MAAV,EAAqB;AAC5C,IAAA,KAAI,CAACV,OAAL,GAAeA,OAAf;AACA,IAAA,KAAI,CAACU,MAAL,GAAcA,MAAd;AACH,GAHc,CAAf;AAIH;;ACzBL;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;AACO,SAASC,WAAT,CAAqBF,OAArB,EAA8B;AACjC;AACA,OAAKA,OAAO,CAACG,IAAR,CAAa,YAAM,EAAnB,CAAL;AACH;;ACfD;AACA;AACA;AACA;AACA;AACA;AAEA,IAAMC,MAAM,GAEL,YAAM;AACL;AACA;AACA,MAAI,EAAE,2BAA2BC,UAA7B,CAAJ,EAA8C;AAC1CrB,IAAAA,IAAI,CAACsB,qBAAL,GAA6B,KAA7B;AACH;;AACD,MAAIC,OAAO,GAAG,KAAd;AACA,MAAMC,gBAAgB,GAAG;AACrBC,IAAAA,KAAK,WADgB;AAErBC,IAAAA,GAAG,WAFkB;AAGrBC,IAAAA,IAAI,WAHiB;AAIrBC,IAAAA,KAAK,WAJgB;AAKrBC,IAAAA,cAAc,WALO;AAMrBC,IAAAA,QAAQ,EAAE,IANW;;AAAA,GAAzB;;AAQA,MAAMC,KAAK,GAAG,SAARA,KAAQ,CAAUC,MAAV,EAAkBC,IAAlB,EAAwB;AAAA;;AAClC,QAAIjC,IAAI,CAACsB,qBAAT,EAAgC;AAC5B;AACH;;AACD,QAAIU,MAAM,KAAK,gBAAf,EAAiC;AAC7B;AACA;AACA,UAAI,iCAAiCE,IAAjC,CAAsCC,SAAS,CAACC,SAAhD,CAAJ,EAAgE;AAAA;;AAC5D,oBAAAC,OAAO,EAACL,MAAD,CAAP,iBAAmBC,IAAnB;;AACA;AACH;AACJ;;AACD,QAAMK,MAAM,GAAG,kBACId,gBAAgB,CAACQ,MAAD,CADpB,oFAAf,CAZkC;;AAoBlC,QAAMO,SAAS,GAAGhB,OAAO,GAAG,EAAH,GAAQ,CAAC,WAAD,EAAce,MAAM,CAACE,IAAP,CAAY,GAAZ,CAAd,CAAjC;;AACA,iBAAAH,OAAO,EAACL,MAAD,CAAP,kBAAmBO,SAAnB,QAAiCN,IAAjC;;AACA,QAAID,MAAM,KAAK,gBAAf,EAAiC;AAC7BT,MAAAA,OAAO,GAAG,IAAV;AACH;;AACD,QAAIS,MAAM,KAAK,UAAf,EAA2B;AACvBT,MAAAA,OAAO,GAAG,KAAV;AACH;AACJ,GA5BD,CAfK;;;AA6CL,MAAMkB,GAAG,GAAG,EAAZ;AACA,MAAMC,aAAa,GAAGC,MAAM,CAACC,IAAP,CAAYpB,gBAAZ,CAAtB;;AA9CK;AA+CA,QAAMqB,GAAG,qBAAT;AACD,QAAMb,MAAM,GAAGa,GAAf;;AACAJ,IAAAA,GAAG,CAACT,MAAD,CAAH,GAAc,YAAa;AAAA,wCAATC,IAAS;AAATA,QAAAA,IAAS;AAAA;;AACvBF,MAAAA,KAAK,CAACC,MAAD,EAASC,IAAT,CAAL;AACH,KAFD;AAjDC;;AA+CL,oCAAkBS,aAAlB,oCAAiC;AAAA;AAKhC;;AACD,SAAOD,GAAP;AACH,CAtDC,EAFN;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;IACaK,kBAAb;AACI,gCAAc;AACV,SAAKC,sBAAL,GAA8B,IAAIC,GAAJ,EAA9B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AARA;;AAAA,SASIC,gBATJ,GASI,0BAAiBC,IAAjB,EAAuBC,QAAvB,EAAiC;AAC7B,QAAMC,GAAG,GAAG,KAAKC,wBAAL,CAA8BH,IAA9B,CAAZ;;AACAE,IAAAA,GAAG,CAACE,GAAJ,CAAQH,QAAR;AACH;AACD;AACJ;AACA;AACA;AACA;AAjBA;;AAAA,SAkBII,mBAlBJ,GAkBI,6BAAoBL,IAApB,EAA0BC,QAA1B,EAAoC;AAChC,SAAKE,wBAAL,CAA8BH,IAA9B,EAAoCM,MAApC,CAA2CL,QAA3C;AACH;AACD;AACJ;AACA;AACA;AAxBA;;AAAA,SAyBIM,aAzBJ,GAyBI,uBAAc7C,KAAd,EAAqB;AACjBA,IAAAA,KAAK,CAAC8C,MAAN,GAAe,IAAf;;AACA,QAAMC,SAAS,GAAG,KAAKN,wBAAL,CAA8BzC,KAAK,CAACsC,IAApC,CAAlB;;AACA,yDAAuBS,SAAvB,wCAAkC;AAAA,UAAvBR,QAAuB;AAC9BA,MAAAA,QAAQ,CAACvC,KAAD,CAAR;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAvCA;;AAAA,SAwCIyC,wBAxCJ,GAwCI,kCAAyBH,IAAzB,EAA+B;AAC3B,QAAI,CAAC,KAAKH,sBAAL,CAA4Ba,GAA5B,CAAgCV,IAAhC,CAAL,EAA4C;AACxC,WAAKH,sBAAL,CAA4Bc,GAA5B,CAAgCX,IAAhC,EAAsC,IAAIY,GAAJ,EAAtC;AACH;;AACD,WAAO,KAAKf,sBAAL,CAA4BgB,GAA5B,CAAgCb,IAAhC,CAAP;AACH,GA7CL;;AAAA;AAAA;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASc,SAAT,CAAmBC,IAAnB,EAAyBC,IAAzB,EAA+B;AAAA,kBACjBC,QADiB;AAAA,MAC1BC,IAD0B,aAC1BA,IAD0B;AAElC,SAAO,IAAIC,GAAJ,CAAQJ,IAAR,EAAcG,IAAd,EAAoBA,IAApB,KAA6B,IAAIC,GAAJ,CAAQH,IAAR,EAAcE,IAAd,EAAoBA,IAAxD;AACH;;ACpBD;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;IACaE,YAAb,GACI,sBAAYpB,IAAZ,EAAkBqB,KAAlB,EAAyB;AACrB,OAAKrB,IAAL,GAAYA,IAAZ;AACAP,EAAAA,MAAM,CAAC6B,MAAP,CAAc,IAAd,EAAoBD,KAApB;AACH;;ACFL;AACA;;AAmEO,SAASE,MAAT,CAAgBC,KAAhB,EAAuBvD,IAAvB,EAA6BwD,MAA7B,EAAqC;AAC3C,MAAIA,MAAJ,EAAY;AACX,WAAOxD,IAAI,GAAGA,IAAI,CAACuD,KAAD,CAAP,GAAiBA,KAA5B;AACA;;AACD,MAAI,CAACA,KAAD,IAAU,CAACA,KAAK,CAACvD,IAArB,EAA2B;AAC1BuD,IAAAA,KAAK,GAAGpE,OAAO,CAACC,OAAR,CAAgBmE,KAAhB,CAAR;AACA;;AACD,SAAOvD,IAAI,GAAGuD,KAAK,CAACvD,IAAN,CAAWA,IAAX,CAAH,GAAsBuD,KAAjC;AACA;;AA1ED,IAAME,wBAAwB,GAAG,GAAjC;AAEA;;AAkDO,SAASC,MAAT,CAAgBC,CAAhB,EAAmB;AACzB,SAAO,YAAW;AACjB,SAAK,IAAI7C,IAAI,GAAG,EAAX,EAAe8C,CAAC,GAAG,CAAxB,EAA2BA,CAAC,GAAGC,SAAS,CAACC,MAAzC,EAAiDF,CAAC,EAAlD,EAAsD;AACrD9C,MAAAA,IAAI,CAAC8C,CAAD,CAAJ,GAAUC,SAAS,CAACD,CAAD,CAAnB;AACA;;AACD,QAAI;AACH,aAAOzE,OAAO,CAACC,OAAR,CAAgBuE,CAAC,CAACI,KAAF,CAAQ,IAAR,EAAcjD,IAAd,CAAhB,CAAP;AACA,KAFD,CAEE,OAAM/B,CAAN,EAAS;AACV,aAAOI,OAAO,CAACW,MAAR,CAAef,CAAf,CAAP;AACA;AACD,GATD;AAUA;;AA5DD,IAAMiF,6BAA6B,GAAG,KAAtC;AAEA;;AAykBO,SAASC,MAAT,GAAkB;;AAxkBzB,IAAMC,oBAAoB,GAAG;AAAEnC,EAAAA,IAAI,EAAE;AAAR,CAA7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AA2DO,SAASoC,aAAT,CAAuBZ,KAAvB,EAA8BC,MAA9B,EAAsC;AAC5C,MAAI,CAACA,MAAL,EAAa;AACZ,WAAOD,KAAK,IAAIA,KAAK,CAACvD,IAAf,GAAsBuD,KAAK,CAACvD,IAAN,CAAWiE,MAAX,CAAtB,GAA2C9E,OAAO,CAACC,OAAR,EAAlD;AACA;AACD;;IA9DKgF;;;AACF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI;AACA,mBAAYC,SAAZ,EAAuBC,eAAvB,EAA6C;AAAA;;AAAA,QAAtBA,eAAsB;AAAtBA,MAAAA,eAAsB,GAAJ,EAAI;AAAA;;AACzC;AACA,UAAKC,gBAAL,GAAwB,EAAxB;AACA,UAAKC,iBAAL,GAAyB,CAAzB,CAHyC;;AAKzC,UAAKC,WAAL,GAAmB,IAAI7E,QAAJ,EAAnB;AACA,UAAK8E,eAAL,GAAuB,IAAI9E,QAAJ,EAAvB;AACA,UAAK+E,oBAAL,GAA4B,IAAI/E,QAAJ,EAA5B;AACA,UAAKgF,iBAAL,GAAyB,CAAzB;AACA,UAAKC,OAAL,GAAe,IAAIlC,GAAJ,EAAf;AACA;AACR;AACA;;AACQ,UAAKmC,cAAL,GAAsB,YAAM;AACxB;AACA,UAAMC,YAAY,GAAG,MAAKC,aAA1B;AACA,UAAMC,YAAY,GAAGF,YAAY,CAACG,UAAlC,CAHwB;AAKxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,UAAMC,+BAA+B;AAErC;AACA;AACA;AACA,YAAKX,iBAAL,GAAyB,CAAzB;AAEI;AACA;AACA,OAAC3B,SAAS,CAACoC,YAAY,CAACZ,SAAd,EAAyB,MAAKe,UAAL,CAAgBC,QAAhB,EAAzB,CAJd;AAMI;AACA;AACAC,MAAAA,WAAW,CAACC,GAAZ,KAAoB,MAAKX,iBAAL,GAAyBZ,6BARjD;AAUQ;AACA,UAXR,GAYM,KAjBN;;AAkBA,UAAImB,+BAAJ,EAAqC;AACjC,cAAKK,WAAL,GAAmBP,YAAnB;AACAF,QAAAA,YAAY,CAAC3C,mBAAb,CAAiC,aAAjC,EAAgD,MAAK0C,cAArD;AACH,OAHD,MAIK;AACD;AACA;AACA,cAAKW,GAAL,GAAWR,YAAX;;AACA,cAAKJ,OAAL,CAAa1C,GAAb,CAAiB8C,YAAjB;;AACA,cAAKR,WAAL,CAAiBrF,OAAjB,CAAyB6F,YAAzB,EALC;AAOD;;;AACA,QAA2C;AACvC,cAAIjE,SAAS,CAAC0E,aAAV,CAAwBC,UAA5B,EAAwC;AACpC1F,YAAAA,MAAM,CAACM,GAAP,CAAW,iDAAX;AACH,WAFD,MAGK;AACDN,YAAAA,MAAM,CAACM,GAAP,CAAW,iCAAX;AACH;AACJ;AACJ,OAtDuB;AAwDxB;;;AACA,QAAE,MAAKiE,iBAAP,CAzDwB;AA2DxB;;AACAS,MAAAA,YAAY,CAACnD,gBAAb,CAA8B,aAA9B,EAA6C,MAAK8D,cAAlD;AACH,KA7DD;AA8DA;AACR;AACA;AACA;;;AACQ,UAAKA,cAAL,GAAsB,UAACC,aAAD,EAAmB;AACrC;AACA,UAAMd,YAAY,GAAG,MAAKC,aAA1B;AACA,UAAM/F,EAAE,GAAG4G,aAAa,CAACtD,MAAzB;AAHqC,UAI7BuD,KAJ6B,GAInB7G,EAJmB,CAI7B6G,KAJ6B;AAKrC,UAAMC,UAAU,GAAG9G,EAAE,KAAK,MAAKuG,WAA/B;AACA,UAAMQ,UAAU,GAAG;AACf/G,QAAAA,EAAE,EAAFA,EADe;AAEf8G,QAAAA,UAAU,EAAVA,UAFe;AAGfF,QAAAA,aAAa,EAAbA;AAHe,OAAnB;;AAKA,UAAI,CAACE,UAAD,IAAe,MAAKE,SAAxB,EAAmC;AAC/BD,QAAAA,UAAU,CAACE,QAAX,GAAsB,IAAtB;AACH;;AACD,YAAK5D,aAAL,CAAmB,IAAIa,YAAJ,CAAiB2C,KAAjB,EAAwBE,UAAxB,CAAnB;;AACA,UAAIF,KAAK,KAAK,WAAd,EAA2B;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAKK,eAAL,GAAuBtH,IAAI,CAACuH,UAAL,CAAgB,YAAM;AACzC;AACA,cAAIN,KAAK,KAAK,WAAV,IAAyBf,YAAY,CAACsB,OAAb,KAAyBpH,EAAtD,EAA0D;AACtD,kBAAKqD,aAAL,CAAmB,IAAIa,YAAJ,CAAiB,SAAjB,EAA4B6C,UAA5B,CAAnB;;AACA,YAA2C;AACvC,kBAAID,UAAJ,EAAgB;AACZ9F,gBAAAA,MAAM,CAACO,IAAP,CAAY,qDACR,uDADJ;AAEH,eAHD,MAIK;AACDP,gBAAAA,MAAM,CAACO,IAAP,CAAY,qDACR,oDADJ;AAEH;AACJ;AACJ;AACJ,SAfsB,EAepBiD,wBAfoB,CAAvB;AAgBH,OAzBD,MA0BK,IAAIqC,KAAK,KAAK,YAAd,EAA4B;AAC7BQ,QAAAA,YAAY,CAAC,MAAKH,eAAN,CAAZ;;AACA,YAAI,CAACJ,UAAL,EAAiB;AACb,gBAAKrB,eAAL,CAAqBtF,OAArB,CAA6BH,EAA7B;AACH;AACJ;;AACD,MAA2C;AACvC,gBAAQ6G,KAAR;AACI,eAAK,WAAL;AACI,gBAAIC,UAAJ,EAAgB;AACZ9F,cAAAA,MAAM,CAACO,IAAP,CAAY,+CACR,iDADJ;AAEH,aAHD,MAIK;AACDP,cAAAA,MAAM,CAACM,GAAP,CAAW,sCAAX;AACH;;AACD;;AACJ,eAAK,WAAL;AACI,gBAAIwF,UAAJ,EAAgB;AACZ9F,cAAAA,MAAM,CAACO,IAAP,CAAY,2CAAZ;AACH,aAFD,MAGK;AACDP,cAAAA,MAAM,CAACM,GAAP,CAAW,sCAAX;;AACA,kBAAItB,EAAE,KAAK+B,SAAS,CAAC0E,aAAV,CAAwBC,UAAnC,EAA+C;AAC3C1F,gBAAAA,MAAM,CAACO,IAAP,CAAY,iDACR,8CADQ,GAER,0CAFJ;AAGH;AACJ;;AACD;;AACJ,eAAK,WAAL;AACI,gBAAIvB,EAAE,KAAK,MAAKsH,wBAAhB,EAA0C;AACtCtG,cAAAA,MAAM,CAACM,GAAP,CAAW,sDAAX;AACH,aAFD,MAGK,IAAI,CAACwF,UAAL,EAAiB;AAClB9F,cAAAA,MAAM,CAACM,GAAP,CAAW,0CAAX;AACH;;AACD;AA9BR;AAgCH;AACJ,KAjFD;AAkFA;AACR;AACA;AACA;;;AACQ,UAAKiG,mBAAL,GAA2B,UAACX,aAAD,EAAmB;AAC1C,UAAM5G,EAAE,GAAG,MAAKwG,GAAhB;AACA,UAAMM,UAAU,GAAG9G,EAAE,KAAK+B,SAAS,CAAC0E,aAAV,CAAwBC,UAAlD,CAF0C;AAI1C;AACA;AACA;;AACA,YAAKrD,aAAL,CAAmB,IAAIa,YAAJ,CAAiB,aAAjB,EAAgC;AAC/C4C,QAAAA,UAAU,EAAVA,UAD+C;AAE/CF,QAAAA,aAAa,EAAbA,aAF+C;AAG/C5G,QAAAA,EAAE,EAAFA,EAH+C;AAI/CiH,QAAAA,QAAQ,EAAE,MAAKD;AAJgC,OAAhC,CAAnB;;AAMA,UAAI,CAACF,UAAL,EAAiB;AACb,QAA2C;AACvC9F,UAAAA,MAAM,CAACM,GAAP,CAAW,sDAAX;AACH;;AACD,cAAKoE,oBAAL,CAA0BvF,OAA1B,CAAkCH,EAAlC;AACH;AACJ,KAnBD;AAoBA;AACR;AACA;AACA;;;AACQ,UAAKwH,UAAL,oBAAyBZ,aAAzB,EAA2C;AACvC;AACA;AAFuC,UAG/B3G,IAH+B,GAGP2G,aAHO,CAG/B3G,IAH+B;AAAA,UAGzBwH,KAHyB,GAGPb,aAHO,CAGzBa,KAHyB;AAAA,UAGlBC,MAHkB,GAGPd,aAHO,CAGlBc,MAHkB;AAKvC;;AALuC,oBAMjC,MAAKC,KAAL,EANiC;AAAA,YAanC,MAAK/B,OAAL,CAAapC,GAAb,CAAiBkE,MAAjB,CAbmC;AAcnC,gBAAKrE,aAAL,CAAmB,IAAIa,YAAJ,CAAiB,SAAjB,EAA4B;AAC3C;AACA;AACAjE,YAAAA,IAAI,EAAJA,IAH2C;AAI3C2G,YAAAA,aAAa,EAAbA,aAJ2C;AAK3Ca,YAAAA,KAAK,EAALA,KAL2C;AAM3CzH,YAAAA,EAAE,EAAE0H;AANuC,WAA5B,CAAnB;AAdmC;AAAA;AAQvC;AACA;AACA;AACA;AACA;AAWH,KAvBD;AAwBA,UAAKvB,UAAL,GAAkBf,SAAlB;AACA,UAAKE,gBAAL,GAAwBD,eAAxB,CAtNyC;AAwNzC;AACA;;AACAtD,IAAAA,SAAS,CAAC0E,aAAV,CAAwB5D,gBAAxB,CAAyC,SAAzC,EAAoD,MAAK2E,UAAzD;AA1NyC;AA2N5C;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;SACUI;kCAAiC;8BAAtBC;QAAAA,wCAAY;;QAAc;AAAA,mBAE/B,IAF+B;;AACvC,UAAIC,KAAA,KAAyB,YAA7B,EAA2C;AACvC,YAAI,OAAKnC,iBAAT,EAA4B;AACxB3E,UAAAA,MAAM,CAACQ,KAAP,CAAa,wDACT,iDADJ;AAEA;AACH;AACJ;;AAPsC;AAAA,YAQnC,CAACqG,SAAD,IAAcE,QAAQ,CAACC,UAAT,KAAwB,UARH;AAAA,+BAS7B,IAAI9H,OAAJ,CAAY,UAAC+H,GAAD;AAAA,mBAASC,MAAM,CAACrF,gBAAP,CAAwB,MAAxB,EAAgCoF,GAAhC,CAAT;AAAA,WAAZ,CAT6B;AAAA;AAAA;AAWvC;AACA;AACA,eAAKjB,SAAL,GAAiBmB,OAAO,CAACpG,SAAS,CAAC0E,aAAV,CAAwBC,UAAzB,CAAxB,CAbuC;AAevC;AACA;;AACA,eAAKY,wBAAL,GAAgC,OAAKc,6BAAL,EAAhC;AAjBuC,sBAkBZ,OAAKC,eAAL,EAlBY;AAkBvC,iBAAKtC,aAAL;;AACA;AACA;AACA,cAAI,OAAKuB,wBAAT,EAAmC;AAC/B,mBAAKd,GAAL,GAAW,OAAKc,wBAAhB;;AACA,mBAAK7B,eAAL,CAAqBtF,OAArB,CAA6B,OAAKmH,wBAAlC;;AACA,mBAAK5B,oBAAL,CAA0BvF,OAA1B,CAAkC,OAAKmH,wBAAvC;;AACA,mBAAKA,wBAAL,CAA8BzE,gBAA9B,CAA+C,aAA/C,EAA8D,OAAK8D,cAAnE,EAAmF;AAAE2B,cAAAA,IAAI,EAAE;AAAR,aAAnF;AACH,WA1BsC;AA4BvC;AACA;AACA;AACA;;;AACA,cAAMC,SAAS,GAAG,OAAKxC,aAAL,CAAmBqB,OAArC;;AACA,cAAImB,SAAS,IACT3E,SAAS,CAAC2E,SAAS,CAACnD,SAAX,EAAsB,OAAKe,UAAL,CAAgBC,QAAhB,EAAtB,CADb,EACgE;AAC5D;AACA;AACA,mBAAKI,GAAL,GAAW+B,SAAX,CAH4D;AAK5D;;AACAzH,YAAAA,WAAW,CAACZ,OAAO,CAACC,OAAR,GAAkBY,IAAlB,CAAuB,YAAM;AACrC,qBAAKsC,aAAL,CAAmB,IAAIa,YAAJ,CAAiB,SAAjB,EAA4B;AAC3ClE,gBAAAA,EAAE,EAAEuI,SADuC;AAE3CC,gBAAAA,wBAAwB,EAAE;AAFiB,eAA5B,CAAnB;;AAIA,kBAAIV,KAAA,KAAyB,YAA7B,EAA2C;AACvC9G,gBAAAA,MAAM,CAACO,IAAP,CAAY,sDACR,sCADJ;AAEH;AACJ,aATW,CAAD,CAAX;AAUH,WAlDsC;;;AAoDvC,cAAI,OAAKiF,GAAT,EAAc;AACV,mBAAKhB,WAAL,CAAiBrF,OAAjB,CAAyB,OAAKqG,GAA9B;;AACA,mBAAKZ,OAAL,CAAa1C,GAAb,CAAiB,OAAKsD,GAAtB;AACH;;AACD,cAAIsB,KAAA,KAAyB,YAA7B,EAA2C;AACvC9G,YAAAA,MAAM,CAACM,GAAP,CAAW,yCAAX,EAAsD,OAAK6E,UAAL,CAAgBC,QAAhB,EAAtD;;AACA,gBAAIrE,SAAS,CAAC0E,aAAV,CAAwBC,UAA5B,EAAwC;AACpC,kBAAI,OAAKY,wBAAT,EAAmC;AAC/BtG,gBAAAA,MAAM,CAACK,KAAP,CAAa,+CACT,mCADJ;AAEH,eAHD,MAIK;AACDL,gBAAAA,MAAM,CAACK,KAAP,CAAa,qDACT,8DADS,GAET,uBAFJ;AAGH;AACJ;;AACD,gBAAMoH,uBAAuB,GAAG,SAA1BA,uBAA0B,GAAM;AAClC,kBAAMC,QAAQ,GAAG,IAAIzE,GAAJ,CAAQ,OAAKqB,gBAAL,CAAsBqD,KAAtB,IAA+B,OAAKxC,UAAL,CAAgBC,QAAhB,EAAvC,EAAmE2B,QAAQ,CAACa,OAA5E,CAAjB;AACA,kBAAMC,gBAAgB,GAAG,IAAI5E,GAAJ,CAAQ,IAAR,EAAcyE,QAAQ,CAAC1E,IAAvB,EAA6B8E,QAAtD;AACA,qBAAO,CAAC/E,QAAQ,CAAC+E,QAAT,CAAkBC,UAAlB,CAA6BF,gBAA7B,CAAR;AACH,aAJD;;AAKA,gBAAIJ,uBAAuB,EAA3B,EAA+B;AAC3BzH,cAAAA,MAAM,CAACO,IAAP,CAAY,yDACR,qCADJ;AAEH;AACJ;;AACD,iBAAKwE,aAAL,CAAmBlD,gBAAnB,CAAoC,aAApC,EAAmD,OAAKgD,cAAxD;;AACA9D,UAAAA,SAAS,CAAC0E,aAAV,CAAwB5D,gBAAxB,CAAyC,kBAAzC,EAA6D,OAAK0E,mBAAlE;AACA,iBAAO,OAAKxB,aAAZ;AAjFuC;AAAA;AAkF1C;;;;AACD;AACJ;AACA;;;SACUiD;QAAS;AAAA,mBACN,IADM;;AACX,UAAI,CAAC,OAAKjD,aAAV,EAAyB;AACrB,YAAI+B,KAAA,KAAyB,YAA7B,EAA2C;AACvC9G,UAAAA,MAAM,CAACQ,KAAP,CAAa,8CACT,wDADJ;AAEH;;AACD;AACH,OAPU;;;AAAA,2BASL,OAAKuE,aAAL,CAAmBiD,MAAnB,EATK;AAUd;;;;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAmBI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;SACIrB,QAAA,iBAAQ;AACJ;AACA;AACA,WAAO,KAAKnB,GAAL,KAAayC,SAAb,GACD/I,OAAO,CAACC,OAAR,CAAgB,KAAKqG,GAArB,CADC,GAED,KAAKhB,WAAL,CAAiB5E,OAFvB;AAGH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI;AACA;;;SACMb,iCAAUE;QAAM;AAAA,mBACD,IADC;;AAAA,oBACD,OAAK0H,KAAL,EADC,YACZ3H,EADY;AAElB,eAAOD,SAAS,CAACC,EAAD,EAAKC,IAAL,CAAhB;AAFkB;AAGrB;;;;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;SACIiJ,qBAAA,8BAAqB;AACjB,QAAI,KAAKnD,aAAL,IAAsB,KAAKA,aAAL,CAAmBqB,OAA7C,EAAsD;AAClD,WAAKrH,SAAS,CAAC,KAAKgG,aAAL,CAAmBqB,OAApB,EAA6BnC,oBAA7B,CAAd;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;SACImD,gCAAA,yCAAgC;AAC5B,QAAM1B,UAAU,GAAG3E,SAAS,CAAC0E,aAAV,CAAwBC,UAA3C;;AACA,QAAIA,UAAU,IACV9C,SAAS,CAAC8C,UAAU,CAACtB,SAAZ,EAAuB,KAAKe,UAAL,CAAgBC,QAAhB,EAAvB,CADb,EACiE;AAC7D,aAAOM,UAAP;AACH,KAHD,MAIK;AACD,aAAOuC,SAAP;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;SACUZ;QAAkB;AAAA,mBAKmC,IALnC;;AAAA,gCAChB;AACA;AACA;AACA;AAHA,sBAIkBtG,SAAS,CAAC0E,aAAV,CAAwBmB,QAAxB,CAAiC,OAAKzB,UAAtC,EAAkD,OAAKb,gBAAvD,CAJlB,YAIM6D,GAJN;AAKA;AACA;AACA;AACA,iBAAKxD,iBAAL,GAAyBU,WAAW,CAACC,GAAZ,EAAzB;AACA,iBAAO6C,GAAP;AATA;AAUH,OAXmB,YAYb3H,KAZa,EAYN;AACV,YAAIsG,KAAA,KAAyB,YAA7B,EAA2C;AACvC9G,UAAAA,MAAM,CAACQ,KAAP,CAAaA,KAAb;AACH,SAHS;;;AAKV,cAAMA,KAAN;AACH,OAlBmB;AAmBvB;;;;;;;wBAjHY;AACT,aAAO,KAAKiE,eAAL,CAAqB7E,OAA5B;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;wBACsB;AACd,aAAO,KAAK8E,oBAAL,CAA0B9E,OAAjC;AACH;;;;EA9WiB8B;;AA4ff,iBAAiB0G,IAAjB,EAAuBrI,IAAvB,EAA6B;AACnC,MAAIsI,MAAM,GAAGD,IAAI,EAAjB;;AACA,MAAIC,MAAM,IAAIA,MAAM,CAACtI,IAArB,EAA2B;AAC1B,WAAOsI,MAAM,CAACtI,IAAP,CAAYA,IAAZ,CAAP;AACA;;AACD,SAAOA,IAAI,CAACsI,MAAD,CAAX;AACA;AAhDD;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAzDO,gBAAgBD,IAAhB,EAAsBE,OAAtB,EAA+B;AACrC,MAAI;AACH,QAAID,MAAM,GAAGD,IAAI,EAAjB;AACA,GAFD,CAEE,OAAMtJ,CAAN,EAAS;AACV,WAAOwJ,OAAO,CAACxJ,CAAD,CAAd;AACA;;AACD,MAAIuJ,MAAM,IAAIA,MAAM,CAACtI,IAArB,EAA2B;AAC1B,WAAOsI,MAAM,CAACtI,IAAP,CAAY,KAAK,CAAjB,EAAoBuI,OAApB,CAAP;AACA;;AACD,SAAOD,MAAP;AACA;;;;"}
/**
 * lucide-react v0.0.1 - ISC
 */

import createLucideIcon from '../createLucideIcon.mjs';

const FileBox = createLucideIcon("FileBox", [
  [
    "path",
    {
      d: "M14.5 22H18a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4",
      key: "h7jej2"
    }
  ],
  ["polyline", { points: "14 2 14 8 20 8", key: "1ew0cm" }],
  [
    "path",
    {
      d: "M2.97 13.12c-.6.36-.97 1.02-.97 1.74v3.28c0 .72.37 1.38.97 1.74l3 1.83c.63.39 1.43.39 2.06 0l3-1.83c.6-.36.97-1.02.97-1.74v-3.28c0-.72-.37-1.38-.97-1.74l-3-1.83a1.97 1.97 0 0 0-2.06 0l-3 1.83Z",
      key: "f4a3oc"
    }
  ],
  ["path", { d: "m7 17-4.74-2.85", key: "etm6su" }],
  ["path", { d: "m7 17 4.74-2.85", key: "5xuooz" }],
  ["path", { d: "M7 17v5", key: "1yj1jh" }]
]);

export { FileBox as default };
//# sourceMappingURL=file-box.mjs.map

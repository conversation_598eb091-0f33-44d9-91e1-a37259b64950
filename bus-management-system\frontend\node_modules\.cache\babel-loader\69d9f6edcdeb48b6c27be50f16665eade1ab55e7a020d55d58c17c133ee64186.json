{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport toast from 'react-hot-toast';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  profile: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  SET_LOADING: 'SET_LOADING',\n  SET_ERROR: 'SET_ERROR',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_PROFILE: 'UPDATE_PROFILE',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        profile: action.payload.profile,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      return {\n        ...state,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        profile: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        profile: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    case AUTH_ACTIONS.SET_ERROR:\n      return {\n        ...state,\n        error: action.payload,\n        isLoading: false\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    case AUTH_ACTIONS.UPDATE_PROFILE:\n      return {\n        ...state,\n        profile: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem('access_token');\n      const user = localStorage.getItem('user');\n      if (token && user) {\n        try {\n          const response = await authAPI.getProfile();\n          dispatch({\n            type: AUTH_ACTIONS.LOGIN_SUCCESS,\n            payload: {\n              user: JSON.parse(user),\n              profile: response.data.data.profile\n            }\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('access_token');\n          localStorage.removeItem('refresh_token');\n          localStorage.removeItem('user');\n          dispatch({\n            type: AUTH_ACTIONS.LOGOUT\n          });\n        }\n      } else {\n        dispatch({\n          type: AUTH_ACTIONS.SET_LOADING,\n          payload: false\n        });\n      }\n    };\n    checkAuth();\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    dispatch({\n      type: AUTH_ACTIONS.LOGIN_START\n    });\n    try {\n      const response = await authAPI.login(credentials);\n      const {\n        access_token,\n        refresh_token,\n        user,\n        profile\n      } = response.data.data;\n\n      // Store tokens and user data\n      localStorage.setItem('access_token', access_token);\n      localStorage.setItem('refresh_token', refresh_token);\n      localStorage.setItem('user', JSON.stringify(user));\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: {\n          user,\n          profile\n        }\n      });\n      toast.success(`Welcome back, ${user.first_name}!`);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    dispatch({\n      type: AUTH_ACTIONS.REGISTER_START\n    });\n    try {\n      const response = await authAPI.register(userData);\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS\n      });\n      toast.success('Registration successful! Please login with your credentials.');\n      return {\n        success: true,\n        data: response.data.data\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      // Ignore logout API errors\n    } finally {\n      // Clear storage and state\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n      localStorage.removeItem('user');\n      dispatch({\n        type: AUTH_ACTIONS.LOGOUT\n      });\n      toast.success('Logged out successfully');\n    }\n  };\n\n  // Change password function\n  const changePassword = async passwordData => {\n    try {\n      await authAPI.changePassword(passwordData);\n      toast.success('Password changed successfully');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) || 'Password change failed';\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n\n  // Update profile function\n  const updateProfile = profileData => {\n    dispatch({\n      type: AUTH_ACTIONS.UPDATE_PROFILE,\n      payload: profileData\n    });\n  };\n\n  // Check if user has specific role\n  const hasRole = role => {\n    var _state$user;\n    return ((_state$user = state.user) === null || _state$user === void 0 ? void 0 : _state$user.role) === role;\n  };\n\n  // Check if user has any of the specified roles\n  const hasAnyRole = roles => {\n    var _state$user2;\n    return roles.includes((_state$user2 = state.user) === null || _state$user2 === void 0 ? void 0 : _state$user2.role);\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return hasRole('admin');\n  };\n\n  // Check if user is driver or conductor\n  const isDriver = () => {\n    return hasAnyRole(['driver', 'conductor']);\n  };\n\n  // Check if user is student\n  const isStudent = () => {\n    return hasRole('student');\n  };\n\n  // Check if user is parent\n  const isParent = () => {\n    return hasRole('parent');\n  };\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    changePassword,\n    clearError,\n    updateProfile,\n    hasRole,\n    hasAnyRole,\n    isAdmin,\n    isDriver,\n    isStudent,\n    isParent\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 10\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authAPI", "toast", "jsxDEV", "_jsxDEV", "initialState", "user", "profile", "isAuthenticated", "isLoading", "error", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "LOGOUT", "SET_LOADING", "SET_ERROR", "CLEAR_ERROR", "UPDATE_PROFILE", "REGISTER_START", "REGISTER_SUCCESS", "REGISTER_FAILURE", "authReducer", "state", "action", "type", "payload", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "checkAuth", "token", "localStorage", "getItem", "response", "getProfile", "JSON", "parse", "data", "removeItem", "login", "credentials", "access_token", "refresh_token", "setItem", "stringify", "success", "first_name", "_error$response", "_error$response$data", "errorMessage", "register", "userData", "_error$response2", "_error$response2$data", "logout", "changePassword", "passwordData", "_error$response3", "_error$response3$data", "clearError", "updateProfile", "profileData", "hasRole", "role", "_state$user", "hasAnyRole", "roles", "_state$user2", "includes", "isAdmin", "isDriver", "isStudent", "isParent", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport toast from 'react-hot-toast';\n\n// Initial state\nconst initialState = {\n  user: null,\n  profile: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  SET_LOADING: 'SET_LOADING',\n  SET_ERROR: 'SET_ERROR',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_PROFILE: 'UPDATE_PROFILE',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE',\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        profile: action.payload.profile,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      return {\n        ...state,\n        isLoading: false,\n        error: null,\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        profile: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        profile: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n    case AUTH_ACTIONS.SET_ERROR:\n      return {\n        ...state,\n        error: action.payload,\n        isLoading: false,\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null,\n      };\n    case AUTH_ACTIONS.UPDATE_PROFILE:\n      return {\n        ...state,\n        profile: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem('access_token');\n      const user = localStorage.getItem('user');\n\n      if (token && user) {\n        try {\n          const response = await authAPI.getProfile();\n          dispatch({\n            type: AUTH_ACTIONS.LOGIN_SUCCESS,\n            payload: {\n              user: JSON.parse(user),\n              profile: response.data.data.profile,\n            },\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('access_token');\n          localStorage.removeItem('refresh_token');\n          localStorage.removeItem('user');\n          dispatch({ type: AUTH_ACTIONS.LOGOUT });\n        }\n      } else {\n        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  // Login function\n  const login = async (credentials) => {\n    dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n\n    try {\n      const response = await authAPI.login(credentials);\n      const { access_token, refresh_token, user, profile } = response.data.data;\n\n      // Store tokens and user data\n      localStorage.setItem('access_token', access_token);\n      localStorage.setItem('refresh_token', refresh_token);\n      localStorage.setItem('user', JSON.stringify(user));\n\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: { user, profile },\n      });\n\n      toast.success(`Welcome back, ${user.first_name}!`);\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.error || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    dispatch({ type: AUTH_ACTIONS.REGISTER_START });\n\n    try {\n      const response = await authAPI.register(userData);\n      \n      dispatch({ type: AUTH_ACTIONS.REGISTER_SUCCESS });\n      \n      toast.success('Registration successful! Please login with your credentials.');\n      return { success: true, data: response.data.data };\n    } catch (error) {\n      const errorMessage = error.response?.data?.error || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      // Ignore logout API errors\n    } finally {\n      // Clear storage and state\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n      localStorage.removeItem('user');\n      dispatch({ type: AUTH_ACTIONS.LOGOUT });\n      toast.success('Logged out successfully');\n    }\n  };\n\n  // Change password function\n  const changePassword = async (passwordData) => {\n    try {\n      await authAPI.changePassword(passwordData);\n      toast.success('Password changed successfully');\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.error || 'Password change failed';\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  // Update profile function\n  const updateProfile = (profileData) => {\n    dispatch({\n      type: AUTH_ACTIONS.UPDATE_PROFILE,\n      payload: profileData,\n    });\n  };\n\n  // Check if user has specific role\n  const hasRole = (role) => {\n    return state.user?.role === role;\n  };\n\n  // Check if user has any of the specified roles\n  const hasAnyRole = (roles) => {\n    return roles.includes(state.user?.role);\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return hasRole('admin');\n  };\n\n  // Check if user is driver or conductor\n  const isDriver = () => {\n    return hasAnyRole(['driver', 'conductor']);\n  };\n\n  // Check if user is student\n  const isStudent = () => {\n    return hasRole('student');\n  };\n\n  // Check if user is parent\n  const isParent = () => {\n    return hasRole('parent');\n  };\n\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    changePassword,\n    clearError,\n    updateProfile,\n    hasRole,\n    hasAnyRole,\n    isAdmin,\n    isDriver,\n    isStudent,\n    isParent,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,OAAO,EAAE,IAAI;EACbC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,cAAc,EAAE,gBAAgB;EAChCC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,YAAY,CAACC,WAAW;IAC7B,KAAKD,YAAY,CAACS,cAAc;MAC9B,OAAO;QACL,GAAGI,KAAK;QACRf,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACE,aAAa;MAC7B,OAAO;QACL,GAAGW,KAAK;QACRlB,IAAI,EAAEmB,MAAM,CAACE,OAAO,CAACrB,IAAI;QACzBC,OAAO,EAAEkB,MAAM,CAACE,OAAO,CAACpB,OAAO;QAC/BC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACU,gBAAgB;MAChC,OAAO;QACL,GAAGG,KAAK;QACRf,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACG,aAAa;IAC/B,KAAKH,YAAY,CAACW,gBAAgB;MAChC,OAAO;QACL,GAAGE,KAAK;QACRlB,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,IAAI;QACbC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEe,MAAM,CAACE;MAChB,CAAC;IACH,KAAKhB,YAAY,CAACI,MAAM;MACtB,OAAO;QACL,GAAGS,KAAK;QACRlB,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,IAAI;QACbC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACK,WAAW;MAC3B,OAAO;QACL,GAAGQ,KAAK;QACRf,SAAS,EAAEgB,MAAM,CAACE;MACpB,CAAC;IACH,KAAKhB,YAAY,CAACM,SAAS;MACzB,OAAO;QACL,GAAGO,KAAK;QACRd,KAAK,EAAEe,MAAM,CAACE,OAAO;QACrBlB,SAAS,EAAE;MACb,CAAC;IACH,KAAKE,YAAY,CAACO,WAAW;MAC3B,OAAO;QACL,GAAGM,KAAK;QACRd,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACQ,cAAc;MAC9B,OAAO;QACL,GAAGK,KAAK;QACRjB,OAAO,EAAEkB,MAAM,CAACE;MAClB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAG/B,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMgC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACP,KAAK,EAAEQ,QAAQ,CAAC,GAAGjC,UAAU,CAACwB,WAAW,EAAElB,YAAY,CAAC;;EAE/D;EACAL,SAAS,CAAC,MAAM;IACd,MAAMiC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MAClD,MAAM9B,IAAI,GAAG6B,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAEzC,IAAIF,KAAK,IAAI5B,IAAI,EAAE;QACjB,IAAI;UACF,MAAM+B,QAAQ,GAAG,MAAMpC,OAAO,CAACqC,UAAU,CAAC,CAAC;UAC3CN,QAAQ,CAAC;YACPN,IAAI,EAAEf,YAAY,CAACE,aAAa;YAChCc,OAAO,EAAE;cACPrB,IAAI,EAAEiC,IAAI,CAACC,KAAK,CAAClC,IAAI,CAAC;cACtBC,OAAO,EAAE8B,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAClC;YAC9B;UACF,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;UACd;UACAyB,YAAY,CAACO,UAAU,CAAC,cAAc,CAAC;UACvCP,YAAY,CAACO,UAAU,CAAC,eAAe,CAAC;UACxCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;UAC/BV,QAAQ,CAAC;YAAEN,IAAI,EAAEf,YAAY,CAACI;UAAO,CAAC,CAAC;QACzC;MACF,CAAC,MAAM;QACLiB,QAAQ,CAAC;UAAEN,IAAI,EAAEf,YAAY,CAACK,WAAW;UAAEW,OAAO,EAAE;QAAM,CAAC,CAAC;MAC9D;IACF,CAAC;IAEDM,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnCZ,QAAQ,CAAC;MAAEN,IAAI,EAAEf,YAAY,CAACC;IAAY,CAAC,CAAC;IAE5C,IAAI;MACF,MAAMyB,QAAQ,GAAG,MAAMpC,OAAO,CAAC0C,KAAK,CAACC,WAAW,CAAC;MACjD,MAAM;QAAEC,YAAY;QAAEC,aAAa;QAAExC,IAAI;QAAEC;MAAQ,CAAC,GAAG8B,QAAQ,CAACI,IAAI,CAACA,IAAI;;MAEzE;MACAN,YAAY,CAACY,OAAO,CAAC,cAAc,EAAEF,YAAY,CAAC;MAClDV,YAAY,CAACY,OAAO,CAAC,eAAe,EAAED,aAAa,CAAC;MACpDX,YAAY,CAACY,OAAO,CAAC,MAAM,EAAER,IAAI,CAACS,SAAS,CAAC1C,IAAI,CAAC,CAAC;MAElD0B,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACE,aAAa;QAChCc,OAAO,EAAE;UAAErB,IAAI;UAAEC;QAAQ;MAC3B,CAAC,CAAC;MAEFL,KAAK,CAAC+C,OAAO,CAAC,iBAAiB3C,IAAI,CAAC4C,UAAU,GAAG,CAAC;MAClD,OAAO;QAAED,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MAAA,IAAAyC,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAzC,KAAK,CAAC2B,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsB1C,KAAK,KAAI,cAAc;MAClEsB,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACG,aAAa;QAChCa,OAAO,EAAE0B;MACX,CAAC,CAAC;MACF,OAAO;QAAEJ,OAAO,EAAE,KAAK;QAAEvC,KAAK,EAAE2C;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnCvB,QAAQ,CAAC;MAAEN,IAAI,EAAEf,YAAY,CAACS;IAAe,CAAC,CAAC;IAE/C,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMpC,OAAO,CAACqD,QAAQ,CAACC,QAAQ,CAAC;MAEjDvB,QAAQ,CAAC;QAAEN,IAAI,EAAEf,YAAY,CAACU;MAAiB,CAAC,CAAC;MAEjDnB,KAAK,CAAC+C,OAAO,CAAC,8DAA8D,CAAC;MAC7E,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAER,IAAI,EAAEJ,QAAQ,CAACI,IAAI,CAACA;MAAK,CAAC;IACpD,CAAC,CAAC,OAAO/B,KAAK,EAAE;MAAA,IAAA8C,gBAAA,EAAAC,qBAAA;MACd,MAAMJ,YAAY,GAAG,EAAAG,gBAAA,GAAA9C,KAAK,CAAC2B,QAAQ,cAAAmB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsB/C,KAAK,KAAI,qBAAqB;MACzEsB,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACW,gBAAgB;QACnCK,OAAO,EAAE0B;MACX,CAAC,CAAC;MACF,OAAO;QAAEJ,OAAO,EAAE,KAAK;QAAEvC,KAAK,EAAE2C;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMK,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMzD,OAAO,CAACyD,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACd;IAAA,CACD,SAAS;MACR;MACAyB,YAAY,CAACO,UAAU,CAAC,cAAc,CAAC;MACvCP,YAAY,CAACO,UAAU,CAAC,eAAe,CAAC;MACxCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;MAC/BV,QAAQ,CAAC;QAAEN,IAAI,EAAEf,YAAY,CAACI;MAAO,CAAC,CAAC;MACvCb,KAAK,CAAC+C,OAAO,CAAC,yBAAyB,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMU,cAAc,GAAG,MAAOC,YAAY,IAAK;IAC7C,IAAI;MACF,MAAM3D,OAAO,CAAC0D,cAAc,CAACC,YAAY,CAAC;MAC1C1D,KAAK,CAAC+C,OAAO,CAAC,+BAA+B,CAAC;MAC9C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MAAA,IAAAmD,gBAAA,EAAAC,qBAAA;MACd,MAAMT,YAAY,GAAG,EAAAQ,gBAAA,GAAAnD,KAAK,CAAC2B,QAAQ,cAAAwB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBpD,KAAK,KAAI,wBAAwB;MAC5E,OAAO;QAAEuC,OAAO,EAAE,KAAK;QAAEvC,KAAK,EAAE2C;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMU,UAAU,GAAGA,CAAA,KAAM;IACvB/B,QAAQ,CAAC;MAAEN,IAAI,EAAEf,YAAY,CAACO;IAAY,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAM8C,aAAa,GAAIC,WAAW,IAAK;IACrCjC,QAAQ,CAAC;MACPN,IAAI,EAAEf,YAAY,CAACQ,cAAc;MACjCQ,OAAO,EAAEsC;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,OAAO,GAAIC,IAAI,IAAK;IAAA,IAAAC,WAAA;IACxB,OAAO,EAAAA,WAAA,GAAA5C,KAAK,CAAClB,IAAI,cAAA8D,WAAA,uBAAVA,WAAA,CAAYD,IAAI,MAAKA,IAAI;EAClC,CAAC;;EAED;EACA,MAAME,UAAU,GAAIC,KAAK,IAAK;IAAA,IAAAC,YAAA;IAC5B,OAAOD,KAAK,CAACE,QAAQ,EAAAD,YAAA,GAAC/C,KAAK,CAAClB,IAAI,cAAAiE,YAAA,uBAAVA,YAAA,CAAYJ,IAAI,CAAC;EACzC,CAAC;;EAED;EACA,MAAMM,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAOP,OAAO,CAAC,OAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAMQ,QAAQ,GAAGA,CAAA,KAAM;IACrB,OAAOL,UAAU,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMM,SAAS,GAAGA,CAAA,KAAM;IACtB,OAAOT,OAAO,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMU,QAAQ,GAAGA,CAAA,KAAM;IACrB,OAAOV,OAAO,CAAC,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMW,KAAK,GAAG;IACZ,GAAGrD,KAAK;IACRmB,KAAK;IACLW,QAAQ;IACRI,MAAM;IACNC,cAAc;IACdI,UAAU;IACVC,aAAa;IACbE,OAAO;IACPG,UAAU;IACVI,OAAO;IACPC,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC;EAED,oBAAOxE,OAAA,CAACwB,WAAW,CAACkD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/C,QAAA,EAAEA;EAAQ;IAAAiD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;;AAED;AAAAnD,EAAA,CA/KaF,YAAY;AAAAsD,EAAA,GAAZtD,YAAY;AAgLzB,OAAO,MAAMuD,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGxF,UAAU,CAAC8B,WAAW,CAAC;EACvC,IAAI,CAAC0D,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAexD,WAAW;AAAC,IAAAuD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
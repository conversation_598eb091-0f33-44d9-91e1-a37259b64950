# Bus Management System

A comprehensive full-stack Bus Management System with AI integration for educational institutions and transportation companies.

## 🚌 Features

### Phase 1: Bus Setup & Staff Allocation
- **Vehicle Registration**: Add buses with auto-form fill from RTO database
- **Driver & Staff Assignment**: Auto-match based on shift/timings
- **Route Creation**: Optimal route suggestion with map API
- **Document Management**: Insurance/RC with expiry alerts and OCR

### Phase 2: Daily Operations
- **Student/Staff Allocation**: Auto-assign nearest route
- **Attendance Management**: QR/biometric boarding with real-time dashboard
- **Timetable & Trip Logging**: Delay predictions via traffic API
- **SMS Alerts**: Template-based smart notifications
- **Fee Collection**: Scholarship-based fee adjustment

### Phase 3: Maintenance & Monitoring
- **Maintenance Logging**: Predictive maintenance alerts
- **GPS Live Tracking**: Location-based delay notifications
- **Driver Performance**: AI-based behavior tracking
- **Occupancy Statistics**: Capacity optimization engine

### Phase 4: Exit & Record Management
- **Staff Management**: History archive auto-generation
- **Bus Deregistration**: Archive and RTO notifications

## 🛠️ Tech Stack

### Frontend
- React 18 with TypeScript
- Tailwind CSS for styling
- React Router for navigation
- Axios for API calls
- React Query for data management
- Leaflet for maps integration

### Backend
- Python Flask with SQLAlchemy
- MySQL database
- JWT authentication
- Flask-SocketIO for real-time updates
- Celery for background tasks
- Redis for caching

### AI Integration
- OpenAI API for intelligent features
- Google Maps API for route optimization
- Twilio for SMS notifications
- OCR for document scanning

## 📁 Project Structure

```
bus-management-system/
├── frontend/                 # React application
│   ├── src/
│   │   ├── components/      # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── hooks/          # Custom hooks
│   │   ├── utils/          # Utility functions
│   │   └── types/          # TypeScript types
│   └── package.json
├── backend/                 # Flask application
│   ├── app/
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utilities
│   │   └── ai/             # AI integration
│   ├── config.py
│   ├── requirements.txt
│   └── run.py
├── docs/                   # Documentation
├── .env.example
└── README.md
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v16+)
- Python (3.8+)
- MySQL (8.0+)
- Redis

### Installation

1. **Clone Repository**
```bash
git clone <repository-url>
cd bus-management-system
```

2. **Backend Setup**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Configure your .env file
flask db upgrade
python run.py
```

3. **Frontend Setup**
```bash
cd frontend
npm install
cp .env.example .env
# Configure your .env file
npm start
```

## 🔑 Default Access

- **Admin**: <EMAIL> / admin123
- **Driver**: <EMAIL> / driver123
- **Student**: <EMAIL> / student123

## 📱 Key Features

### Real-time Tracking
- Live GPS tracking of all buses
- Real-time attendance updates
- Instant delay notifications

### AI-Powered Intelligence
- Route optimization algorithms
- Predictive maintenance scheduling
- Smart staff allocation
- Automated document processing

### Mobile-First Design
- Responsive web application
- QR code scanning for attendance
- Push notifications
- Offline capability

## 🌐 API Documentation

The system provides RESTful APIs for all operations:
- Authentication and user management
- Bus and route management
- Real-time tracking and updates
- Maintenance and reporting

## 🔧 Configuration

### Environment Variables

**Backend (.env)**
```env
DATABASE_URL=mysql://user:pass@localhost/bus_management
JWT_SECRET_KEY=your-secret-key
GOOGLE_MAPS_API_KEY=your-maps-key
TWILIO_ACCOUNT_SID=your-twilio-sid
OPENAI_API_KEY=your-openai-key
```

**Frontend (.env)**
```env
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_MAPS_API_KEY=your-maps-key
```

## 📊 Monitoring & Analytics

- Real-time dashboard with key metrics
- Route efficiency analytics
- Driver performance reports
- Maintenance cost tracking
- Student attendance patterns

## 🚀 Deployment

### Production Deployment
- Frontend: Vercel/Netlify
- Backend: Railway/Render
- Database: PlanetScale/Railway MySQL
- Cache: Redis Cloud

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License.

---

**Built for modern transportation management with AI-powered intelligence**

{"version": 3, "names": ["extractParserOptionsPlugin", "parserOverride", "code", "opts"], "sources": ["../../src/worker/extract-parser-options-plugin.cts"], "sourcesContent": ["export = function extractParserOptionsPlugin() {\n  return {\n    parserOverride(code: string, opts: any) {\n      return opts;\n    },\n  };\n};\n"], "mappings": ";;iBAAS,SAASA,0BAA0BA,CAAA,EAAG;EAC7C,OAAO;IACLC,cAAcA,CAACC,IAAY,EAAEC,IAAS,EAAE;MACtC,OAAOA,IAAI;IACb;EACF,CAAC;AACH,CAAC", "ignoreList": []}
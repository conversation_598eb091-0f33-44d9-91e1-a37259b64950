import React from 'react';
import { DollarSign, Plus, CreditCard } from 'lucide-react';

const FeeManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Fee Management</h1>
          <p className="text-gray-600">Manage transportation fees with scholarship support</p>
        </div>
        <button className="btn btn-primary flex items-center space-x-2">
          <Plus className="h-5 w-5" />
          <span>Generate Fees</span>
        </button>
      </div>

      <div className="card text-center py-12">
        <DollarSign className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Fee Management</h3>
        <p className="text-gray-600 mb-6">
          Comprehensive fee management with AI-powered scholarship calculations.
        </p>
        <div className="space-y-2 text-sm text-gray-500">
          <p>✓ Automated fee generation and calculation</p>
          <p>✓ Scholarship and discount management</p>
          <p>✓ Multiple payment method support</p>
          <p>✓ Fee reminder notifications</p>
          <p>✓ Payment tracking and receipts</p>
        </div>
      </div>
    </div>
  );
};

export default FeeManagement;

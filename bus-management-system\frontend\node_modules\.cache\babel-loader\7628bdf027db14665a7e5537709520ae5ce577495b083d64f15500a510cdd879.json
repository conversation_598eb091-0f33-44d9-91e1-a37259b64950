{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Archive = createLucideIcon(\"Archive\", [[\"rect\", {\n  width: \"20\",\n  height: \"5\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"uhwcea\"\n}], [\"path\", {\n  d: \"M4 9v9a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9\",\n  key: \"shkvi4\"\n}], [\"path\", {\n  d: \"M10 13h4\",\n  key: \"ytezjc\"\n}]]);\nexport { Archive as default };", "map": {"version": 3, "names": ["Archive", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\archive.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Archive\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iNSIgeD0iMiIgeT0iNCIgcng9IjIiIC8+CiAgPHBhdGggZD0iTTQgOXY5YTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDItMlY5IiAvPgogIDxwYXRoIGQ9Ik0xMCAxM2g0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/archive\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Archive = createLucideIcon('Archive', [\n  [\n    'rect',\n    { width: '20', height: '5', x: '2', y: '4', rx: '2', key: 'uhwcea' },\n  ],\n  ['path', { d: 'M4 9v9a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9', key: 'shkvi4' }],\n  ['path', { d: 'M10 13h4', key: 'ytezjc' }],\n]);\n\nexport default Archive;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
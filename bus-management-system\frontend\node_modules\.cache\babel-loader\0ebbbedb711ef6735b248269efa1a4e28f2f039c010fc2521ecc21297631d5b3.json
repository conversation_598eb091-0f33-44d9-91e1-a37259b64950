{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst AlignHorizontalJustifyEnd = createLucideIcon(\"AlignHorizontalJustifyEnd\", [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"2\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"dy24zr\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"12\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"1ht384\"\n}], [\"path\", {\n  d: \"M22 2v20\",\n  key: \"40qfg1\"\n}]]);\nexport { AlignHorizontalJustifyEnd as default };", "map": {"version": 3, "names": ["AlignHorizontalJustifyEnd", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\align-horizontal-justify-end.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignHorizontalJustifyEnd\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSIxNCIgeD0iMiIgeT0iNSIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjYiIGhlaWdodD0iMTAiIHg9IjEyIiB5PSI3IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNMjIgMnYyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/align-horizontal-justify-end\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignHorizontalJustifyEnd = createLucideIcon(\n  'AlignHorizontalJustifyEnd',\n  [\n    [\n      'rect',\n      { width: '6', height: '14', x: '2', y: '5', rx: '2', key: 'dy24zr' },\n    ],\n    [\n      'rect',\n      { width: '6', height: '10', x: '12', y: '7', rx: '2', key: '1ht384' },\n    ],\n    ['path', { d: 'M22 2v20', key: '40qfg1' }],\n  ],\n);\n\nexport default AlignHorizontalJustifyEnd;\n"], "mappings": ";;;;;AAaA,MAAMA,yBAA4B,GAAAC,gBAAA,CAChC,6BACA,CACE,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CACE,QACA;EAAEL,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAE7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
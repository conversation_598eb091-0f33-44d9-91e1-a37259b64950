{"ast": null, "code": "import removeAccents from 'remove-accents';\n\n/**\n * @name match-sorter\n * @license MIT license.\n * @copyright (c) 2020 Kent C<PERSON>\n * <AUTHOR> <PERSON><PERSON> <<EMAIL>> (https://kentcdodds.com)\n */\nconst rankings = {\n  CASE_SENSITIVE_EQUAL: 7,\n  EQUAL: 6,\n  STARTS_WITH: 5,\n  WORD_STARTS_WITH: 4,\n  CONTAINS: 3,\n  ACRONYM: 2,\n  MATCHES: 1,\n  NO_MATCH: 0\n};\nconst defaultBaseSortFn = (a, b) => String(a.rankedValue).localeCompare(String(b.rankedValue));\n\n/**\n * Takes an array of items and a value and returns a new array with the items that match the given value\n * @param {Array} items - the items to sort\n * @param {String} value - the value to use for ranking\n * @param {Object} options - Some options to configure the sorter\n * @return {Array} - the new sorted array\n */\nfunction matchSorter(items, value, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    keys,\n    threshold = rankings.MATCHES,\n    baseSort = defaultBaseSortFn,\n    sorter = matchedItems => matchedItems.sort((a, b) => sortRankedValues(a, b, baseSort))\n  } = options;\n  const matchedItems = items.reduce(reduceItemsToRanked, []);\n  return sorter(matchedItems).map(_ref => {\n    let {\n      item\n    } = _ref;\n    return item;\n  });\n  function reduceItemsToRanked(matches, item, index) {\n    const rankingInfo = getHighestRanking(item, keys, value, options);\n    const {\n      rank,\n      keyThreshold = threshold\n    } = rankingInfo;\n    if (rank >= keyThreshold) {\n      matches.push({\n        ...rankingInfo,\n        item,\n        index\n      });\n    }\n    return matches;\n  }\n}\nmatchSorter.rankings = rankings;\n\n/**\n * Gets the highest ranking for value for the given item based on its values for the given keys\n * @param {*} item - the item to rank\n * @param {Array} keys - the keys to get values from the item for the ranking\n * @param {String} value - the value to rank against\n * @param {Object} options - options to control the ranking\n * @return {{rank: Number, keyIndex: Number, keyThreshold: Number}} - the highest ranking\n */\nfunction getHighestRanking(item, keys, value, options) {\n  if (!keys) {\n    // if keys is not specified, then we assume the item given is ready to be matched\n    const stringItem = item;\n    return {\n      // ends up being duplicate of 'item' in matches but consistent\n      rankedValue: stringItem,\n      rank: getMatchRanking(stringItem, value, options),\n      keyIndex: -1,\n      keyThreshold: options.threshold\n    };\n  }\n  const valuesToRank = getAllValuesToRank(item, keys);\n  return valuesToRank.reduce((_ref2, _ref3, i) => {\n    let {\n      rank,\n      rankedValue,\n      keyIndex,\n      keyThreshold\n    } = _ref2;\n    let {\n      itemValue,\n      attributes\n    } = _ref3;\n    let newRank = getMatchRanking(itemValue, value, options);\n    let newRankedValue = rankedValue;\n    const {\n      minRanking,\n      maxRanking,\n      threshold\n    } = attributes;\n    if (newRank < minRanking && newRank >= rankings.MATCHES) {\n      newRank = minRanking;\n    } else if (newRank > maxRanking) {\n      newRank = maxRanking;\n    }\n    if (newRank > rank) {\n      rank = newRank;\n      keyIndex = i;\n      keyThreshold = threshold;\n      newRankedValue = itemValue;\n    }\n    return {\n      rankedValue: newRankedValue,\n      rank,\n      keyIndex,\n      keyThreshold\n    };\n  }, {\n    rankedValue: item,\n    rank: rankings.NO_MATCH,\n    keyIndex: -1,\n    keyThreshold: options.threshold\n  });\n}\n\n/**\n * Gives a rankings score based on how well the two strings match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @param {Object} options - options for the match (like keepDiacritics for comparison)\n * @returns {Number} the ranking for how well stringToRank matches testString\n */\nfunction getMatchRanking(testString, stringToRank, options) {\n  testString = prepareValueForComparison(testString, options);\n  stringToRank = prepareValueForComparison(stringToRank, options);\n\n  // too long\n  if (stringToRank.length > testString.length) {\n    return rankings.NO_MATCH;\n  }\n\n  // case sensitive equals\n  if (testString === stringToRank) {\n    return rankings.CASE_SENSITIVE_EQUAL;\n  }\n\n  // Lower casing before further comparison\n  testString = testString.toLowerCase();\n  stringToRank = stringToRank.toLowerCase();\n\n  // case insensitive equals\n  if (testString === stringToRank) {\n    return rankings.EQUAL;\n  }\n\n  // starts with\n  if (testString.startsWith(stringToRank)) {\n    return rankings.STARTS_WITH;\n  }\n\n  // word starts with\n  if (testString.includes(` ${stringToRank}`)) {\n    return rankings.WORD_STARTS_WITH;\n  }\n\n  // contains\n  if (testString.includes(stringToRank)) {\n    return rankings.CONTAINS;\n  } else if (stringToRank.length === 1) {\n    // If the only character in the given stringToRank\n    //   isn't even contained in the testString, then\n    //   it's definitely not a match.\n    return rankings.NO_MATCH;\n  }\n\n  // acronym\n  if (getAcronym(testString).includes(stringToRank)) {\n    return rankings.ACRONYM;\n  }\n\n  // will return a number between rankings.MATCHES and\n  // rankings.MATCHES + 1 depending  on how close of a match it is.\n  return getClosenessRanking(testString, stringToRank);\n}\n\n/**\n * Generates an acronym for a string.\n *\n * @param {String} string the string for which to produce the acronym\n * @returns {String} the acronym\n */\nfunction getAcronym(string) {\n  let acronym = '';\n  const wordsInString = string.split(' ');\n  wordsInString.forEach(wordInString => {\n    const splitByHyphenWords = wordInString.split('-');\n    splitByHyphenWords.forEach(splitByHyphenWord => {\n      acronym += splitByHyphenWord.substr(0, 1);\n    });\n  });\n  return acronym;\n}\n\n/**\n * Returns a score based on how spread apart the\n * characters from the stringToRank are within the testString.\n * A number close to rankings.MATCHES represents a loose match. A number close\n * to rankings.MATCHES + 1 represents a tighter match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @returns {Number} the number between rankings.MATCHES and\n * rankings.MATCHES + 1 for how well stringToRank matches testString\n */\nfunction getClosenessRanking(testString, stringToRank) {\n  let matchingInOrderCharCount = 0;\n  let charNumber = 0;\n  function findMatchingCharacter(matchChar, string, index) {\n    for (let j = index, J = string.length; j < J; j++) {\n      const stringChar = string[j];\n      if (stringChar === matchChar) {\n        matchingInOrderCharCount += 1;\n        return j + 1;\n      }\n    }\n    return -1;\n  }\n  function getRanking(spread) {\n    const spreadPercentage = 1 / spread;\n    const inOrderPercentage = matchingInOrderCharCount / stringToRank.length;\n    const ranking = rankings.MATCHES + inOrderPercentage * spreadPercentage;\n    return ranking;\n  }\n  const firstIndex = findMatchingCharacter(stringToRank[0], testString, 0);\n  if (firstIndex < 0) {\n    return rankings.NO_MATCH;\n  }\n  charNumber = firstIndex;\n  for (let i = 1, I = stringToRank.length; i < I; i++) {\n    const matchChar = stringToRank[i];\n    charNumber = findMatchingCharacter(matchChar, testString, charNumber);\n    const found = charNumber > -1;\n    if (!found) {\n      return rankings.NO_MATCH;\n    }\n  }\n  const spread = charNumber - firstIndex;\n  return getRanking(spread);\n}\n\n/**\n * Sorts items that have a rank, index, and keyIndex\n * @param {Object} a - the first item to sort\n * @param {Object} b - the second item to sort\n * @return {Number} -1 if a should come first, 1 if b should come first, 0 if equal\n */\nfunction sortRankedValues(a, b, baseSort) {\n  const aFirst = -1;\n  const bFirst = 1;\n  const {\n    rank: aRank,\n    keyIndex: aKeyIndex\n  } = a;\n  const {\n    rank: bRank,\n    keyIndex: bKeyIndex\n  } = b;\n  const same = aRank === bRank;\n  if (same) {\n    if (aKeyIndex === bKeyIndex) {\n      // use the base sort function as a tie-breaker\n      return baseSort(a, b);\n    } else {\n      return aKeyIndex < bKeyIndex ? aFirst : bFirst;\n    }\n  } else {\n    return aRank > bRank ? aFirst : bFirst;\n  }\n}\n\n/**\n * Prepares value for comparison by stringifying it, removing diacritics (if specified)\n * @param {String} value - the value to clean\n * @param {Object} options - {keepDiacritics: whether to remove diacritics}\n * @return {String} the prepared value\n */\nfunction prepareValueForComparison(value, _ref4) {\n  let {\n    keepDiacritics\n  } = _ref4;\n  // value might not actually be a string at this point (we don't get to choose)\n  // so part of preparing the value for comparison is ensure that it is a string\n  value = `${value}`; // toString\n  if (!keepDiacritics) {\n    value = removeAccents(value);\n  }\n  return value;\n}\n\n/**\n * Gets value for key in item at arbitrarily nested keypath\n * @param {Object} item - the item\n * @param {Object|Function} key - the potentially nested keypath or property callback\n * @return {Array} - an array containing the value(s) at the nested keypath\n */\nfunction getItemValues(item, key) {\n  if (typeof key === 'object') {\n    key = key.key;\n  }\n  let value;\n  if (typeof key === 'function') {\n    value = key(item);\n  } else if (item == null) {\n    value = null;\n  } else if (Object.hasOwnProperty.call(item, key)) {\n    value = item[key];\n  } else if (key.includes('.')) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n    return getNestedValues(key, item);\n  } else {\n    value = null;\n  }\n\n  // because `value` can also be undefined\n  if (value == null) {\n    return [];\n  }\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return [String(value)];\n}\n\n/**\n * Given path: \"foo.bar.baz\"\n * And item: {foo: {bar: {baz: 'buzz'}}}\n *   -> 'buzz'\n * @param path a dot-separated set of keys\n * @param item the item to get the value from\n */\nfunction getNestedValues(path, item) {\n  const keys = path.split('.');\n  let values = [item];\n  for (let i = 0, I = keys.length; i < I; i++) {\n    const nestedKey = keys[i];\n    let nestedValues = [];\n    for (let j = 0, J = values.length; j < J; j++) {\n      const nestedItem = values[j];\n      if (nestedItem == null) continue;\n      if (Object.hasOwnProperty.call(nestedItem, nestedKey)) {\n        const nestedValue = nestedItem[nestedKey];\n        if (nestedValue != null) {\n          nestedValues.push(nestedValue);\n        }\n      } else if (nestedKey === '*') {\n        // ensure that values is an array\n        nestedValues = nestedValues.concat(nestedItem);\n      }\n    }\n    values = nestedValues;\n  }\n  if (Array.isArray(values[0])) {\n    // keep allowing the implicit wildcard for an array of strings at the end of\n    // the path; don't use `.flat()` because that's not available in node.js v10\n    const result = [];\n    return result.concat(...values);\n  }\n  // Based on our logic it should be an array of strings by now...\n  // assuming the user's path terminated in strings\n  return values;\n}\n\n/**\n * Gets all the values for the given keys in the given item and returns an array of those values\n * @param item - the item from which the values will be retrieved\n * @param keys - the keys to use to retrieve the values\n * @return objects with {itemValue, attributes}\n */\nfunction getAllValuesToRank(item, keys) {\n  const allValues = [];\n  for (let j = 0, J = keys.length; j < J; j++) {\n    const key = keys[j];\n    const attributes = getKeyAttributes(key);\n    const itemValues = getItemValues(item, key);\n    for (let i = 0, I = itemValues.length; i < I; i++) {\n      allValues.push({\n        itemValue: itemValues[i],\n        attributes\n      });\n    }\n  }\n  return allValues;\n}\nconst defaultKeyAttributes = {\n  maxRanking: Infinity,\n  minRanking: -Infinity\n};\n/**\n * Gets all the attributes for the given key\n * @param key - the key from which the attributes will be retrieved\n * @return object containing the key's attributes\n */\nfunction getKeyAttributes(key) {\n  if (typeof key === 'string') {\n    return defaultKeyAttributes;\n  }\n  return {\n    ...defaultKeyAttributes,\n    ...key\n  };\n}\n\n/*\neslint\n  no-continue: \"off\",\n*/\n\nexport { defaultBaseSortFn, matchSorter, rankings };", "map": {"version": 3, "names": ["removeAccents", "rankings", "CASE_SENSITIVE_EQUAL", "EQUAL", "STARTS_WITH", "WORD_STARTS_WITH", "CONTAINS", "ACRONYM", "MATCHES", "NO_MATCH", "defaultBaseSortFn", "a", "b", "String", "rankedValue", "localeCompare", "matchSorter", "items", "value", "options", "keys", "threshold", "baseSort", "sorter", "matchedItems", "sort", "sortRankedValues", "reduce", "reduceItemsToRanked", "map", "_ref", "item", "matches", "index", "rankingInfo", "getHighestRanking", "rank", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "stringItem", "getMatchRanking", "keyIndex", "valuesToRank", "getAllValuesToRank", "_ref2", "_ref3", "i", "itemValue", "attributes", "newRank", "newRankedValue", "minRanking", "maxRanking", "testString", "stringToRank", "prepareValueForComparison", "length", "toLowerCase", "startsWith", "includes", "getAcronym", "getClosenessRanking", "string", "acronym", "wordsInString", "split", "for<PERSON>ach", "wordInString", "splitByHyphenWords", "splitByHyphenWord", "substr", "matchingInOrderCharCount", "char<PERSON><PERSON>ber", "findMatchingCharacter", "matchChar", "j", "J", "stringChar", "getRanking", "spread", "spreadPercentage", "inOrderPercentage", "ranking", "firstIndex", "I", "found", "<PERSON><PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "aRank", "aKeyIndex", "bRank", "bKeyIndex", "same", "_ref4", "keepDiacritics", "getItemValues", "key", "Object", "hasOwnProperty", "call", "getNestedValues", "Array", "isArray", "path", "values", "nested<PERSON><PERSON>", "nested<PERSON><PERSON><PERSON>", "nestedItem", "nestedV<PERSON>ue", "concat", "result", "allValues", "getKeyAttributes", "itemValues", "defaultKeyAttributes", "Infinity"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/match-sorter/dist/match-sorter.esm.js"], "sourcesContent": ["import removeAccents from 'remove-accents';\n\n/**\n * @name match-sorter\n * @license MIT license.\n * @copyright (c) 2020 Kent C<PERSON>\n * <AUTHOR> <PERSON><PERSON> <<EMAIL>> (https://kentcdodds.com)\n */\nconst rankings = {\n  CASE_SENSITIVE_EQUAL: 7,\n  EQUAL: 6,\n  STARTS_WITH: 5,\n  WORD_STARTS_WITH: 4,\n  CONTAINS: 3,\n  ACRONYM: 2,\n  MATCHES: 1,\n  NO_MATCH: 0\n};\nconst defaultBaseSortFn = (a, b) => String(a.rankedValue).localeCompare(String(b.rankedValue));\n\n/**\n * Takes an array of items and a value and returns a new array with the items that match the given value\n * @param {Array} items - the items to sort\n * @param {String} value - the value to use for ranking\n * @param {Object} options - Some options to configure the sorter\n * @return {Array} - the new sorted array\n */\nfunction matchSorter(items, value, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    keys,\n    threshold = rankings.MATCHES,\n    baseSort = defaultBaseSortFn,\n    sorter = matchedItems => matchedItems.sort((a, b) => sortRankedValues(a, b, baseSort))\n  } = options;\n  const matchedItems = items.reduce(reduceItemsToRanked, []);\n  return sorter(matchedItems).map(_ref => {\n    let {\n      item\n    } = _ref;\n    return item;\n  });\n  function reduceItemsToRanked(matches, item, index) {\n    const rankingInfo = getHighestRanking(item, keys, value, options);\n    const {\n      rank,\n      keyThreshold = threshold\n    } = rankingInfo;\n    if (rank >= keyThreshold) {\n      matches.push({\n        ...rankingInfo,\n        item,\n        index\n      });\n    }\n    return matches;\n  }\n}\nmatchSorter.rankings = rankings;\n\n/**\n * Gets the highest ranking for value for the given item based on its values for the given keys\n * @param {*} item - the item to rank\n * @param {Array} keys - the keys to get values from the item for the ranking\n * @param {String} value - the value to rank against\n * @param {Object} options - options to control the ranking\n * @return {{rank: Number, keyIndex: Number, keyThreshold: Number}} - the highest ranking\n */\nfunction getHighestRanking(item, keys, value, options) {\n  if (!keys) {\n    // if keys is not specified, then we assume the item given is ready to be matched\n    const stringItem = item;\n    return {\n      // ends up being duplicate of 'item' in matches but consistent\n      rankedValue: stringItem,\n      rank: getMatchRanking(stringItem, value, options),\n      keyIndex: -1,\n      keyThreshold: options.threshold\n    };\n  }\n  const valuesToRank = getAllValuesToRank(item, keys);\n  return valuesToRank.reduce((_ref2, _ref3, i) => {\n    let {\n      rank,\n      rankedValue,\n      keyIndex,\n      keyThreshold\n    } = _ref2;\n    let {\n      itemValue,\n      attributes\n    } = _ref3;\n    let newRank = getMatchRanking(itemValue, value, options);\n    let newRankedValue = rankedValue;\n    const {\n      minRanking,\n      maxRanking,\n      threshold\n    } = attributes;\n    if (newRank < minRanking && newRank >= rankings.MATCHES) {\n      newRank = minRanking;\n    } else if (newRank > maxRanking) {\n      newRank = maxRanking;\n    }\n    if (newRank > rank) {\n      rank = newRank;\n      keyIndex = i;\n      keyThreshold = threshold;\n      newRankedValue = itemValue;\n    }\n    return {\n      rankedValue: newRankedValue,\n      rank,\n      keyIndex,\n      keyThreshold\n    };\n  }, {\n    rankedValue: item,\n    rank: rankings.NO_MATCH,\n    keyIndex: -1,\n    keyThreshold: options.threshold\n  });\n}\n\n/**\n * Gives a rankings score based on how well the two strings match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @param {Object} options - options for the match (like keepDiacritics for comparison)\n * @returns {Number} the ranking for how well stringToRank matches testString\n */\nfunction getMatchRanking(testString, stringToRank, options) {\n  testString = prepareValueForComparison(testString, options);\n  stringToRank = prepareValueForComparison(stringToRank, options);\n\n  // too long\n  if (stringToRank.length > testString.length) {\n    return rankings.NO_MATCH;\n  }\n\n  // case sensitive equals\n  if (testString === stringToRank) {\n    return rankings.CASE_SENSITIVE_EQUAL;\n  }\n\n  // Lower casing before further comparison\n  testString = testString.toLowerCase();\n  stringToRank = stringToRank.toLowerCase();\n\n  // case insensitive equals\n  if (testString === stringToRank) {\n    return rankings.EQUAL;\n  }\n\n  // starts with\n  if (testString.startsWith(stringToRank)) {\n    return rankings.STARTS_WITH;\n  }\n\n  // word starts with\n  if (testString.includes(` ${stringToRank}`)) {\n    return rankings.WORD_STARTS_WITH;\n  }\n\n  // contains\n  if (testString.includes(stringToRank)) {\n    return rankings.CONTAINS;\n  } else if (stringToRank.length === 1) {\n    // If the only character in the given stringToRank\n    //   isn't even contained in the testString, then\n    //   it's definitely not a match.\n    return rankings.NO_MATCH;\n  }\n\n  // acronym\n  if (getAcronym(testString).includes(stringToRank)) {\n    return rankings.ACRONYM;\n  }\n\n  // will return a number between rankings.MATCHES and\n  // rankings.MATCHES + 1 depending  on how close of a match it is.\n  return getClosenessRanking(testString, stringToRank);\n}\n\n/**\n * Generates an acronym for a string.\n *\n * @param {String} string the string for which to produce the acronym\n * @returns {String} the acronym\n */\nfunction getAcronym(string) {\n  let acronym = '';\n  const wordsInString = string.split(' ');\n  wordsInString.forEach(wordInString => {\n    const splitByHyphenWords = wordInString.split('-');\n    splitByHyphenWords.forEach(splitByHyphenWord => {\n      acronym += splitByHyphenWord.substr(0, 1);\n    });\n  });\n  return acronym;\n}\n\n/**\n * Returns a score based on how spread apart the\n * characters from the stringToRank are within the testString.\n * A number close to rankings.MATCHES represents a loose match. A number close\n * to rankings.MATCHES + 1 represents a tighter match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @returns {Number} the number between rankings.MATCHES and\n * rankings.MATCHES + 1 for how well stringToRank matches testString\n */\nfunction getClosenessRanking(testString, stringToRank) {\n  let matchingInOrderCharCount = 0;\n  let charNumber = 0;\n  function findMatchingCharacter(matchChar, string, index) {\n    for (let j = index, J = string.length; j < J; j++) {\n      const stringChar = string[j];\n      if (stringChar === matchChar) {\n        matchingInOrderCharCount += 1;\n        return j + 1;\n      }\n    }\n    return -1;\n  }\n  function getRanking(spread) {\n    const spreadPercentage = 1 / spread;\n    const inOrderPercentage = matchingInOrderCharCount / stringToRank.length;\n    const ranking = rankings.MATCHES + inOrderPercentage * spreadPercentage;\n    return ranking;\n  }\n  const firstIndex = findMatchingCharacter(stringToRank[0], testString, 0);\n  if (firstIndex < 0) {\n    return rankings.NO_MATCH;\n  }\n  charNumber = firstIndex;\n  for (let i = 1, I = stringToRank.length; i < I; i++) {\n    const matchChar = stringToRank[i];\n    charNumber = findMatchingCharacter(matchChar, testString, charNumber);\n    const found = charNumber > -1;\n    if (!found) {\n      return rankings.NO_MATCH;\n    }\n  }\n  const spread = charNumber - firstIndex;\n  return getRanking(spread);\n}\n\n/**\n * Sorts items that have a rank, index, and keyIndex\n * @param {Object} a - the first item to sort\n * @param {Object} b - the second item to sort\n * @return {Number} -1 if a should come first, 1 if b should come first, 0 if equal\n */\nfunction sortRankedValues(a, b, baseSort) {\n  const aFirst = -1;\n  const bFirst = 1;\n  const {\n    rank: aRank,\n    keyIndex: aKeyIndex\n  } = a;\n  const {\n    rank: bRank,\n    keyIndex: bKeyIndex\n  } = b;\n  const same = aRank === bRank;\n  if (same) {\n    if (aKeyIndex === bKeyIndex) {\n      // use the base sort function as a tie-breaker\n      return baseSort(a, b);\n    } else {\n      return aKeyIndex < bKeyIndex ? aFirst : bFirst;\n    }\n  } else {\n    return aRank > bRank ? aFirst : bFirst;\n  }\n}\n\n/**\n * Prepares value for comparison by stringifying it, removing diacritics (if specified)\n * @param {String} value - the value to clean\n * @param {Object} options - {keepDiacritics: whether to remove diacritics}\n * @return {String} the prepared value\n */\nfunction prepareValueForComparison(value, _ref4) {\n  let {\n    keepDiacritics\n  } = _ref4;\n  // value might not actually be a string at this point (we don't get to choose)\n  // so part of preparing the value for comparison is ensure that it is a string\n  value = `${value}`; // toString\n  if (!keepDiacritics) {\n    value = removeAccents(value);\n  }\n  return value;\n}\n\n/**\n * Gets value for key in item at arbitrarily nested keypath\n * @param {Object} item - the item\n * @param {Object|Function} key - the potentially nested keypath or property callback\n * @return {Array} - an array containing the value(s) at the nested keypath\n */\nfunction getItemValues(item, key) {\n  if (typeof key === 'object') {\n    key = key.key;\n  }\n  let value;\n  if (typeof key === 'function') {\n    value = key(item);\n  } else if (item == null) {\n    value = null;\n  } else if (Object.hasOwnProperty.call(item, key)) {\n    value = item[key];\n  } else if (key.includes('.')) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n    return getNestedValues(key, item);\n  } else {\n    value = null;\n  }\n\n  // because `value` can also be undefined\n  if (value == null) {\n    return [];\n  }\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return [String(value)];\n}\n\n/**\n * Given path: \"foo.bar.baz\"\n * And item: {foo: {bar: {baz: 'buzz'}}}\n *   -> 'buzz'\n * @param path a dot-separated set of keys\n * @param item the item to get the value from\n */\nfunction getNestedValues(path, item) {\n  const keys = path.split('.');\n  let values = [item];\n  for (let i = 0, I = keys.length; i < I; i++) {\n    const nestedKey = keys[i];\n    let nestedValues = [];\n    for (let j = 0, J = values.length; j < J; j++) {\n      const nestedItem = values[j];\n      if (nestedItem == null) continue;\n      if (Object.hasOwnProperty.call(nestedItem, nestedKey)) {\n        const nestedValue = nestedItem[nestedKey];\n        if (nestedValue != null) {\n          nestedValues.push(nestedValue);\n        }\n      } else if (nestedKey === '*') {\n        // ensure that values is an array\n        nestedValues = nestedValues.concat(nestedItem);\n      }\n    }\n    values = nestedValues;\n  }\n  if (Array.isArray(values[0])) {\n    // keep allowing the implicit wildcard for an array of strings at the end of\n    // the path; don't use `.flat()` because that's not available in node.js v10\n    const result = [];\n    return result.concat(...values);\n  }\n  // Based on our logic it should be an array of strings by now...\n  // assuming the user's path terminated in strings\n  return values;\n}\n\n/**\n * Gets all the values for the given keys in the given item and returns an array of those values\n * @param item - the item from which the values will be retrieved\n * @param keys - the keys to use to retrieve the values\n * @return objects with {itemValue, attributes}\n */\nfunction getAllValuesToRank(item, keys) {\n  const allValues = [];\n  for (let j = 0, J = keys.length; j < J; j++) {\n    const key = keys[j];\n    const attributes = getKeyAttributes(key);\n    const itemValues = getItemValues(item, key);\n    for (let i = 0, I = itemValues.length; i < I; i++) {\n      allValues.push({\n        itemValue: itemValues[i],\n        attributes\n      });\n    }\n  }\n  return allValues;\n}\nconst defaultKeyAttributes = {\n  maxRanking: Infinity,\n  minRanking: -Infinity\n};\n/**\n * Gets all the attributes for the given key\n * @param key - the key from which the attributes will be retrieved\n * @return object containing the key's attributes\n */\nfunction getKeyAttributes(key) {\n  if (typeof key === 'string') {\n    return defaultKeyAttributes;\n  }\n  return {\n    ...defaultKeyAttributes,\n    ...key\n  };\n}\n\n/*\neslint\n  no-continue: \"off\",\n*/\n\nexport { defaultBaseSortFn, matchSorter, rankings };\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,gBAAgB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAG;EACfC,oBAAoB,EAAE,CAAC;EACvBC,KAAK,EAAE,CAAC;EACRC,WAAW,EAAE,CAAC;EACdC,gBAAgB,EAAE,CAAC;EACnBC,QAAQ,EAAE,CAAC;EACXC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE;AACZ,CAAC;AACD,MAAMC,iBAAiB,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKC,MAAM,CAACF,CAAC,CAACG,WAAW,CAAC,CAACC,aAAa,CAACF,MAAM,CAACD,CAAC,CAACE,WAAW,CAAC,CAAC;;AAE9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAC1C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,MAAM;IACJC,IAAI;IACJC,SAAS,GAAGpB,QAAQ,CAACO,OAAO;IAC5Bc,QAAQ,GAAGZ,iBAAiB;IAC5Ba,MAAM,GAAGC,YAAY,IAAIA,YAAY,CAACC,IAAI,CAAC,CAACd,CAAC,EAAEC,CAAC,KAAKc,gBAAgB,CAACf,CAAC,EAAEC,CAAC,EAAEU,QAAQ,CAAC;EACvF,CAAC,GAAGH,OAAO;EACX,MAAMK,YAAY,GAAGP,KAAK,CAACU,MAAM,CAACC,mBAAmB,EAAE,EAAE,CAAC;EAC1D,OAAOL,MAAM,CAACC,YAAY,CAAC,CAACK,GAAG,CAACC,IAAI,IAAI;IACtC,IAAI;MACFC;IACF,CAAC,GAAGD,IAAI;IACR,OAAOC,IAAI;EACb,CAAC,CAAC;EACF,SAASH,mBAAmBA,CAACI,OAAO,EAAED,IAAI,EAAEE,KAAK,EAAE;IACjD,MAAMC,WAAW,GAAGC,iBAAiB,CAACJ,IAAI,EAAEX,IAAI,EAAEF,KAAK,EAAEC,OAAO,CAAC;IACjE,MAAM;MACJiB,IAAI;MACJC,YAAY,GAAGhB;IACjB,CAAC,GAAGa,WAAW;IACf,IAAIE,IAAI,IAAIC,YAAY,EAAE;MACxBL,OAAO,CAACM,IAAI,CAAC;QACX,GAAGJ,WAAW;QACdH,IAAI;QACJE;MACF,CAAC,CAAC;IACJ;IACA,OAAOD,OAAO;EAChB;AACF;AACAhB,WAAW,CAACf,QAAQ,GAAGA,QAAQ;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkC,iBAAiBA,CAACJ,IAAI,EAAEX,IAAI,EAAEF,KAAK,EAAEC,OAAO,EAAE;EACrD,IAAI,CAACC,IAAI,EAAE;IACT;IACA,MAAMmB,UAAU,GAAGR,IAAI;IACvB,OAAO;MACL;MACAjB,WAAW,EAAEyB,UAAU;MACvBH,IAAI,EAAEI,eAAe,CAACD,UAAU,EAAErB,KAAK,EAAEC,OAAO,CAAC;MACjDsB,QAAQ,EAAE,CAAC,CAAC;MACZJ,YAAY,EAAElB,OAAO,CAACE;IACxB,CAAC;EACH;EACA,MAAMqB,YAAY,GAAGC,kBAAkB,CAACZ,IAAI,EAAEX,IAAI,CAAC;EACnD,OAAOsB,YAAY,CAACf,MAAM,CAAC,CAACiB,KAAK,EAAEC,KAAK,EAAEC,CAAC,KAAK;IAC9C,IAAI;MACFV,IAAI;MACJtB,WAAW;MACX2B,QAAQ;MACRJ;IACF,CAAC,GAAGO,KAAK;IACT,IAAI;MACFG,SAAS;MACTC;IACF,CAAC,GAAGH,KAAK;IACT,IAAII,OAAO,GAAGT,eAAe,CAACO,SAAS,EAAE7B,KAAK,EAAEC,OAAO,CAAC;IACxD,IAAI+B,cAAc,GAAGpC,WAAW;IAChC,MAAM;MACJqC,UAAU;MACVC,UAAU;MACV/B;IACF,CAAC,GAAG2B,UAAU;IACd,IAAIC,OAAO,GAAGE,UAAU,IAAIF,OAAO,IAAIhD,QAAQ,CAACO,OAAO,EAAE;MACvDyC,OAAO,GAAGE,UAAU;IACtB,CAAC,MAAM,IAAIF,OAAO,GAAGG,UAAU,EAAE;MAC/BH,OAAO,GAAGG,UAAU;IACtB;IACA,IAAIH,OAAO,GAAGb,IAAI,EAAE;MAClBA,IAAI,GAAGa,OAAO;MACdR,QAAQ,GAAGK,CAAC;MACZT,YAAY,GAAGhB,SAAS;MACxB6B,cAAc,GAAGH,SAAS;IAC5B;IACA,OAAO;MACLjC,WAAW,EAAEoC,cAAc;MAC3Bd,IAAI;MACJK,QAAQ;MACRJ;IACF,CAAC;EACH,CAAC,EAAE;IACDvB,WAAW,EAAEiB,IAAI;IACjBK,IAAI,EAAEnC,QAAQ,CAACQ,QAAQ;IACvBgC,QAAQ,EAAE,CAAC,CAAC;IACZJ,YAAY,EAAElB,OAAO,CAACE;EACxB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmB,eAAeA,CAACa,UAAU,EAAEC,YAAY,EAAEnC,OAAO,EAAE;EAC1DkC,UAAU,GAAGE,yBAAyB,CAACF,UAAU,EAAElC,OAAO,CAAC;EAC3DmC,YAAY,GAAGC,yBAAyB,CAACD,YAAY,EAAEnC,OAAO,CAAC;;EAE/D;EACA,IAAImC,YAAY,CAACE,MAAM,GAAGH,UAAU,CAACG,MAAM,EAAE;IAC3C,OAAOvD,QAAQ,CAACQ,QAAQ;EAC1B;;EAEA;EACA,IAAI4C,UAAU,KAAKC,YAAY,EAAE;IAC/B,OAAOrD,QAAQ,CAACC,oBAAoB;EACtC;;EAEA;EACAmD,UAAU,GAAGA,UAAU,CAACI,WAAW,CAAC,CAAC;EACrCH,YAAY,GAAGA,YAAY,CAACG,WAAW,CAAC,CAAC;;EAEzC;EACA,IAAIJ,UAAU,KAAKC,YAAY,EAAE;IAC/B,OAAOrD,QAAQ,CAACE,KAAK;EACvB;;EAEA;EACA,IAAIkD,UAAU,CAACK,UAAU,CAACJ,YAAY,CAAC,EAAE;IACvC,OAAOrD,QAAQ,CAACG,WAAW;EAC7B;;EAEA;EACA,IAAIiD,UAAU,CAACM,QAAQ,CAAC,IAAIL,YAAY,EAAE,CAAC,EAAE;IAC3C,OAAOrD,QAAQ,CAACI,gBAAgB;EAClC;;EAEA;EACA,IAAIgD,UAAU,CAACM,QAAQ,CAACL,YAAY,CAAC,EAAE;IACrC,OAAOrD,QAAQ,CAACK,QAAQ;EAC1B,CAAC,MAAM,IAAIgD,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;IACpC;IACA;IACA;IACA,OAAOvD,QAAQ,CAACQ,QAAQ;EAC1B;;EAEA;EACA,IAAImD,UAAU,CAACP,UAAU,CAAC,CAACM,QAAQ,CAACL,YAAY,CAAC,EAAE;IACjD,OAAOrD,QAAQ,CAACM,OAAO;EACzB;;EAEA;EACA;EACA,OAAOsD,mBAAmB,CAACR,UAAU,EAAEC,YAAY,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,UAAUA,CAACE,MAAM,EAAE;EAC1B,IAAIC,OAAO,GAAG,EAAE;EAChB,MAAMC,aAAa,GAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC;EACvCD,aAAa,CAACE,OAAO,CAACC,YAAY,IAAI;IACpC,MAAMC,kBAAkB,GAAGD,YAAY,CAACF,KAAK,CAAC,GAAG,CAAC;IAClDG,kBAAkB,CAACF,OAAO,CAACG,iBAAiB,IAAI;MAC9CN,OAAO,IAAIM,iBAAiB,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOP,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,mBAAmBA,CAACR,UAAU,EAAEC,YAAY,EAAE;EACrD,IAAIiB,wBAAwB,GAAG,CAAC;EAChC,IAAIC,UAAU,GAAG,CAAC;EAClB,SAASC,qBAAqBA,CAACC,SAAS,EAAEZ,MAAM,EAAE7B,KAAK,EAAE;IACvD,KAAK,IAAI0C,CAAC,GAAG1C,KAAK,EAAE2C,CAAC,GAAGd,MAAM,CAACN,MAAM,EAAEmB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjD,MAAME,UAAU,GAAGf,MAAM,CAACa,CAAC,CAAC;MAC5B,IAAIE,UAAU,KAAKH,SAAS,EAAE;QAC5BH,wBAAwB,IAAI,CAAC;QAC7B,OAAOI,CAAC,GAAG,CAAC;MACd;IACF;IACA,OAAO,CAAC,CAAC;EACX;EACA,SAASG,UAAUA,CAACC,MAAM,EAAE;IAC1B,MAAMC,gBAAgB,GAAG,CAAC,GAAGD,MAAM;IACnC,MAAME,iBAAiB,GAAGV,wBAAwB,GAAGjB,YAAY,CAACE,MAAM;IACxE,MAAM0B,OAAO,GAAGjF,QAAQ,CAACO,OAAO,GAAGyE,iBAAiB,GAAGD,gBAAgB;IACvE,OAAOE,OAAO;EAChB;EACA,MAAMC,UAAU,GAAGV,qBAAqB,CAACnB,YAAY,CAAC,CAAC,CAAC,EAAED,UAAU,EAAE,CAAC,CAAC;EACxE,IAAI8B,UAAU,GAAG,CAAC,EAAE;IAClB,OAAOlF,QAAQ,CAACQ,QAAQ;EAC1B;EACA+D,UAAU,GAAGW,UAAU;EACvB,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEsC,CAAC,GAAG9B,YAAY,CAACE,MAAM,EAAEV,CAAC,GAAGsC,CAAC,EAAEtC,CAAC,EAAE,EAAE;IACnD,MAAM4B,SAAS,GAAGpB,YAAY,CAACR,CAAC,CAAC;IACjC0B,UAAU,GAAGC,qBAAqB,CAACC,SAAS,EAAErB,UAAU,EAAEmB,UAAU,CAAC;IACrE,MAAMa,KAAK,GAAGb,UAAU,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACa,KAAK,EAAE;MACV,OAAOpF,QAAQ,CAACQ,QAAQ;IAC1B;EACF;EACA,MAAMsE,MAAM,GAAGP,UAAU,GAAGW,UAAU;EACtC,OAAOL,UAAU,CAACC,MAAM,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASrD,gBAAgBA,CAACf,CAAC,EAAEC,CAAC,EAAEU,QAAQ,EAAE;EACxC,MAAMgE,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,MAAM,GAAG,CAAC;EAChB,MAAM;IACJnD,IAAI,EAAEoD,KAAK;IACX/C,QAAQ,EAAEgD;EACZ,CAAC,GAAG9E,CAAC;EACL,MAAM;IACJyB,IAAI,EAAEsD,KAAK;IACXjD,QAAQ,EAAEkD;EACZ,CAAC,GAAG/E,CAAC;EACL,MAAMgF,IAAI,GAAGJ,KAAK,KAAKE,KAAK;EAC5B,IAAIE,IAAI,EAAE;IACR,IAAIH,SAAS,KAAKE,SAAS,EAAE;MAC3B;MACA,OAAOrE,QAAQ,CAACX,CAAC,EAAEC,CAAC,CAAC;IACvB,CAAC,MAAM;MACL,OAAO6E,SAAS,GAAGE,SAAS,GAAGL,MAAM,GAAGC,MAAM;IAChD;EACF,CAAC,MAAM;IACL,OAAOC,KAAK,GAAGE,KAAK,GAAGJ,MAAM,GAAGC,MAAM;EACxC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAShC,yBAAyBA,CAACrC,KAAK,EAAE2E,KAAK,EAAE;EAC/C,IAAI;IACFC;EACF,CAAC,GAAGD,KAAK;EACT;EACA;EACA3E,KAAK,GAAG,GAAGA,KAAK,EAAE,CAAC,CAAC;EACpB,IAAI,CAAC4E,cAAc,EAAE;IACnB5E,KAAK,GAAGlB,aAAa,CAACkB,KAAK,CAAC;EAC9B;EACA,OAAOA,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6E,aAAaA,CAAChE,IAAI,EAAEiE,GAAG,EAAE;EAChC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAGA,GAAG,CAACA,GAAG;EACf;EACA,IAAI9E,KAAK;EACT,IAAI,OAAO8E,GAAG,KAAK,UAAU,EAAE;IAC7B9E,KAAK,GAAG8E,GAAG,CAACjE,IAAI,CAAC;EACnB,CAAC,MAAM,IAAIA,IAAI,IAAI,IAAI,EAAE;IACvBb,KAAK,GAAG,IAAI;EACd,CAAC,MAAM,IAAI+E,MAAM,CAACC,cAAc,CAACC,IAAI,CAACpE,IAAI,EAAEiE,GAAG,CAAC,EAAE;IAChD9E,KAAK,GAAGa,IAAI,CAACiE,GAAG,CAAC;EACnB,CAAC,MAAM,IAAIA,GAAG,CAACrC,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC5B;IACA,OAAOyC,eAAe,CAACJ,GAAG,EAAEjE,IAAI,CAAC;EACnC,CAAC,MAAM;IACLb,KAAK,GAAG,IAAI;EACd;;EAEA;EACA,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,OAAO,EAAE;EACX;EACA,IAAImF,KAAK,CAACC,OAAO,CAACpF,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK;EACd;EACA,OAAO,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkF,eAAeA,CAACG,IAAI,EAAExE,IAAI,EAAE;EACnC,MAAMX,IAAI,GAAGmF,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC;EAC5B,IAAIuC,MAAM,GAAG,CAACzE,IAAI,CAAC;EACnB,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEsC,CAAC,GAAGhE,IAAI,CAACoC,MAAM,EAAEV,CAAC,GAAGsC,CAAC,EAAEtC,CAAC,EAAE,EAAE;IAC3C,MAAM2D,SAAS,GAAGrF,IAAI,CAAC0B,CAAC,CAAC;IACzB,IAAI4D,YAAY,GAAG,EAAE;IACrB,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG4B,MAAM,CAAChD,MAAM,EAAEmB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMgC,UAAU,GAAGH,MAAM,CAAC7B,CAAC,CAAC;MAC5B,IAAIgC,UAAU,IAAI,IAAI,EAAE;MACxB,IAAIV,MAAM,CAACC,cAAc,CAACC,IAAI,CAACQ,UAAU,EAAEF,SAAS,CAAC,EAAE;QACrD,MAAMG,WAAW,GAAGD,UAAU,CAACF,SAAS,CAAC;QACzC,IAAIG,WAAW,IAAI,IAAI,EAAE;UACvBF,YAAY,CAACpE,IAAI,CAACsE,WAAW,CAAC;QAChC;MACF,CAAC,MAAM,IAAIH,SAAS,KAAK,GAAG,EAAE;QAC5B;QACAC,YAAY,GAAGA,YAAY,CAACG,MAAM,CAACF,UAAU,CAAC;MAChD;IACF;IACAH,MAAM,GAAGE,YAAY;EACvB;EACA,IAAIL,KAAK,CAACC,OAAO,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5B;IACA;IACA,MAAMM,MAAM,GAAG,EAAE;IACjB,OAAOA,MAAM,CAACD,MAAM,CAAC,GAAGL,MAAM,CAAC;EACjC;EACA;EACA;EACA,OAAOA,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS7D,kBAAkBA,CAACZ,IAAI,EAAEX,IAAI,EAAE;EACtC,MAAM2F,SAAS,GAAG,EAAE;EACpB,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGxD,IAAI,CAACoC,MAAM,EAAEmB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAC3C,MAAMqB,GAAG,GAAG5E,IAAI,CAACuD,CAAC,CAAC;IACnB,MAAM3B,UAAU,GAAGgE,gBAAgB,CAAChB,GAAG,CAAC;IACxC,MAAMiB,UAAU,GAAGlB,aAAa,CAAChE,IAAI,EAAEiE,GAAG,CAAC;IAC3C,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEsC,CAAC,GAAG6B,UAAU,CAACzD,MAAM,EAAEV,CAAC,GAAGsC,CAAC,EAAEtC,CAAC,EAAE,EAAE;MACjDiE,SAAS,CAACzE,IAAI,CAAC;QACbS,SAAS,EAAEkE,UAAU,CAACnE,CAAC,CAAC;QACxBE;MACF,CAAC,CAAC;IACJ;EACF;EACA,OAAO+D,SAAS;AAClB;AACA,MAAMG,oBAAoB,GAAG;EAC3B9D,UAAU,EAAE+D,QAAQ;EACpBhE,UAAU,EAAE,CAACgE;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAASH,gBAAgBA,CAAChB,GAAG,EAAE;EAC7B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAOkB,oBAAoB;EAC7B;EACA,OAAO;IACL,GAAGA,oBAAoB;IACvB,GAAGlB;EACL,CAAC;AACH;;AAEA;AACA;AACA;AACA;;AAEA,SAAStF,iBAAiB,EAAEM,WAAW,EAAEf,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
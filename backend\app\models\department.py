from datetime import datetime
from app import db, ma

class Department(db.Model):
    """Department model."""
    __tablename__ = 'departments'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(10), nullable=False, unique=True)
    description = db.Column(db.Text)
    hod_id = db.Column(db.Integer, db.<PERSON>ey('faculty.id'), nullable=True)
    established_year = db.Column(db.Integer)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    hod = db.relationship('Faculty', foreign_keys=[hod_id], backref='headed_department')
    students = db.relationship('Student', backref='department', lazy='dynamic')
    faculty = db.relationship('Faculty', foreign_keys='Faculty.department_id', 
                             backref='department', lazy='dynamic')
    courses = db.relationship('Course', backref='department', lazy='dynamic')
    notices = db.relationship('Notice', backref='department', lazy='dynamic')
    
    def to_dict(self):
        """Convert department to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'hod_id': self.hod_id,
            'hod_name': self.hod.full_name if self.hod else None,
            'established_year': self.established_year,
            'is_active': self.is_active,
            'student_count': self.students.filter_by(is_active=True).count(),
            'faculty_count': self.faculty.filter_by(is_active=True).count(),
            'course_count': self.courses.filter_by(is_active=True).count(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Department {self.name}>'

class DepartmentSchema(ma.SQLAlchemyAutoSchema):
    """Department serialization schema."""
    class Meta:
        model = Department
        load_instance = True
        include_fk = True

department_schema = DepartmentSchema()
departments_schema = DepartmentSchema(many=True)

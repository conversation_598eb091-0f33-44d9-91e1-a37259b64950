{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "transformStatementList", "parentPath", "paths", "path", "isFunctionDeclaration", "func", "node", "declar", "t", "variableDeclaration", "variableDeclarator", "id", "toExpression", "_blockHoist", "replaceWith", "name", "visitor", "BlockStatement", "parent", "isFunction", "body", "isExportDeclaration", "get", "SwitchCase"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, type NodePath } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  function transformStatementList(\n    parentPath: NodePath,\n    paths: NodePath<t.Statement>[],\n  ) {\n    if (process.env.BABEL_8_BREAKING) {\n      // eslint-disable-next-line no-var\n      var isInStrictMode = parentPath.isInStrictMode();\n    }\n\n    for (const path of paths) {\n      if (!path.isFunctionDeclaration()) continue;\n\n      if (\n        process.env.BABEL_8_BREAKING &&\n        !isInStrictMode &&\n        !(\n          path.node.async ||\n          path.node.generator ||\n          path.getData(\n            \"@babel/plugin-transform-async-generator-functions/async_generator_function\",\n          )\n        )\n      ) {\n        continue;\n      }\n\n      const func = path.node;\n      const declar = t.variableDeclaration(\"let\", [\n        t.variableDeclarator(func.id, t.toExpression(func)),\n      ]);\n\n      // hoist it up above everything else\n      // @ts-expect-error todo(flow->ts): avoid mutations\n      declar._blockHoist = 2;\n\n      // todo: name this\n      func.id = null;\n\n      path.replaceWith(declar);\n    }\n  }\n\n  return {\n    name: \"transform-block-scoped-functions\",\n\n    visitor: {\n      BlockStatement(path) {\n        const { node, parent } = path;\n        if (\n          t.isFunction(parent, { body: node }) ||\n          t.isExportDeclaration(parent)\n        ) {\n          return;\n        }\n\n        transformStatementList(path, path.get(\"body\"));\n      },\n\n      SwitchCase(path) {\n        transformStatementList(path, path.get(\"consequent\"));\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAwD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEzC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,SAASC,sBAAsBA,CAC7BC,UAAoB,EACpBC,KAA8B,EAC9B;IAAA;IAMA,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,IAAI,CAACC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAE;MAAS;MAgB5C,MAAMC,IAAI,GAAGF,IAAI,CAACG,IAAI;MACtB,MAAMC,MAAM,GAAGC,WAAC,CAACC,mBAAmB,CAAC,KAAK,EAAE,CAC1CD,WAAC,CAACE,kBAAkB,CAACL,IAAI,CAACM,EAAE,EAAEH,WAAC,CAACI,YAAY,CAACP,IAAI,CAAC,CAAC,CACpD,CAAC;MAIFE,MAAM,CAACM,WAAW,GAAG,CAAC;MAGtBR,IAAI,CAACM,EAAE,GAAG,IAAI;MAEdR,IAAI,CAACW,WAAW,CAACP,MAAM,CAAC;IAC1B;EACF;EAEA,OAAO;IACLQ,IAAI,EAAE,kCAAkC;IAExCC,OAAO,EAAE;MACPC,cAAcA,CAACd,IAAI,EAAE;QACnB,MAAM;UAAEG,IAAI;UAAEY;QAAO,CAAC,GAAGf,IAAI;QAC7B,IACEK,WAAC,CAACW,UAAU,CAACD,MAAM,EAAE;UAAEE,IAAI,EAAEd;QAAK,CAAC,CAAC,IACpCE,WAAC,CAACa,mBAAmB,CAACH,MAAM,CAAC,EAC7B;UACA;QACF;QAEAlB,sBAAsB,CAACG,IAAI,EAAEA,IAAI,CAACmB,GAAG,CAAC,MAAM,CAAC,CAAC;MAChD,CAAC;MAEDC,UAAUA,CAACpB,IAAI,EAAE;QACfH,sBAAsB,CAACG,IAAI,EAAEA,IAAI,CAACmB,GAAG,CAAC,YAAY,CAAC,CAAC;MACtD;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}
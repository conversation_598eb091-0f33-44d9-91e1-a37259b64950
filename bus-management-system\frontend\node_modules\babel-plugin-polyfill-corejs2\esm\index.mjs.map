{"version": 3, "file": "index.mjs", "sources": ["../src/built-in-definitions.ts", "../src/add-platform-specific-polyfills.ts", "../src/helpers.ts", "../src/index.ts"], "sourcesContent": ["import corejs2Polyfills from \"@babel/compat-data/corejs2-built-ins\";\n\ntype ObjectMap<V> = { [name: string]: V };\n\ntype PolyfillDescriptor<T> = {\n  name: string;\n  pure: string | null;\n  global: string[];\n  meta: T | null;\n};\n\ntype CoreJS2Meta = {\n  minRuntimeVersion: string | null;\n};\n\nconst define = <T>(\n  name: string,\n  pure?: string | null,\n  global: string[] = [],\n  meta?: T | null,\n): PolyfillDescriptor<T> => {\n  return { name, pure, global, meta };\n};\n\nconst pureAndGlobal = (\n  pure: string,\n  global: string[],\n  minRuntimeVersion: string | null = null,\n) => define<CoreJS2Meta>(global[0], pure, global, { minRuntimeVersion });\n\nconst globalOnly = (global: string[]) =>\n  define<CoreJS2Meta>(global[0], null, global);\n\nconst pureOnly = (pure: string, name: string) =>\n  define<CoreJS2Meta>(name, pure, []);\n\nconst ArrayNatureIterators = [\n  \"es6.object.to-string\",\n  \"es6.array.iterator\",\n  \"web.dom.iterable\",\n];\n\nexport const CommonIterators = [\"es6.string.iterator\", ...ArrayNatureIterators];\n\nconst PromiseDependencies = [\"es6.object.to-string\", \"es6.promise\"];\n\nexport const BuiltIns: ObjectMap<PolyfillDescriptor<CoreJS2Meta>> = {\n  DataView: globalOnly([\"es6.typed.data-view\"]),\n  Float32Array: globalOnly([\"es6.typed.float32-array\"]),\n  Float64Array: globalOnly([\"es6.typed.float64-array\"]),\n  Int8Array: globalOnly([\"es6.typed.int8-array\"]),\n  Int16Array: globalOnly([\"es6.typed.int16-array\"]),\n  Int32Array: globalOnly([\"es6.typed.int32-array\"]),\n  Map: pureAndGlobal(\"map\", [\"es6.map\", ...CommonIterators]),\n  Number: globalOnly([\"es6.number.constructor\"]),\n  Promise: pureAndGlobal(\"promise\", PromiseDependencies),\n  RegExp: globalOnly([\"es6.regexp.constructor\"]),\n  Set: pureAndGlobal(\"set\", [\"es6.set\", ...CommonIterators]),\n  Symbol: pureAndGlobal(\"symbol/index\", [\"es6.symbol\"]),\n  Uint8Array: globalOnly([\"es6.typed.uint8-array\"]),\n  Uint8ClampedArray: globalOnly([\"es6.typed.uint8-clamped-array\"]),\n  Uint16Array: globalOnly([\"es6.typed.uint16-array\"]),\n  Uint32Array: globalOnly([\"es6.typed.uint32-array\"]),\n  WeakMap: pureAndGlobal(\"weak-map\", [\"es6.weak-map\", ...CommonIterators]),\n  WeakSet: pureAndGlobal(\"weak-set\", [\"es6.weak-set\", ...CommonIterators]),\n\n  setImmediate: pureOnly(\"set-immediate\", \"web.immediate\"),\n  clearImmediate: pureOnly(\"clear-immediate\", \"web.immediate\"),\n  parseFloat: pureOnly(\"parse-float\", \"es6.parse-float\"),\n  parseInt: pureOnly(\"parse-int\", \"es6.parse-int\"),\n};\n\nexport const InstanceProperties: ObjectMap<PolyfillDescriptor<CoreJS2Meta>> = {\n  __defineGetter__: globalOnly([\"es7.object.define-getter\"]),\n  __defineSetter__: globalOnly([\"es7.object.define-setter\"]),\n  __lookupGetter__: globalOnly([\"es7.object.lookup-getter\"]),\n  __lookupSetter__: globalOnly([\"es7.object.lookup-setter\"]),\n  anchor: globalOnly([\"es6.string.anchor\"]),\n  big: globalOnly([\"es6.string.big\"]),\n  bind: globalOnly([\"es6.function.bind\"]),\n  blink: globalOnly([\"es6.string.blink\"]),\n  bold: globalOnly([\"es6.string.bold\"]),\n  codePointAt: globalOnly([\"es6.string.code-point-at\"]),\n  copyWithin: globalOnly([\"es6.array.copy-within\"]),\n  endsWith: globalOnly([\"es6.string.ends-with\"]),\n  entries: globalOnly(ArrayNatureIterators),\n  every: globalOnly([\"es6.array.every\"]),\n  fill: globalOnly([\"es6.array.fill\"]),\n  filter: globalOnly([\"es6.array.filter\"]),\n  finally: globalOnly([\"es7.promise.finally\", ...PromiseDependencies]),\n  find: globalOnly([\"es6.array.find\"]),\n  findIndex: globalOnly([\"es6.array.find-index\"]),\n  fixed: globalOnly([\"es6.string.fixed\"]),\n  flags: globalOnly([\"es6.regexp.flags\"]),\n  flatMap: globalOnly([\"es7.array.flat-map\"]),\n  fontcolor: globalOnly([\"es6.string.fontcolor\"]),\n  fontsize: globalOnly([\"es6.string.fontsize\"]),\n  forEach: globalOnly([\"es6.array.for-each\"]),\n  includes: globalOnly([\"es6.string.includes\", \"es7.array.includes\"]),\n  indexOf: globalOnly([\"es6.array.index-of\"]),\n  italics: globalOnly([\"es6.string.italics\"]),\n  keys: globalOnly(ArrayNatureIterators),\n  lastIndexOf: globalOnly([\"es6.array.last-index-of\"]),\n  link: globalOnly([\"es6.string.link\"]),\n  map: globalOnly([\"es6.array.map\"]),\n  match: globalOnly([\"es6.regexp.match\"]),\n  name: globalOnly([\"es6.function.name\"]),\n  padStart: globalOnly([\"es7.string.pad-start\"]),\n  padEnd: globalOnly([\"es7.string.pad-end\"]),\n  reduce: globalOnly([\"es6.array.reduce\"]),\n  reduceRight: globalOnly([\"es6.array.reduce-right\"]),\n  repeat: globalOnly([\"es6.string.repeat\"]),\n  replace: globalOnly([\"es6.regexp.replace\"]),\n  search: globalOnly([\"es6.regexp.search\"]),\n  small: globalOnly([\"es6.string.small\"]),\n  some: globalOnly([\"es6.array.some\"]),\n  sort: globalOnly([\"es6.array.sort\"]),\n  split: globalOnly([\"es6.regexp.split\"]),\n  startsWith: globalOnly([\"es6.string.starts-with\"]),\n  strike: globalOnly([\"es6.string.strike\"]),\n  sub: globalOnly([\"es6.string.sub\"]),\n  sup: globalOnly([\"es6.string.sup\"]),\n  toISOString: globalOnly([\"es6.date.to-iso-string\"]),\n  toJSON: globalOnly([\"es6.date.to-json\"]),\n  toString: globalOnly([\n    \"es6.object.to-string\",\n    \"es6.date.to-string\",\n    \"es6.regexp.to-string\",\n  ]),\n  trim: globalOnly([\"es6.string.trim\"]),\n  trimEnd: globalOnly([\"es7.string.trim-right\"]),\n  trimLeft: globalOnly([\"es7.string.trim-left\"]),\n  trimRight: globalOnly([\"es7.string.trim-right\"]),\n  trimStart: globalOnly([\"es7.string.trim-left\"]),\n  values: globalOnly(ArrayNatureIterators),\n};\n\n// This isn't present in older @babel/compat-data versions\nif (\"es6.array.slice\" in corejs2Polyfills) {\n  InstanceProperties.slice = globalOnly([\"es6.array.slice\"]);\n}\n\nexport const StaticProperties: ObjectMap<\n  ObjectMap<PolyfillDescriptor<CoreJS2Meta>>\n> = {\n  Array: {\n    from: pureAndGlobal(\"array/from\", [\n      \"es6.symbol\",\n      \"es6.array.from\",\n      ...CommonIterators,\n    ]),\n    isArray: pureAndGlobal(\"array/is-array\", [\"es6.array.is-array\"]),\n    of: pureAndGlobal(\"array/of\", [\"es6.array.of\"]),\n  },\n\n  Date: {\n    now: pureAndGlobal(\"date/now\", [\"es6.date.now\"]),\n  },\n\n  JSON: {\n    stringify: pureOnly(\"json/stringify\", \"es6.symbol\"),\n  },\n\n  Math: {\n    // 'Math' was not included in the 7.0.0\n    // release of '@babel/runtime'. See issue https://github.com/babel/babel/pull/8616.\n    acosh: pureAndGlobal(\"math/acosh\", [\"es6.math.acosh\"], \"7.0.1\"),\n    asinh: pureAndGlobal(\"math/asinh\", [\"es6.math.asinh\"], \"7.0.1\"),\n    atanh: pureAndGlobal(\"math/atanh\", [\"es6.math.atanh\"], \"7.0.1\"),\n    cbrt: pureAndGlobal(\"math/cbrt\", [\"es6.math.cbrt\"], \"7.0.1\"),\n    clz32: pureAndGlobal(\"math/clz32\", [\"es6.math.clz32\"], \"7.0.1\"),\n    cosh: pureAndGlobal(\"math/cosh\", [\"es6.math.cosh\"], \"7.0.1\"),\n    expm1: pureAndGlobal(\"math/expm1\", [\"es6.math.expm1\"], \"7.0.1\"),\n    fround: pureAndGlobal(\"math/fround\", [\"es6.math.fround\"], \"7.0.1\"),\n    hypot: pureAndGlobal(\"math/hypot\", [\"es6.math.hypot\"], \"7.0.1\"),\n    imul: pureAndGlobal(\"math/imul\", [\"es6.math.imul\"], \"7.0.1\"),\n    log1p: pureAndGlobal(\"math/log1p\", [\"es6.math.log1p\"], \"7.0.1\"),\n    log10: pureAndGlobal(\"math/log10\", [\"es6.math.log10\"], \"7.0.1\"),\n    log2: pureAndGlobal(\"math/log2\", [\"es6.math.log2\"], \"7.0.1\"),\n    sign: pureAndGlobal(\"math/sign\", [\"es6.math.sign\"], \"7.0.1\"),\n    sinh: pureAndGlobal(\"math/sinh\", [\"es6.math.sinh\"], \"7.0.1\"),\n    tanh: pureAndGlobal(\"math/tanh\", [\"es6.math.tanh\"], \"7.0.1\"),\n    trunc: pureAndGlobal(\"math/trunc\", [\"es6.math.trunc\"], \"7.0.1\"),\n  },\n\n  Number: {\n    EPSILON: pureAndGlobal(\"number/epsilon\", [\"es6.number.epsilon\"]),\n    MIN_SAFE_INTEGER: pureAndGlobal(\"number/min-safe-integer\", [\n      \"es6.number.min-safe-integer\",\n    ]),\n    MAX_SAFE_INTEGER: pureAndGlobal(\"number/max-safe-integer\", [\n      \"es6.number.max-safe-integer\",\n    ]),\n    isFinite: pureAndGlobal(\"number/is-finite\", [\"es6.number.is-finite\"]),\n    isInteger: pureAndGlobal(\"number/is-integer\", [\"es6.number.is-integer\"]),\n    isSafeInteger: pureAndGlobal(\"number/is-safe-integer\", [\n      \"es6.number.is-safe-integer\",\n    ]),\n    isNaN: pureAndGlobal(\"number/is-nan\", [\"es6.number.is-nan\"]),\n    parseFloat: pureAndGlobal(\"number/parse-float\", [\"es6.number.parse-float\"]),\n    parseInt: pureAndGlobal(\"number/parse-int\", [\"es6.number.parse-int\"]),\n  },\n\n  Object: {\n    assign: pureAndGlobal(\"object/assign\", [\"es6.object.assign\"]),\n    create: pureAndGlobal(\"object/create\", [\"es6.object.create\"]),\n    defineProperties: pureAndGlobal(\"object/define-properties\", [\n      \"es6.object.define-properties\",\n    ]),\n    defineProperty: pureAndGlobal(\"object/define-property\", [\n      \"es6.object.define-property\",\n    ]),\n    entries: pureAndGlobal(\"object/entries\", [\"es7.object.entries\"]),\n    freeze: pureAndGlobal(\"object/freeze\", [\"es6.object.freeze\"]),\n    getOwnPropertyDescriptor: pureAndGlobal(\n      \"object/get-own-property-descriptor\",\n      [\"es6.object.get-own-property-descriptor\"],\n    ),\n    getOwnPropertyDescriptors: pureAndGlobal(\n      \"object/get-own-property-descriptors\",\n      [\"es7.object.get-own-property-descriptors\"],\n    ),\n    getOwnPropertyNames: pureAndGlobal(\"object/get-own-property-names\", [\n      \"es6.object.get-own-property-names\",\n    ]),\n    getOwnPropertySymbols: pureAndGlobal(\"object/get-own-property-symbols\", [\n      \"es6.symbol\",\n    ]),\n    getPrototypeOf: pureAndGlobal(\"object/get-prototype-of\", [\n      \"es6.object.get-prototype-of\",\n    ]),\n    is: pureAndGlobal(\"object/is\", [\"es6.object.is\"]),\n    isExtensible: pureAndGlobal(\"object/is-extensible\", [\n      \"es6.object.is-extensible\",\n    ]),\n    isFrozen: pureAndGlobal(\"object/is-frozen\", [\"es6.object.is-frozen\"]),\n    isSealed: pureAndGlobal(\"object/is-sealed\", [\"es6.object.is-sealed\"]),\n    keys: pureAndGlobal(\"object/keys\", [\"es6.object.keys\"]),\n    preventExtensions: pureAndGlobal(\"object/prevent-extensions\", [\n      \"es6.object.prevent-extensions\",\n    ]),\n    seal: pureAndGlobal(\"object/seal\", [\"es6.object.seal\"]),\n    setPrototypeOf: pureAndGlobal(\"object/set-prototype-of\", [\n      \"es6.object.set-prototype-of\",\n    ]),\n    values: pureAndGlobal(\"object/values\", [\"es7.object.values\"]),\n  },\n\n  Promise: {\n    all: globalOnly(CommonIterators),\n    race: globalOnly(CommonIterators),\n  },\n\n  Reflect: {\n    apply: pureAndGlobal(\"reflect/apply\", [\"es6.reflect.apply\"]),\n    construct: pureAndGlobal(\"reflect/construct\", [\"es6.reflect.construct\"]),\n    defineProperty: pureAndGlobal(\"reflect/define-property\", [\n      \"es6.reflect.define-property\",\n    ]),\n    deleteProperty: pureAndGlobal(\"reflect/delete-property\", [\n      \"es6.reflect.delete-property\",\n    ]),\n    get: pureAndGlobal(\"reflect/get\", [\"es6.reflect.get\"]),\n    getOwnPropertyDescriptor: pureAndGlobal(\n      \"reflect/get-own-property-descriptor\",\n      [\"es6.reflect.get-own-property-descriptor\"],\n    ),\n    getPrototypeOf: pureAndGlobal(\"reflect/get-prototype-of\", [\n      \"es6.reflect.get-prototype-of\",\n    ]),\n    has: pureAndGlobal(\"reflect/has\", [\"es6.reflect.has\"]),\n    isExtensible: pureAndGlobal(\"reflect/is-extensible\", [\n      \"es6.reflect.is-extensible\",\n    ]),\n    ownKeys: pureAndGlobal(\"reflect/own-keys\", [\"es6.reflect.own-keys\"]),\n    preventExtensions: pureAndGlobal(\"reflect/prevent-extensions\", [\n      \"es6.reflect.prevent-extensions\",\n    ]),\n    set: pureAndGlobal(\"reflect/set\", [\"es6.reflect.set\"]),\n    setPrototypeOf: pureAndGlobal(\"reflect/set-prototype-of\", [\n      \"es6.reflect.set-prototype-of\",\n    ]),\n  },\n\n  String: {\n    at: pureOnly(\"string/at\", \"es7.string.at\"),\n    fromCodePoint: pureAndGlobal(\"string/from-code-point\", [\n      \"es6.string.from-code-point\",\n    ]),\n    raw: pureAndGlobal(\"string/raw\", [\"es6.string.raw\"]),\n  },\n\n  Symbol: {\n    // FIXME: Pure disabled to work around zloirock/core-js#262.\n    asyncIterator: globalOnly([\"es6.symbol\", \"es7.symbol.async-iterator\"]),\n    for: pureOnly(\"symbol/for\", \"es6.symbol\"),\n    hasInstance: pureOnly(\"symbol/has-instance\", \"es6.symbol\"),\n    isConcatSpreadable: pureOnly(\"symbol/is-concat-spreadable\", \"es6.symbol\"),\n    iterator: define(\"es6.symbol\", \"symbol/iterator\", CommonIterators),\n    keyFor: pureOnly(\"symbol/key-for\", \"es6.symbol\"),\n    match: pureAndGlobal(\"symbol/match\", [\"es6.regexp.match\"]),\n    replace: pureOnly(\"symbol/replace\", \"es6.symbol\"),\n    search: pureOnly(\"symbol/search\", \"es6.symbol\"),\n    species: pureOnly(\"symbol/species\", \"es6.symbol\"),\n    split: pureOnly(\"symbol/split\", \"es6.symbol\"),\n    toPrimitive: pureOnly(\"symbol/to-primitive\", \"es6.symbol\"),\n    toStringTag: pureOnly(\"symbol/to-string-tag\", \"es6.symbol\"),\n    unscopables: pureOnly(\"symbol/unscopables\", \"es6.symbol\"),\n  },\n};\n", "import type { Targets } from \"@babel/helper-define-polyfill-provider\";\n\nconst webPolyfills = {\n  \"web.timers\": {},\n  \"web.immediate\": {},\n  \"web.dom.iterable\": {},\n};\n\nconst purePolyfills = {\n  \"es6.parse-float\": {},\n  \"es6.parse-int\": {},\n  \"es7.string.at\": {},\n};\n\nexport default function (targets: Targets, method: string, polyfills: any) {\n  const targetNames = Object.keys(targets);\n  const isAnyTarget = !targetNames.length;\n  const isWebTarget = targetNames.some(name => name !== \"node\");\n\n  return {\n    ...polyfills,\n    ...(method === \"usage-pure\" ? purePolyfills : null),\n    ...(isAnyTarget || isWebTarget ? webPolyfills : null),\n  };\n}\n", "import semver from \"semver\";\n\nexport function hasMinVersion(\n  minVersion?: string | null,\n  runtimeVersion?: string | number | null,\n) {\n  // If the range is unavailable, we're running the script during Babel's\n  // build process, and we want to assume that all versions are satisfied so\n  // that the built output will include all definitions.\n  if (!runtimeVersion || !minVersion) return true;\n\n  runtimeVersion = String(runtimeVersion);\n\n  // semver.intersects() has some surprising behavior with comparing ranges\n  // with preprelease versions. We add '^' to ensure that we are always\n  // comparing ranges with ranges, which sidesteps this logic.\n  // For example:\n  //\n  //   semver.intersects(`<7.0.1`, \"7.0.0-beta.0\") // false - surprising\n  //   semver.intersects(`<7.0.1`, \"^7.0.0-beta.0\") // true - expected\n  //\n  // This is because the first falls back to\n  //\n  //   semver.satisfies(\"7.0.0-beta.0\", `<7.0.1`) // false - surprising\n  //\n  // and this fails because a prerelease version can only satisfy a range\n  // if it is a prerelease within the same major/minor/patch range.\n  //\n  // Note: If this is found to have issues, please also revist the logic in\n  // babel-core's availableHelper() API.\n  if (semver.valid(runtimeVersion)) runtimeVersion = `^${runtimeVersion}`;\n\n  return (\n    !semver.intersects(`<${minVersion}`, runtimeVersion) &&\n    !semver.intersects(`>=8.0.0`, runtimeVersion)\n  );\n}\n", "import corejs2Polyfills from \"@babel/compat-data/corejs2-built-ins\";\nimport {\n  BuiltIns,\n  StaticProperties,\n  InstanceProperties,\n  CommonIterators,\n} from \"./built-in-definitions\";\nimport addPlatformSpecificPolyfills from \"./add-platform-specific-polyfills\";\nimport { hasMinVersion } from \"./helpers\";\n\nimport defineProvider from \"@babel/helper-define-polyfill-provider\";\nimport type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\n\nconst BABEL_RUNTIME = \"@babel/runtime-corejs2\";\n\nconst presetEnvCompat = \"#__secret_key__@babel/preset-env__compatibility\";\nconst runtimeCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\nconst has = Function.call.bind(Object.hasOwnProperty);\n\ntype Options = {\n  [presetEnvCompat]?: {\n    entryInjectRegenerator: boolean;\n    noRuntimeName: boolean;\n  };\n  [runtimeCompat]?: {\n    useBabelRuntime: boolean;\n    runtimeVersion: string;\n    ext: string;\n  };\n};\n\nexport default defineProvider<Options>(function (\n  api,\n  {\n    [presetEnvCompat]: {\n      entryInjectRegenerator = false,\n      noRuntimeName = false,\n    } = {},\n    [runtimeCompat]: {\n      useBabelRuntime = false,\n      runtimeVersion = \"\",\n      ext = \".js\",\n    } = {},\n  },\n) {\n  const resolve = api.createMetaResolver({\n    global: BuiltIns,\n    static: StaticProperties,\n    instance: InstanceProperties,\n  });\n\n  const { debug, shouldInjectPolyfill, method } = api;\n\n  const polyfills = addPlatformSpecificPolyfills(\n    api.targets,\n    method,\n    corejs2Polyfills,\n  );\n\n  const coreJSBase = useBabelRuntime\n    ? `${BABEL_RUNTIME}/core-js`\n    : method === \"usage-pure\"\n      ? \"core-js/library/fn\"\n      : \"core-js/modules\";\n\n  function inject(name: string | string[], utils) {\n    if (typeof name === \"string\") {\n      // Some polyfills aren't always available, for example\n      // web.dom.iterable when targeting node\n      if (has(polyfills, name) && shouldInjectPolyfill(name)) {\n        debug(name);\n        utils.injectGlobalImport(`${coreJSBase}/${name}.js`);\n      }\n      return;\n    }\n\n    name.forEach(name => inject(name, utils));\n  }\n\n  function maybeInjectPure(desc, hint, utils) {\n    let { pure, meta, name } = desc;\n\n    if (!pure || !shouldInjectPolyfill(name)) return;\n\n    if (\n      runtimeVersion &&\n      meta &&\n      meta.minRuntimeVersion &&\n      !hasMinVersion(meta && meta.minRuntimeVersion, runtimeVersion)\n    ) {\n      return;\n    }\n\n    // Unfortunately core-js and @babel/runtime-corejs2 don't have the same\n    // directory structure, so we need to special case this.\n    if (useBabelRuntime && pure === \"symbol/index\") pure = \"symbol\";\n\n    return utils.injectDefaultImport(`${coreJSBase}/${pure}${ext}`, hint);\n  }\n\n  return {\n    name: \"corejs2\",\n\n    runtimeName: noRuntimeName ? null : BABEL_RUNTIME,\n\n    polyfills,\n\n    entryGlobal(meta, utils, path) {\n      if (meta.kind === \"import\" && meta.source === \"core-js\") {\n        debug(null);\n\n        inject(Object.keys(polyfills), utils);\n\n        if (entryInjectRegenerator) {\n          utils.injectGlobalImport(\"regenerator-runtime/runtime.js\");\n        }\n\n        path.remove();\n      }\n    },\n\n    usageGlobal(meta, utils): undefined {\n      const resolved = resolve(meta);\n      if (!resolved) return;\n\n      let deps = resolved.desc.global;\n\n      if (\n        resolved.kind !== \"global\" &&\n        \"object\" in meta &&\n        meta.object &&\n        meta.placement === \"prototype\"\n      ) {\n        const low = meta.object.toLowerCase();\n        deps = deps.filter(m => m.includes(low));\n      }\n\n      inject(deps, utils);\n    },\n\n    usagePure(meta, utils, path) {\n      if (meta.kind === \"in\") {\n        if (meta.key === \"Symbol.iterator\") {\n          path.replaceWith(\n            t.callExpression(\n              utils.injectDefaultImport(\n                `${coreJSBase}/is-iterable${ext}`,\n                \"isIterable\",\n              ),\n              [(path.node as t.BinaryExpression).right], // meta.kind === \"in\" narrows this\n            ),\n          );\n        }\n\n        return;\n      }\n\n      if (path.parentPath.isUnaryExpression({ operator: \"delete\" })) return;\n\n      if (meta.kind === \"property\") {\n        // We can't compile destructuring.\n        if (!path.isMemberExpression()) return;\n        if (!path.isReferenced()) return;\n\n        if (\n          meta.key === \"Symbol.iterator\" &&\n          shouldInjectPolyfill(\"es6.symbol\") &&\n          path.parentPath.isCallExpression({ callee: path.node }) &&\n          path.parentPath.node.arguments.length === 0\n        ) {\n          path.parentPath.replaceWith(\n            t.callExpression(\n              utils.injectDefaultImport(\n                `${coreJSBase}/get-iterator${ext}`,\n                \"getIterator\",\n              ),\n              [path.node.object],\n            ),\n          );\n          path.skip();\n\n          return;\n        }\n      }\n\n      const resolved = resolve(meta);\n      if (!resolved) return;\n\n      const id = maybeInjectPure(resolved.desc, resolved.name, utils);\n      if (id) path.replaceWith(id);\n    },\n\n    visitor: method === \"usage-global\" && {\n      // yield*\n      YieldExpression(path: NodePath<t.YieldExpression>) {\n        if (path.node.delegate) {\n          inject(\"web.dom.iterable\", api.getUtils(path));\n        }\n      },\n\n      // for-of, [a, b] = c\n      \"ForOfStatement|ArrayPattern\"(\n        path: NodePath<t.ForOfStatement | t.ArrayPattern>,\n      ) {\n        CommonIterators.forEach(name => inject(name, api.getUtils(path)));\n      },\n    },\n  };\n});\n"], "names": ["define", "name", "pure", "global", "meta", "pureAndGlobal", "minRuntimeVersion", "globalOnly", "pureOnly", "ArrayNatureIterators", "CommonIterators", "PromiseDependencies", "BuiltIns", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Map", "Number", "Promise", "RegExp", "Set", "Symbol", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "WeakMap", "WeakSet", "setImmediate", "clearImmediate", "parseFloat", "parseInt", "InstanceProperties", "__defineGetter__", "__defineSetter__", "__lookupGetter__", "__lookupSetter__", "anchor", "big", "bind", "blink", "bold", "codePointAt", "copyWithin", "endsWith", "entries", "every", "fill", "filter", "finally", "find", "findIndex", "fixed", "flags", "flatMap", "fontcolor", "fontsize", "for<PERSON>ach", "includes", "indexOf", "italics", "keys", "lastIndexOf", "link", "map", "match", "padStart", "padEnd", "reduce", "reduceRight", "repeat", "replace", "search", "small", "some", "sort", "split", "startsWith", "strike", "sub", "sup", "toISOString", "toJSON", "toString", "trim", "trimEnd", "trimLeft", "trimRight", "trimStart", "values", "corejs2Polyfills", "slice", "StaticProperties", "Array", "from", "isArray", "of", "Date", "now", "JSON", "stringify", "Math", "acosh", "asinh", "atanh", "cbrt", "clz32", "cosh", "expm1", "fround", "hypot", "imul", "log1p", "log10", "log2", "sign", "sinh", "tanh", "trunc", "EPSILON", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER", "isFinite", "isInteger", "isSafeInteger", "isNaN", "Object", "assign", "create", "defineProperties", "defineProperty", "freeze", "getOwnPropertyDescriptor", "getOwnPropertyDescriptors", "getOwnPropertyNames", "getOwnPropertySymbols", "getPrototypeOf", "is", "isExtensible", "isFrozen", "isSealed", "preventExtensions", "seal", "setPrototypeOf", "all", "race", "Reflect", "apply", "construct", "deleteProperty", "get", "has", "ownKeys", "set", "String", "at", "fromCodePoint", "raw", "asyncIterator", "for", "hasInstance", "isConcatSpreadable", "iterator", "keyFor", "species", "toPrimitive", "toStringTag", "unscopables", "webPolyfills", "purePolyfills", "targets", "method", "polyfills", "targetNames", "isAnyTarget", "length", "isWebTarget", "hasMinVersion", "minVersion", "runtimeVersion", "semver", "valid", "intersects", "types", "t", "_babel", "default", "BABEL_RUNTIME", "presetEnvCompat", "runtimeCompat", "Function", "call", "hasOwnProperty", "define<PERSON>rovider", "api", "entryInjectRegenerator", "noRuntimeName", "useBabelRuntime", "ext", "resolve", "createMetaResolver", "static", "instance", "debug", "shouldInjectPolyfill", "addPlatformSpecificPolyfills", "coreJSBase", "inject", "utils", "injectGlobalImport", "maybeInjectPure", "desc", "hint", "injectDefaultImport", "runtimeName", "entryGlobal", "path", "kind", "source", "remove", "usageGlobal", "resolved", "deps", "object", "placement", "low", "toLowerCase", "m", "usagePure", "key", "replaceWith", "callExpression", "node", "right", "parentPath", "isUnaryExpression", "operator", "isMemberExpression", "isReferenced", "isCallExpression", "callee", "arguments", "skip", "id", "visitor", "YieldExpression", "delegate", "getUtils", "ForOfStatement|ArrayPattern"], "mappings": ";;;;;AAeA,MAAMA,MAAM,GAAGA,CACbC,IAAY,EACZC,IAAoB,EACpBC,MAAgB,GAAG,EAAE,EACrBC,IAAe,KACW;EAC1B,OAAO;IAAEH,IAAI;IAAEC,IAAI;IAAEC,MAAM;AAAEC,IAAAA,IAAAA;GAAM,CAAA;AACrC,CAAC,CAAA;AAED,MAAMC,aAAa,GAAGA,CACpBH,IAAY,EACZC,MAAgB,EAChBG,iBAAgC,GAAG,IAAI,KACpCN,MAAM,CAAcG,MAAM,CAAC,CAAC,CAAC,EAAED,IAAI,EAAEC,MAAM,EAAE;AAAEG,EAAAA,iBAAAA;AAAkB,CAAC,CAAC,CAAA;AAExE,MAAMC,UAAU,GAAIJ,MAAgB,IAClCH,MAAM,CAAcG,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,EAAEA,MAAM,CAAC,CAAA;AAE9C,MAAMK,QAAQ,GAAGA,CAACN,IAAY,EAAED,IAAY,KAC1CD,MAAM,CAAcC,IAAI,EAAEC,IAAI,EAAE,EAAE,CAAC,CAAA;AAErC,MAAMO,oBAAoB,GAAG,CAC3B,sBAAsB,EACtB,oBAAoB,EACpB,kBAAkB,CACnB,CAAA;AAEM,MAAMC,eAAe,GAAG,CAAC,qBAAqB,EAAE,GAAGD,oBAAoB,CAAC,CAAA;AAE/E,MAAME,mBAAmB,GAAG,CAAC,sBAAsB,EAAE,aAAa,CAAC,CAAA;AAE5D,MAAMC,QAAoD,GAAG;AAClEC,EAAAA,QAAQ,EAAEN,UAAU,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAC7CO,EAAAA,YAAY,EAAEP,UAAU,CAAC,CAAC,yBAAyB,CAAC,CAAC;AACrDQ,EAAAA,YAAY,EAAER,UAAU,CAAC,CAAC,yBAAyB,CAAC,CAAC;AACrDS,EAAAA,SAAS,EAAET,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC/CU,EAAAA,UAAU,EAAEV,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACjDW,EAAAA,UAAU,EAAEX,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjDY,GAAG,EAAEd,aAAa,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,GAAGK,eAAe,CAAC,CAAC;AAC1DU,EAAAA,MAAM,EAAEb,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;AAC9Cc,EAAAA,OAAO,EAAEhB,aAAa,CAAC,SAAS,EAAEM,mBAAmB,CAAC;AACtDW,EAAAA,MAAM,EAAEf,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAC9CgB,GAAG,EAAElB,aAAa,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,GAAGK,eAAe,CAAC,CAAC;EAC1Dc,MAAM,EAAEnB,aAAa,CAAC,cAAc,EAAE,CAAC,YAAY,CAAC,CAAC;AACrDoB,EAAAA,UAAU,EAAElB,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACjDmB,EAAAA,iBAAiB,EAAEnB,UAAU,CAAC,CAAC,+BAA+B,CAAC,CAAC;AAChEoB,EAAAA,WAAW,EAAEpB,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;AACnDqB,EAAAA,WAAW,EAAErB,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;EACnDsB,OAAO,EAAExB,aAAa,CAAC,UAAU,EAAE,CAAC,cAAc,EAAE,GAAGK,eAAe,CAAC,CAAC;EACxEoB,OAAO,EAAEzB,aAAa,CAAC,UAAU,EAAE,CAAC,cAAc,EAAE,GAAGK,eAAe,CAAC,CAAC;AAExEqB,EAAAA,YAAY,EAAEvB,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC;AACxDwB,EAAAA,cAAc,EAAExB,QAAQ,CAAC,iBAAiB,EAAE,eAAe,CAAC;AAC5DyB,EAAAA,UAAU,EAAEzB,QAAQ,CAAC,aAAa,EAAE,iBAAiB,CAAC;AACtD0B,EAAAA,QAAQ,EAAE1B,QAAQ,CAAC,WAAW,EAAE,eAAe,CAAA;AACjD,CAAC,CAAA;AAEM,MAAM2B,kBAA8D,GAAG;AAC5EC,EAAAA,gBAAgB,EAAE7B,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAC1D8B,EAAAA,gBAAgB,EAAE9B,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAC1D+B,EAAAA,gBAAgB,EAAE/B,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAC1DgC,EAAAA,gBAAgB,EAAEhC,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAC1DiC,EAAAA,MAAM,EAAEjC,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACzCkC,EAAAA,GAAG,EAAElC,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACnCmC,EAAAA,IAAI,EAAEnC,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACvCoC,EAAAA,KAAK,EAAEpC,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACvCqC,EAAAA,IAAI,EAAErC,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACrCsC,EAAAA,WAAW,EAAEtC,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;AACrDuC,EAAAA,UAAU,EAAEvC,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACjDwC,EAAAA,QAAQ,EAAExC,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC9CyC,EAAAA,OAAO,EAAEzC,UAAU,CAACE,oBAAoB,CAAC;AACzCwC,EAAAA,KAAK,EAAE1C,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACtC2C,EAAAA,IAAI,EAAE3C,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACpC4C,EAAAA,MAAM,EAAE5C,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACxC6C,OAAO,EAAE7C,UAAU,CAAC,CAAC,qBAAqB,EAAE,GAAGI,mBAAmB,CAAC,CAAC;AACpE0C,EAAAA,IAAI,EAAE9C,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACpC+C,EAAAA,SAAS,EAAE/C,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC/CgD,EAAAA,KAAK,EAAEhD,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACvCiD,EAAAA,KAAK,EAAEjD,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACvCkD,EAAAA,OAAO,EAAElD,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC3CmD,EAAAA,SAAS,EAAEnD,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC/CoD,EAAAA,QAAQ,EAAEpD,UAAU,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAC7CqD,EAAAA,OAAO,EAAErD,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC3CsD,QAAQ,EAAEtD,UAAU,CAAC,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,CAAC;AACnEuD,EAAAA,OAAO,EAAEvD,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC3CwD,EAAAA,OAAO,EAAExD,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC3CyD,EAAAA,IAAI,EAAEzD,UAAU,CAACE,oBAAoB,CAAC;AACtCwD,EAAAA,WAAW,EAAE1D,UAAU,CAAC,CAAC,yBAAyB,CAAC,CAAC;AACpD2D,EAAAA,IAAI,EAAE3D,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACrC4D,EAAAA,GAAG,EAAE5D,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC;AAClC6D,EAAAA,KAAK,EAAE7D,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACvCN,EAAAA,IAAI,EAAEM,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACvC8D,EAAAA,QAAQ,EAAE9D,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC9C+D,EAAAA,MAAM,EAAE/D,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC1CgE,EAAAA,MAAM,EAAEhE,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACxCiE,EAAAA,WAAW,EAAEjE,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;AACnDkE,EAAAA,MAAM,EAAElE,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACzCmE,EAAAA,OAAO,EAAEnE,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;AAC3CoE,EAAAA,MAAM,EAAEpE,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACzCqE,EAAAA,KAAK,EAAErE,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACvCsE,EAAAA,IAAI,EAAEtE,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACpCuE,EAAAA,IAAI,EAAEvE,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACpCwE,EAAAA,KAAK,EAAExE,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACvCyE,EAAAA,UAAU,EAAEzE,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;AAClD0E,EAAAA,MAAM,EAAE1E,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACzC2E,EAAAA,GAAG,EAAE3E,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACnC4E,EAAAA,GAAG,EAAE5E,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACnC6E,EAAAA,WAAW,EAAE7E,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;AACnD8E,EAAAA,MAAM,EAAE9E,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACxC+E,QAAQ,EAAE/E,UAAU,CAAC,CACnB,sBAAsB,EACtB,oBAAoB,EACpB,sBAAsB,CACvB,CAAC;AACFgF,EAAAA,IAAI,EAAEhF,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACrCiF,EAAAA,OAAO,EAAEjF,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;AAC9CkF,EAAAA,QAAQ,EAAElF,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;AAC9CmF,EAAAA,SAAS,EAAEnF,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;AAChDoF,EAAAA,SAAS,EAAEpF,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAC/CqF,MAAM,EAAErF,UAAU,CAACE,oBAAoB,CAAA;AACzC,CAAC,CAAA;;AAED;AACA,IAAI,iBAAiB,IAAIoF,gBAAgB,EAAE;EACzC1D,kBAAkB,CAAC2D,KAAK,GAAGvF,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAA;AAC5D,CAAA;AAEO,MAAMwF,gBAEZ,GAAG;AACFC,EAAAA,KAAK,EAAE;AACLC,IAAAA,IAAI,EAAE5F,aAAa,CAAC,YAAY,EAAE,CAChC,YAAY,EACZ,gBAAgB,EAChB,GAAGK,eAAe,CACnB,CAAC;IACFwF,OAAO,EAAE7F,aAAa,CAAC,gBAAgB,EAAE,CAAC,oBAAoB,CAAC,CAAC;AAChE8F,IAAAA,EAAE,EAAE9F,aAAa,CAAC,UAAU,EAAE,CAAC,cAAc,CAAC,CAAA;GAC/C;AAED+F,EAAAA,IAAI,EAAE;AACJC,IAAAA,GAAG,EAAEhG,aAAa,CAAC,UAAU,EAAE,CAAC,cAAc,CAAC,CAAA;GAChD;AAEDiG,EAAAA,IAAI,EAAE;AACJC,IAAAA,SAAS,EAAE/F,QAAQ,CAAC,gBAAgB,EAAE,YAAY,CAAA;GACnD;AAEDgG,EAAAA,IAAI,EAAE;AACJ;AACA;IACAC,KAAK,EAAEpG,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DqG,KAAK,EAAErG,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DsG,KAAK,EAAEtG,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DuG,IAAI,EAAEvG,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DwG,KAAK,EAAExG,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DyG,IAAI,EAAEzG,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5D0G,KAAK,EAAE1G,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/D2G,MAAM,EAAE3G,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAClE4G,KAAK,EAAE5G,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/D6G,IAAI,EAAE7G,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5D8G,KAAK,EAAE9G,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/D+G,KAAK,EAAE/G,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DgH,IAAI,EAAEhH,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DiH,IAAI,EAAEjH,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DkH,IAAI,EAAElH,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DmH,IAAI,EAAEnH,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DoH,KAAK,EAAEpH,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAA;GAC/D;AAEDe,EAAAA,MAAM,EAAE;IACNsG,OAAO,EAAErH,aAAa,CAAC,gBAAgB,EAAE,CAAC,oBAAoB,CAAC,CAAC;IAChEsH,gBAAgB,EAAEtH,aAAa,CAAC,yBAAyB,EAAE,CACzD,6BAA6B,CAC9B,CAAC;IACFuH,gBAAgB,EAAEvH,aAAa,CAAC,yBAAyB,EAAE,CACzD,6BAA6B,CAC9B,CAAC;IACFwH,QAAQ,EAAExH,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACrEyH,SAAS,EAAEzH,aAAa,CAAC,mBAAmB,EAAE,CAAC,uBAAuB,CAAC,CAAC;IACxE0H,aAAa,EAAE1H,aAAa,CAAC,wBAAwB,EAAE,CACrD,4BAA4B,CAC7B,CAAC;IACF2H,KAAK,EAAE3H,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC5D4B,UAAU,EAAE5B,aAAa,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,CAAC,CAAC;AAC3E6B,IAAAA,QAAQ,EAAE7B,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,CAAA;GACrE;AAED4H,EAAAA,MAAM,EAAE;IACNC,MAAM,EAAE7H,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC7D8H,MAAM,EAAE9H,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC7D+H,gBAAgB,EAAE/H,aAAa,CAAC,0BAA0B,EAAE,CAC1D,8BAA8B,CAC/B,CAAC;IACFgI,cAAc,EAAEhI,aAAa,CAAC,wBAAwB,EAAE,CACtD,4BAA4B,CAC7B,CAAC;IACF2C,OAAO,EAAE3C,aAAa,CAAC,gBAAgB,EAAE,CAAC,oBAAoB,CAAC,CAAC;IAChEiI,MAAM,EAAEjI,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC7DkI,wBAAwB,EAAElI,aAAa,CACrC,oCAAoC,EACpC,CAAC,wCAAwC,CAC3C,CAAC;IACDmI,yBAAyB,EAAEnI,aAAa,CACtC,qCAAqC,EACrC,CAAC,yCAAyC,CAC5C,CAAC;IACDoI,mBAAmB,EAAEpI,aAAa,CAAC,+BAA+B,EAAE,CAClE,mCAAmC,CACpC,CAAC;IACFqI,qBAAqB,EAAErI,aAAa,CAAC,iCAAiC,EAAE,CACtE,YAAY,CACb,CAAC;IACFsI,cAAc,EAAEtI,aAAa,CAAC,yBAAyB,EAAE,CACvD,6BAA6B,CAC9B,CAAC;IACFuI,EAAE,EAAEvI,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,CAAC;IACjDwI,YAAY,EAAExI,aAAa,CAAC,sBAAsB,EAAE,CAClD,0BAA0B,CAC3B,CAAC;IACFyI,QAAQ,EAAEzI,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACrE0I,QAAQ,EAAE1I,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACrE2D,IAAI,EAAE3D,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;IACvD2I,iBAAiB,EAAE3I,aAAa,CAAC,2BAA2B,EAAE,CAC5D,+BAA+B,CAChC,CAAC;IACF4I,IAAI,EAAE5I,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;IACvD6I,cAAc,EAAE7I,aAAa,CAAC,yBAAyB,EAAE,CACvD,6BAA6B,CAC9B,CAAC;AACFuF,IAAAA,MAAM,EAAEvF,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAA;GAC7D;AAEDgB,EAAAA,OAAO,EAAE;AACP8H,IAAAA,GAAG,EAAE5I,UAAU,CAACG,eAAe,CAAC;IAChC0I,IAAI,EAAE7I,UAAU,CAACG,eAAe,CAAA;GACjC;AAED2I,EAAAA,OAAO,EAAE;IACPC,KAAK,EAAEjJ,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC5DkJ,SAAS,EAAElJ,aAAa,CAAC,mBAAmB,EAAE,CAAC,uBAAuB,CAAC,CAAC;IACxEgI,cAAc,EAAEhI,aAAa,CAAC,yBAAyB,EAAE,CACvD,6BAA6B,CAC9B,CAAC;IACFmJ,cAAc,EAAEnJ,aAAa,CAAC,yBAAyB,EAAE,CACvD,6BAA6B,CAC9B,CAAC;IACFoJ,GAAG,EAAEpJ,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;IACtDkI,wBAAwB,EAAElI,aAAa,CACrC,qCAAqC,EACrC,CAAC,yCAAyC,CAC5C,CAAC;IACDsI,cAAc,EAAEtI,aAAa,CAAC,0BAA0B,EAAE,CACxD,8BAA8B,CAC/B,CAAC;IACFqJ,GAAG,EAAErJ,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;IACtDwI,YAAY,EAAExI,aAAa,CAAC,uBAAuB,EAAE,CACnD,2BAA2B,CAC5B,CAAC;IACFsJ,OAAO,EAAEtJ,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACpE2I,iBAAiB,EAAE3I,aAAa,CAAC,4BAA4B,EAAE,CAC7D,gCAAgC,CACjC,CAAC;IACFuJ,GAAG,EAAEvJ,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;AACtD6I,IAAAA,cAAc,EAAE7I,aAAa,CAAC,0BAA0B,EAAE,CACxD,8BAA8B,CAC/B,CAAA;GACF;AAEDwJ,EAAAA,MAAM,EAAE;AACNC,IAAAA,EAAE,EAAEtJ,QAAQ,CAAC,WAAW,EAAE,eAAe,CAAC;IAC1CuJ,aAAa,EAAE1J,aAAa,CAAC,wBAAwB,EAAE,CACrD,4BAA4B,CAC7B,CAAC;AACF2J,IAAAA,GAAG,EAAE3J,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAA;GACpD;AAEDmB,EAAAA,MAAM,EAAE;AACN;IACAyI,aAAa,EAAE1J,UAAU,CAAC,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAAC;AACtE2J,IAAAA,GAAG,EAAE1J,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC2J,IAAAA,WAAW,EAAE3J,QAAQ,CAAC,qBAAqB,EAAE,YAAY,CAAC;AAC1D4J,IAAAA,kBAAkB,EAAE5J,QAAQ,CAAC,6BAA6B,EAAE,YAAY,CAAC;IACzE6J,QAAQ,EAAErK,MAAM,CAAC,YAAY,EAAE,iBAAiB,EAAEU,eAAe,CAAC;AAClE4J,IAAAA,MAAM,EAAE9J,QAAQ,CAAC,gBAAgB,EAAE,YAAY,CAAC;IAChD4D,KAAK,EAAE/D,aAAa,CAAC,cAAc,EAAE,CAAC,kBAAkB,CAAC,CAAC;AAC1DqE,IAAAA,OAAO,EAAElE,QAAQ,CAAC,gBAAgB,EAAE,YAAY,CAAC;AACjDmE,IAAAA,MAAM,EAAEnE,QAAQ,CAAC,eAAe,EAAE,YAAY,CAAC;AAC/C+J,IAAAA,OAAO,EAAE/J,QAAQ,CAAC,gBAAgB,EAAE,YAAY,CAAC;AACjDuE,IAAAA,KAAK,EAAEvE,QAAQ,CAAC,cAAc,EAAE,YAAY,CAAC;AAC7CgK,IAAAA,WAAW,EAAEhK,QAAQ,CAAC,qBAAqB,EAAE,YAAY,CAAC;AAC1DiK,IAAAA,WAAW,EAAEjK,QAAQ,CAAC,sBAAsB,EAAE,YAAY,CAAC;AAC3DkK,IAAAA,WAAW,EAAElK,QAAQ,CAAC,oBAAoB,EAAE,YAAY,CAAA;AAC1D,GAAA;AACF,CAAC;;ACnTD,MAAMmK,YAAY,GAAG;EACnB,YAAY,EAAE,EAAE;EAChB,eAAe,EAAE,EAAE;AACnB,EAAA,kBAAkB,EAAE,EAAC;AACvB,CAAC,CAAA;AAED,MAAMC,aAAa,GAAG;EACpB,iBAAiB,EAAE,EAAE;EACrB,eAAe,EAAE,EAAE;AACnB,EAAA,eAAe,EAAE,EAAC;AACpB,CAAC,CAAA;AAEc,uCAAUC,OAAgB,EAAEC,MAAc,EAAEC,SAAc,EAAE;AACzE,EAAA,MAAMC,WAAW,GAAG/C,MAAM,CAACjE,IAAI,CAAC6G,OAAO,CAAC,CAAA;AACxC,EAAA,MAAMI,WAAW,GAAG,CAACD,WAAW,CAACE,MAAM,CAAA;EACvC,MAAMC,WAAW,GAAGH,WAAW,CAACnG,IAAI,CAAC5E,IAAI,IAAIA,IAAI,KAAK,MAAM,CAAC,CAAA;EAE7D,OAAO;AACL,IAAA,GAAG8K,SAAS;AACZ,IAAA,IAAID,MAAM,KAAK,YAAY,GAAGF,aAAa,GAAG,IAAI,CAAC;AACnD,IAAA,IAAIK,WAAW,IAAIE,WAAW,GAAGR,YAAY,GAAG,IAAI,CAAA;GACrD,CAAA;AACH;;ACtBO,SAASS,aAAaA,CAC3BC,UAA0B,EAC1BC,cAAuC,EACvC;AACA;AACA;AACA;AACA,EAAA,IAAI,CAACA,cAAc,IAAI,CAACD,UAAU,EAAE,OAAO,IAAI,CAAA;AAE/CC,EAAAA,cAAc,GAAGzB,MAAM,CAACyB,cAAc,CAAC,CAAA;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIC,MAAM,CAACC,KAAK,CAACF,cAAc,CAAC,EAAEA,cAAc,GAAG,CAAIA,CAAAA,EAAAA,cAAc,CAAE,CAAA,CAAA;EAEvE,OACE,CAACC,MAAM,CAACE,UAAU,CAAC,CAAIJ,CAAAA,EAAAA,UAAU,EAAE,EAAEC,cAAc,CAAC,IACpD,CAACC,MAAM,CAACE,UAAU,CAAC,CAAS,OAAA,CAAA,EAAEH,cAAc,CAAC,CAAA;AAEjD;;AC1BoE,MAAA;AAE3DI,EAAAA,KAAK,EAAIC,CAAAA;AAAC,CAAA,GAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA,CAAA;AAEnB,MAAME,aAAa,GAAG,wBAAwB,CAAA;AAE9C,MAAMC,eAAe,GAAG,iDAAiD,CAAA;AACzE,MAAMC,aAAa,GAAG,8CAA8C,CAAA;AAEpE,MAAMtC,GAAG,GAAGuC,QAAQ,CAACC,IAAI,CAACxJ,IAAI,CAACuF,MAAM,CAACkE,cAAc,CAAC,CAAA;AAcrD,YAAeC,cAAc,CAAU,UACrCC,GAAG,EACH;AACE,EAAA,CAACN,eAAe,GAAG;AACjBO,IAAAA,sBAAsB,GAAG,KAAK;AAC9BC,IAAAA,aAAa,GAAG,KAAA;GACjB,GAAG,EAAE;AACN,EAAA,CAACP,aAAa,GAAG;AACfQ,IAAAA,eAAe,GAAG,KAAK;AACvBlB,IAAAA,cAAc,GAAG,EAAE;AACnBmB,IAAAA,GAAG,GAAG,KAAA;AACR,GAAC,GAAG,EAAC;AACP,CAAC,EACD;AACA,EAAA,MAAMC,OAAO,GAAGL,GAAG,CAACM,kBAAkB,CAAC;AACrCxM,IAAAA,MAAM,EAAES,QAAQ;AAChBgM,IAAAA,MAAM,EAAE7G,gBAAgB;AACxB8G,IAAAA,QAAQ,EAAE1K,kBAAAA;AACZ,GAAC,CAAC,CAAA;EAEF,MAAM;IAAE2K,KAAK;IAAEC,oBAAoB;AAAEjC,IAAAA,MAAAA;AAAO,GAAC,GAAGuB,GAAG,CAAA;EAEnD,MAAMtB,SAAS,GAAGiC,4BAA4B,CAC5CX,GAAG,CAACxB,OAAO,EACXC,MAAM,EACNjF,gBACF,CAAC,CAAA;AAED,EAAA,MAAMoH,UAAU,GAAGT,eAAe,GAC9B,GAAGV,aAAa,CAAA,QAAA,CAAU,GAC1BhB,MAAM,KAAK,YAAY,GACrB,oBAAoB,GACpB,iBAAiB,CAAA;AAEvB,EAAA,SAASoC,MAAMA,CAACjN,IAAuB,EAAEkN,KAAK,EAAE;AAC9C,IAAA,IAAI,OAAOlN,IAAI,KAAK,QAAQ,EAAE;AAC5B;AACA;MACA,IAAIyJ,GAAG,CAACqB,SAAS,EAAE9K,IAAI,CAAC,IAAI8M,oBAAoB,CAAC9M,IAAI,CAAC,EAAE;QACtD6M,KAAK,CAAC7M,IAAI,CAAC,CAAA;QACXkN,KAAK,CAACC,kBAAkB,CAAC,CAAA,EAAGH,UAAU,CAAIhN,CAAAA,EAAAA,IAAI,KAAK,CAAC,CAAA;AACtD,OAAA;AACA,MAAA,OAAA;AACF,KAAA;IAEAA,IAAI,CAAC2D,OAAO,CAAC3D,IAAI,IAAIiN,MAAM,CAACjN,IAAI,EAAEkN,KAAK,CAAC,CAAC,CAAA;AAC3C,GAAA;AAEA,EAAA,SAASE,eAAeA,CAACC,IAAI,EAAEC,IAAI,EAAEJ,KAAK,EAAE;IAC1C,IAAI;MAAEjN,IAAI;MAAEE,IAAI;AAAEH,MAAAA,IAAAA;AAAK,KAAC,GAAGqN,IAAI,CAAA;IAE/B,IAAI,CAACpN,IAAI,IAAI,CAAC6M,oBAAoB,CAAC9M,IAAI,CAAC,EAAE,OAAA;AAE1C,IAAA,IACEqL,cAAc,IACdlL,IAAI,IACJA,IAAI,CAACE,iBAAiB,IACtB,CAAC8K,aAAa,CAAChL,IAAI,IAAIA,IAAI,CAACE,iBAAiB,EAAEgL,cAAc,CAAC,EAC9D;AACA,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;IACA,IAAIkB,eAAe,IAAItM,IAAI,KAAK,cAAc,EAAEA,IAAI,GAAG,QAAQ,CAAA;AAE/D,IAAA,OAAOiN,KAAK,CAACK,mBAAmB,CAAC,CAAGP,EAAAA,UAAU,CAAI/M,CAAAA,EAAAA,IAAI,CAAGuM,EAAAA,GAAG,CAAE,CAAA,EAAEc,IAAI,CAAC,CAAA;AACvE,GAAA;EAEA,OAAO;AACLtN,IAAAA,IAAI,EAAE,SAAS;AAEfwN,IAAAA,WAAW,EAAElB,aAAa,GAAG,IAAI,GAAGT,aAAa;IAEjDf,SAAS;AAET2C,IAAAA,WAAWA,CAACtN,IAAI,EAAE+M,KAAK,EAAEQ,IAAI,EAAE;MAC7B,IAAIvN,IAAI,CAACwN,IAAI,KAAK,QAAQ,IAAIxN,IAAI,CAACyN,MAAM,KAAK,SAAS,EAAE;QACvDf,KAAK,CAAC,IAAI,CAAC,CAAA;QAEXI,MAAM,CAACjF,MAAM,CAACjE,IAAI,CAAC+G,SAAS,CAAC,EAAEoC,KAAK,CAAC,CAAA;AAErC,QAAA,IAAIb,sBAAsB,EAAE;AAC1Ba,UAAAA,KAAK,CAACC,kBAAkB,CAAC,gCAAgC,CAAC,CAAA;AAC5D,SAAA;QAEAO,IAAI,CAACG,MAAM,EAAE,CAAA;AACf,OAAA;KACD;AAEDC,IAAAA,WAAWA,CAAC3N,IAAI,EAAE+M,KAAK,EAAa;AAClC,MAAA,MAAMa,QAAQ,GAAGtB,OAAO,CAACtM,IAAI,CAAC,CAAA;MAC9B,IAAI,CAAC4N,QAAQ,EAAE,OAAA;AAEf,MAAA,IAAIC,IAAI,GAAGD,QAAQ,CAACV,IAAI,CAACnN,MAAM,CAAA;AAE/B,MAAA,IACE6N,QAAQ,CAACJ,IAAI,KAAK,QAAQ,IAC1B,QAAQ,IAAIxN,IAAI,IAChBA,IAAI,CAAC8N,MAAM,IACX9N,IAAI,CAAC+N,SAAS,KAAK,WAAW,EAC9B;QACA,MAAMC,GAAG,GAAGhO,IAAI,CAAC8N,MAAM,CAACG,WAAW,EAAE,CAAA;AACrCJ,QAAAA,IAAI,GAAGA,IAAI,CAAC9K,MAAM,CAACmL,CAAC,IAAIA,CAAC,CAACzK,QAAQ,CAACuK,GAAG,CAAC,CAAC,CAAA;AAC1C,OAAA;AAEAlB,MAAAA,MAAM,CAACe,IAAI,EAAEd,KAAK,CAAC,CAAA;KACpB;AAEDoB,IAAAA,SAASA,CAACnO,IAAI,EAAE+M,KAAK,EAAEQ,IAAI,EAAE;AAC3B,MAAA,IAAIvN,IAAI,CAACwN,IAAI,KAAK,IAAI,EAAE;AACtB,QAAA,IAAIxN,IAAI,CAACoO,GAAG,KAAK,iBAAiB,EAAE;UAClCb,IAAI,CAACc,WAAW,CACd9C,CAAC,CAAC+C,cAAc,CACdvB,KAAK,CAACK,mBAAmB,CACvB,CAAGP,EAAAA,UAAU,CAAeR,YAAAA,EAAAA,GAAG,CAAE,CAAA,EACjC,YACF,CAAC,EACD,CAAEkB,IAAI,CAACgB,IAAI,CAAwBC,KAAK,CAAC;AAC3C,WACF,CAAC,CAAA;AACH,SAAA;AAEA,QAAA,OAAA;AACF,OAAA;AAEA,MAAA,IAAIjB,IAAI,CAACkB,UAAU,CAACC,iBAAiB,CAAC;AAAEC,QAAAA,QAAQ,EAAE,QAAA;AAAS,OAAC,CAAC,EAAE,OAAA;AAE/D,MAAA,IAAI3O,IAAI,CAACwN,IAAI,KAAK,UAAU,EAAE;AAC5B;AACA,QAAA,IAAI,CAACD,IAAI,CAACqB,kBAAkB,EAAE,EAAE,OAAA;AAChC,QAAA,IAAI,CAACrB,IAAI,CAACsB,YAAY,EAAE,EAAE,OAAA;AAE1B,QAAA,IACE7O,IAAI,CAACoO,GAAG,KAAK,iBAAiB,IAC9BzB,oBAAoB,CAAC,YAAY,CAAC,IAClCY,IAAI,CAACkB,UAAU,CAACK,gBAAgB,CAAC;UAAEC,MAAM,EAAExB,IAAI,CAACgB,IAAAA;AAAK,SAAC,CAAC,IACvDhB,IAAI,CAACkB,UAAU,CAACF,IAAI,CAACS,SAAS,CAAClE,MAAM,KAAK,CAAC,EAC3C;AACAyC,UAAAA,IAAI,CAACkB,UAAU,CAACJ,WAAW,CACzB9C,CAAC,CAAC+C,cAAc,CACdvB,KAAK,CAACK,mBAAmB,CACvB,CAAGP,EAAAA,UAAU,CAAgBR,aAAAA,EAAAA,GAAG,CAAE,CAAA,EAClC,aACF,CAAC,EACD,CAACkB,IAAI,CAACgB,IAAI,CAACT,MAAM,CACnB,CACF,CAAC,CAAA;UACDP,IAAI,CAAC0B,IAAI,EAAE,CAAA;AAEX,UAAA,OAAA;AACF,SAAA;AACF,OAAA;AAEA,MAAA,MAAMrB,QAAQ,GAAGtB,OAAO,CAACtM,IAAI,CAAC,CAAA;MAC9B,IAAI,CAAC4N,QAAQ,EAAE,OAAA;AAEf,MAAA,MAAMsB,EAAE,GAAGjC,eAAe,CAACW,QAAQ,CAACV,IAAI,EAAEU,QAAQ,CAAC/N,IAAI,EAAEkN,KAAK,CAAC,CAAA;AAC/D,MAAA,IAAImC,EAAE,EAAE3B,IAAI,CAACc,WAAW,CAACa,EAAE,CAAC,CAAA;KAC7B;AAEDC,IAAAA,OAAO,EAAEzE,MAAM,KAAK,cAAc,IAAI;AACpC;MACA0E,eAAeA,CAAC7B,IAAiC,EAAE;AACjD,QAAA,IAAIA,IAAI,CAACgB,IAAI,CAACc,QAAQ,EAAE;UACtBvC,MAAM,CAAC,kBAAkB,EAAEb,GAAG,CAACqD,QAAQ,CAAC/B,IAAI,CAAC,CAAC,CAAA;AAChD,SAAA;OACD;AAED;MACA,6BAA6BgC,CAC3BhC,IAAiD,EACjD;AACAjN,QAAAA,eAAe,CAACkD,OAAO,CAAC3D,IAAI,IAAIiN,MAAM,CAACjN,IAAI,EAAEoM,GAAG,CAACqD,QAAQ,CAAC/B,IAAI,CAAC,CAAC,CAAC,CAAA;AACnE,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;"}
from flask import Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.user import User
from app.models.student import Student
from app.models.department import Department
from app.utils.decorators import admin_required, faculty_required
from app.utils.helpers import (
    success_response, error_response, paginate_query,
    validate_email, validate_phone, generate_roll_number
)

students_bp = Blueprint('students', __name__)

@students_bp.route('', methods=['GET'])
@jwt_required()
def get_students():
    """Get all students with pagination and filtering."""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        department_id = request.args.get('department_id', type=int)
        year = request.args.get('year', type=int)
        search = request.args.get('search', '').strip()
        
        query = Student.query.filter_by(is_active=True)
        
        # Apply filters
        if department_id:
            query = query.filter_by(department_id=department_id)
        
        if year:
            query = query.filter_by(year=year)
        
        if search:
            query = query.filter(
                (Student.first_name.contains(search)) |
                (Student.last_name.contains(search)) |
                (Student.roll_number.contains(search))
            )
        
        # Order by roll number
        query = query.order_by(Student.roll_number)
        
        # Paginate
        paginated = paginate_query(query, page, per_page)
        if not paginated:
            return error_response('Invalid pagination parameters')
        
        students_data = [student.to_dict() for student in paginated['items']]
        
        return success_response('Students retrieved successfully', {
            'students': students_data,
            'pagination': {
                'total': paginated['total'],
                'pages': paginated['pages'],
                'current_page': paginated['current_page'],
                'per_page': paginated['per_page'],
                'has_next': paginated['has_next'],
                'has_prev': paginated['has_prev']
            }
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve students: {str(e)}', 500)

@students_bp.route('/<int:student_id>', methods=['GET'])
@jwt_required()
def get_student(student_id):
    """Get a specific student by ID."""
    try:
        student = Student.query.filter_by(id=student_id, is_active=True).first()
        
        if not student:
            return error_response('Student not found', 404)
        
        return success_response('Student retrieved successfully', {
            'student': student.to_dict()
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve student: {str(e)}', 500)

@students_bp.route('', methods=['POST'])
@admin_required
def create_student(current_user):
    """Create a new student."""
    try:
        data = request.get_json()
        
        if not data:
            return error_response('No data provided')
        
        # Validate required fields
        required_fields = ['first_name', 'last_name', 'email', 'department_id', 'year', 'semester']
        for field in required_fields:
            if not data.get(field):
                return error_response(f'{field.replace("_", " ").title()} is required')
        
        # Validate email
        email = data.get('email').strip().lower()
        if not validate_email(email):
            return error_response('Invalid email format')
        
        # Check if email already exists
        if User.query.filter_by(email=email).first():
            return error_response('Email already exists')
        
        # Validate phone if provided
        phone = data.get('phone', '').strip()
        if phone and not validate_phone(phone):
            return error_response('Invalid phone number format')
        
        # Validate department
        department = Department.query.get(data.get('department_id'))
        if not department:
            return error_response('Department not found')
        
        # Generate username and roll number
        first_name = data.get('first_name').strip()
        last_name = data.get('last_name').strip()
        username = f"{first_name.lower()}.{last_name.lower()}"
        
        # Ensure unique username
        counter = 1
        original_username = username
        while User.query.filter_by(username=username).first():
            username = f"{original_username}{counter}"
            counter += 1
        
        # Generate roll number
        year = data.get('year')
        last_student = Student.query.filter_by(
            department_id=department.id, year=year
        ).order_by(Student.roll_number.desc()).first()
        
        sequence = 1
        if last_student:
            # Extract sequence from last roll number
            try:
                sequence = int(last_student.roll_number[-3:]) + 1
            except:
                sequence = 1
        
        roll_number = generate_roll_number(department.code, year, sequence)
        
        # Create user account
        user = User(
            username=username,
            email=email,
            role='student',
            is_active=True
        )
        user.set_password('student123')  # Default password
        
        db.session.add(user)
        db.session.flush()  # Get user ID
        
        # Create student record
        student = Student(
            user_id=user.id,
            roll_number=roll_number,
            first_name=first_name,
            last_name=last_name,
            date_of_birth=data.get('date_of_birth'),
            gender=data.get('gender'),
            phone=phone,
            address=data.get('address'),
            department_id=data.get('department_id'),
            year=year,
            semester=data.get('semester'),
            guardian_name=data.get('guardian_name'),
            guardian_phone=data.get('guardian_phone'),
            guardian_email=data.get('guardian_email'),
            blood_group=data.get('blood_group')
        )
        
        db.session.add(student)
        db.session.commit()
        
        return success_response('Student created successfully', {
            'student': student.to_dict(),
            'default_password': 'student123'
        }, 201)
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to create student: {str(e)}', 500)

@students_bp.route('/<int:student_id>', methods=['PUT'])
@admin_required
def update_student(current_user, student_id):
    """Update a student."""
    try:
        student = Student.query.filter_by(id=student_id, is_active=True).first()
        
        if not student:
            return error_response('Student not found', 404)
        
        data = request.get_json()
        if not data:
            return error_response('No data provided')
        
        # Update fields if provided
        if 'first_name' in data:
            student.first_name = data['first_name'].strip()
        
        if 'last_name' in data:
            student.last_name = data['last_name'].strip()
        
        if 'date_of_birth' in data:
            student.date_of_birth = data['date_of_birth']
        
        if 'gender' in data:
            student.gender = data['gender']
        
        if 'phone' in data:
            phone = data['phone'].strip()
            if phone and not validate_phone(phone):
                return error_response('Invalid phone number format')
            student.phone = phone
        
        if 'address' in data:
            student.address = data['address']
        
        if 'year' in data:
            student.year = data['year']
        
        if 'semester' in data:
            student.semester = data['semester']
        
        if 'guardian_name' in data:
            student.guardian_name = data['guardian_name']
        
        if 'guardian_phone' in data:
            student.guardian_phone = data['guardian_phone']
        
        if 'guardian_email' in data:
            student.guardian_email = data['guardian_email']
        
        if 'blood_group' in data:
            student.blood_group = data['blood_group']
        
        # Update email in user table if provided
        if 'email' in data:
            email = data['email'].strip().lower()
            if not validate_email(email):
                return error_response('Invalid email format')
            
            # Check if email already exists for another user
            existing_user = User.query.filter(
                User.email == email, User.id != student.user_id
            ).first()
            if existing_user:
                return error_response('Email already exists')
            
            student.user.email = email
        
        db.session.commit()
        
        return success_response('Student updated successfully', {
            'student': student.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to update student: {str(e)}', 500)

@students_bp.route('/<int:student_id>', methods=['DELETE'])
@admin_required
def delete_student(current_user, student_id):
    """Delete (deactivate) a student."""
    try:
        student = Student.query.filter_by(id=student_id, is_active=True).first()
        
        if not student:
            return error_response('Student not found', 404)
        
        # Soft delete
        student.is_active = False
        student.user.is_active = False
        
        db.session.commit()
        
        return success_response('Student deleted successfully')
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to delete student: {str(e)}', 500)

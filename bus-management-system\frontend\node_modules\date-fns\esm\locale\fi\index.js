import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Finnish locale.
 * @language Finnish
 * @iso-639-2 fin
 * <AUTHOR> [@Pyppe]{@link https://github.com/Pyppe}
 * <AUTHOR> [@mikolajgrzyb]{@link https://github.com/mikolajgrzyb}
 * <AUTHOR> [@sjuvonen]{@link https://github.com/sjuvonen}
 */
var locale = {
  code: 'fi',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4
  }
};
export default locale;
{"ast": null, "code": "import { notifyManager } from '../core';\nimport { unstable_batchedUpdates } from './reactBatchedUpdates';\nnotifyManager.setBatchNotifyFunction(unstable_batchedUpdates);", "map": {"version": 3, "names": ["notify<PERSON><PERSON>ger", "unstable_batchedUpdates", "setBatchNotifyFunction"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/react/setBatchUpdatesFn.js"], "sourcesContent": ["import { notifyManager } from '../core';\nimport { unstable_batchedUpdates } from './reactBatchedUpdates';\nnotifyManager.setBatchNotifyFunction(unstable_batchedUpdates);"], "mappings": "AAAA,SAASA,aAAa,QAAQ,SAAS;AACvC,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/DD,aAAa,CAACE,sBAAsB,CAACD,uBAAuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
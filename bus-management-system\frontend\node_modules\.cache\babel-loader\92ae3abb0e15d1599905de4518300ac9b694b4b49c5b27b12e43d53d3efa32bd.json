{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileX = createLucideIcon(\"FileX\", [[\"path\", {\n  d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\",\n  key: \"1nnpy2\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"line\", {\n  x1: \"9.5\",\n  x2: \"14.5\",\n  y1: \"12.5\",\n  y2: \"17.5\",\n  key: \"izs6du\"\n}], [\"line\", {\n  x1: \"14.5\",\n  x2: \"9.5\",\n  y1: \"12.5\",\n  y2: \"17.5\",\n  key: \"1lehlj\"\n}]]);\nexport { FileX as default };", "map": {"version": 3, "names": ["FileX", "createLucideIcon", "d", "key", "points", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\file-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWNy41TDE0LjUgMnoiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPGxpbmUgeDE9IjkuNSIgeDI9IjE0LjUiIHkxPSIxMi41IiB5Mj0iMTcuNSIgLz4KICA8bGluZSB4MT0iMTQuNSIgeDI9IjkuNSIgeTE9IjEyLjUiIHkyPSIxNy41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileX = createLucideIcon('FileX', [\n  [\n    'path',\n    {\n      d: 'M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z',\n      key: '1nnpy2',\n    },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['line', { x1: '9.5', x2: '14.5', y1: '12.5', y2: '17.5', key: 'izs6du' }],\n  ['line', { x1: '14.5', x2: '9.5', y1: '12.5', y2: '17.5', key: '1lehlj' }],\n]);\n\nexport default FileX;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEE,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAL,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,QAAQ;EAAEE,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
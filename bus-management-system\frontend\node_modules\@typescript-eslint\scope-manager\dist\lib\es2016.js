"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2016 = void 0;
const es2015_1 = require("./es2015");
const es2016_array_include_1 = require("./es2016.array.include");
exports.es2016 = Object.assign(Object.assign({}, es2015_1.es2015), es2016_array_include_1.es2016_array_include);
//# sourceMappingURL=es2016.js.map
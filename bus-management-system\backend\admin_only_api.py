#!/usr/bin/env python3
"""
Complete Admin-Only Transport Management System API
Full functionality for admin users with comprehensive data
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import timedelta, datetime, date
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'admin-transport-secret-key'
app.config['JWT_SECRET_KEY'] = 'admin-transport-jwt-secret'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

jwt = JWTManager(app)
CORS(app, origins=['http://localhost:3000'])

# Admin user only
ADMIN_USER = {
    'id': 1,
    'username': 'admin',
    'email': '<EMAIL>',
    'password_hash': generate_password_hash('admin123'),
    'role': 'admin',
    'first_name': 'Transport',
    'last_name': 'Administrator',
    'phone': '+1234567890'
}

# Comprehensive transport data
BUSES = [
    {
        'id': 1, 'bus_number': 'TRP001', 'registration_number': 'KA01AB1234',
        'bus_type': 'AC', 'capacity': 45, 'manufacturer': 'Tata', 'model': 'Starbus',
        'year_of_manufacture': 2020, 'fuel_type': 'Diesel', 'mileage': 8.5,
        'status': 'Active', 'occupancy_percentage': 85.0, 'current_occupancy': 38,
        'driver_name': 'John Smith', 'conductor_name': 'Mike Wilson',
        'route_name': 'City Center → School District', 'route_id': 1,
        'is_insurance_expired': False, 'is_rc_expired': False,
        'days_to_insurance_expiry': 180, 'insurance_number': 'INS001234',
        'rc_number': 'RC001234', 'current_location_lat': 12.9716,
        'current_location_lng': 77.5946, 'last_maintenance': '2024-01-15'
    },
    {
        'id': 2, 'bus_number': 'TRP002', 'registration_number': 'KA01CD5678',
        'bus_type': 'Non-AC', 'capacity': 50, 'manufacturer': 'Ashok Leyland', 'model': 'Viking',
        'year_of_manufacture': 2019, 'fuel_type': 'Diesel', 'mileage': 7.2,
        'status': 'Active', 'occupancy_percentage': 72.0, 'current_occupancy': 36,
        'driver_name': 'Sarah Johnson', 'conductor_name': 'Lisa Brown',
        'route_name': 'Residential Area → College Campus', 'route_id': 2,
        'is_insurance_expired': False, 'is_rc_expired': False,
        'days_to_insurance_expiry': 90, 'insurance_number': 'INS005678',
        'rc_number': 'RC005678', 'current_location_lat': 12.9352,
        'current_location_lng': 77.6245, 'last_maintenance': '2024-02-01'
    },
    {
        'id': 3, 'bus_number': 'TRP003', 'registration_number': 'KA01EF9012',
        'bus_type': 'AC', 'capacity': 40, 'manufacturer': 'Mahindra', 'model': 'Tourister',
        'year_of_manufacture': 2021, 'fuel_type': 'CNG', 'mileage': 12.0,
        'status': 'Maintenance', 'occupancy_percentage': 0.0, 'current_occupancy': 0,
        'driver_name': None, 'conductor_name': None,
        'route_name': None, 'route_id': None,
        'is_insurance_expired': False, 'is_rc_expired': False,
        'days_to_insurance_expiry': 200, 'insurance_number': 'INS009012',
        'rc_number': 'RC009012', 'current_location_lat': None,
        'current_location_lng': None, 'last_maintenance': '2024-01-20'
    },
    {
        'id': 4, 'bus_number': 'TRP004', 'registration_number': 'KA01GH3456',
        'bus_type': 'Non-AC', 'capacity': 55, 'manufacturer': 'Eicher', 'model': 'Skyline',
        'year_of_manufacture': 2018, 'fuel_type': 'Diesel', 'mileage': 6.8,
        'status': 'Active', 'occupancy_percentage': 90.0, 'current_occupancy': 50,
        'driver_name': 'Robert Davis', 'conductor_name': 'Anna Wilson',
        'route_name': 'Industrial Area → Tech Park', 'route_id': 3,
        'is_insurance_expired': True, 'is_rc_expired': False,
        'days_to_insurance_expiry': -15, 'insurance_number': 'INS003456',
        'rc_number': 'RC003456', 'current_location_lat': 12.8456,
        'current_location_lng': 77.6632, 'last_maintenance': '2024-01-10'
    },
    {
        'id': 5, 'bus_number': 'TRP005', 'registration_number': 'KA01IJ7890',
        'bus_type': 'Electric', 'capacity': 35, 'manufacturer': 'BYD', 'model': 'K7M',
        'year_of_manufacture': 2022, 'fuel_type': 'Electric', 'mileage': 0.8,
        'status': 'Active', 'occupancy_percentage': 65.0, 'current_occupancy': 23,
        'driver_name': 'Emily Chen', 'conductor_name': 'David Kumar',
        'route_name': 'Metro Station → Airport', 'route_id': 4,
        'is_insurance_expired': False, 'is_rc_expired': False,
        'days_to_insurance_expiry': 300, 'insurance_number': 'INS007890',
        'rc_number': 'RC007890', 'current_location_lat': 13.1986,
        'current_location_lng': 77.7066, 'last_maintenance': '2024-02-10'
    }
]

DRIVERS = [
    {
        'id': 1, 'employee_id': 'DRV001', 'full_name': 'John Smith',
        'driver_type': 'Driver', 'license_number': 'DL123456789',
        'license_expiry': '2025-12-31', 'phone': '+**********',
        'status': 'Available', 'shift_type': 'Morning', 'rating': 4.8,
        'experience_years': 8, 'assigned_bus': 'TRP001'
    },
    {
        'id': 2, 'employee_id': 'DRV002', 'full_name': 'Sarah Johnson',
        'driver_type': 'Driver', 'license_number': 'DL987654321',
        'license_expiry': '2025-08-15', 'phone': '+**********',
        'status': 'On Trip', 'shift_type': 'Evening', 'rating': 4.6,
        'experience_years': 5, 'assigned_bus': 'TRP002'
    },
    {
        'id': 3, 'employee_id': 'CON001', 'full_name': 'Mike Wilson',
        'driver_type': 'Conductor', 'license_number': 'DL456789123',
        'license_expiry': '2024-10-20', 'phone': '+**********',
        'status': 'Available', 'shift_type': 'Full Day', 'rating': 4.7,
        'experience_years': 3, 'assigned_bus': 'TRP001'
    },
    {
        'id': 4, 'employee_id': 'DRV003', 'full_name': 'Robert Davis',
        'driver_type': 'Driver', 'license_number': 'DL789123456',
        'license_expiry': '2025-06-30', 'phone': '+**********',
        'status': 'On Trip', 'shift_type': 'Morning', 'rating': 4.9,
        'experience_years': 12, 'assigned_bus': 'TRP004'
    },
    {
        'id': 5, 'employee_id': 'DRV004', 'full_name': 'Emily Chen',
        'driver_type': 'Driver', 'license_number': 'DL321654987',
        'license_expiry': '2026-03-15', 'phone': '+**********',
        'status': 'Available', 'shift_type': 'Evening', 'rating': 4.5,
        'experience_years': 4, 'assigned_bus': 'TRP005'
    }
]

ROUTES = [
    {
        'id': 1, 'route_name': 'City Center to School District', 'route_code': 'CC-SD-01',
        'start_location': 'City Center Bus Terminal', 'end_location': 'Green Valley School',
        'total_distance': 15.5, 'estimated_duration': 45, 'route_type': 'Urban',
        'status': 'Active', 'stops_count': 8, 'daily_trips': 6
    },
    {
        'id': 2, 'route_name': 'Residential Area to College Campus', 'route_code': 'RA-CC-02',
        'start_location': 'Sunrise Apartments', 'end_location': 'Tech University',
        'total_distance': 22.3, 'estimated_duration': 60, 'route_type': 'Suburban',
        'status': 'Active', 'stops_count': 12, 'daily_trips': 8
    },
    {
        'id': 3, 'route_name': 'Industrial Area to Tech Park', 'route_code': 'IA-TP-03',
        'start_location': 'Industrial Complex Gate', 'end_location': 'Software Tech Park',
        'total_distance': 18.7, 'estimated_duration': 50, 'route_type': 'Express',
        'status': 'Active', 'stops_count': 6, 'daily_trips': 10
    },
    {
        'id': 4, 'route_name': 'Metro Station to Airport', 'route_code': 'MS-AP-04',
        'start_location': 'Central Metro Station', 'end_location': 'International Airport',
        'total_distance': 35.2, 'estimated_duration': 75, 'route_type': 'Express',
        'status': 'Active', 'stops_count': 4, 'daily_trips': 12
    }
]

STUDENTS = [
    {
        'id': 1, 'student_id': 'STU001', 'full_name': 'Alice Brown',
        'class_name': 'Grade 10', 'section': 'A', 'roll_number': '101',
        'parent_name': 'Robert Brown', 'parent_phone': '+1234567896',
        'route_id': 1, 'pickup_stop': 'City Center', 'drop_stop': 'School Gate',
        'monthly_fee': 2000, 'status': 'Active', 'attendance_percentage': 95
    },
    {
        'id': 2, 'student_id': 'STU002', 'full_name': 'Bob Davis',
        'class_name': 'Grade 11', 'section': 'B', 'roll_number': '205',
        'parent_name': 'Linda Davis', 'parent_phone': '+**********',
        'route_id': 2, 'pickup_stop': 'Sunrise Apartments', 'drop_stop': 'University Gate',
        'monthly_fee': 2200, 'status': 'Active', 'attendance_percentage': 88
    }
]

# API Endpoints
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'message': 'Transport Management System API'})

@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if username == 'admin' and check_password_hash(ADMIN_USER['password_hash'], password):
            access_token = create_access_token(identity=ADMIN_USER['id'])
            return jsonify({
                'success': True,
                'data': {
                    'access_token': access_token,
                    'user': {
                        'id': ADMIN_USER['id'],
                        'username': ADMIN_USER['username'],
                        'email': ADMIN_USER['email'],
                        'role': ADMIN_USER['role'],
                        'first_name': ADMIN_USER['first_name'],
                        'last_name': ADMIN_USER['last_name']
                    }
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Invalid credentials. Only admin access available.'}), 401
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/auth/profile', methods=['GET'])
@jwt_required()
def get_profile():
    return jsonify({
        'success': True,
        'data': {
            'profile': {
                'id': ADMIN_USER['id'],
                'username': ADMIN_USER['username'],
                'email': ADMIN_USER['email'],
                'role': ADMIN_USER['role'],
                'first_name': ADMIN_USER['first_name'],
                'last_name': ADMIN_USER['last_name'],
                'phone': ADMIN_USER['phone']
            }
        }
    })

@app.route('/api/buses', methods=['GET'])
@jwt_required()
def get_buses():
    try:
        # Get filters
        status = request.args.get('status', '')
        bus_type = request.args.get('bus_type', '')
        search = request.args.get('search', '')
        
        filtered_buses = BUSES.copy()
        
        if status:
            filtered_buses = [b for b in filtered_buses if b['status'] == status]
        if bus_type:
            filtered_buses = [b for b in filtered_buses if b['bus_type'] == bus_type]
        if search:
            search_lower = search.lower()
            filtered_buses = [b for b in filtered_buses if 
                            search_lower in b['bus_number'].lower() or
                            search_lower in b['registration_number'].lower()]
        
        return jsonify({
            'success': True,
            'data': {
                'buses': filtered_buses,
                'pagination': {
                    'total': len(filtered_buses),
                    'pages': 1,
                    'current_page': 1,
                    'per_page': 10,
                    'has_next': False,
                    'has_prev': False
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/buses', methods=['POST'])
@jwt_required()
def create_bus():
    try:
        data = request.get_json()
        new_id = max([b['id'] for b in BUSES]) + 1
        
        new_bus = {
            'id': new_id,
            'bus_number': data.get('bus_number', f'TRP{new_id:03d}'),
            'registration_number': data.get('registration_number'),
            'bus_type': data.get('bus_type'),
            'capacity': data.get('capacity'),
            'manufacturer': data.get('manufacturer'),
            'model': data.get('model'),
            'year_of_manufacture': data.get('year_of_manufacture'),
            'fuel_type': data.get('fuel_type'),
            'mileage': data.get('mileage'),
            'status': 'Active',
            'occupancy_percentage': 0.0,
            'current_occupancy': 0,
            'driver_name': None,
            'conductor_name': None,
            'route_name': None,
            'route_id': None,
            'is_insurance_expired': False,
            'is_rc_expired': False,
            'days_to_insurance_expiry': 365,
            'insurance_number': data.get('insurance_number'),
            'rc_number': data.get('rc_number'),
            'current_location_lat': None,
            'current_location_lng': None,
            'last_maintenance': datetime.now().strftime('%Y-%m-%d')
        }
        
        BUSES.append(new_bus)
        
        return jsonify({
            'success': True,
            'data': {'bus': new_bus}
        }), 201
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/dashboard/stats', methods=['GET'])
@jwt_required()
def get_dashboard_stats():
    active_buses = len([b for b in BUSES if b['status'] == 'Active'])
    maintenance_buses = len([b for b in BUSES if b['status'] == 'Maintenance'])
    
    return jsonify({
        'success': True,
        'data': {
            'total_buses': len(BUSES),
            'active_buses': active_buses,
            'maintenance_buses': maintenance_buses,
            'total_drivers': len(DRIVERS),
            'total_students': len(STUDENTS),
            'total_routes': len(ROUTES),
            'daily_trips': sum([r['daily_trips'] for r in ROUTES]),
            'average_occupancy': sum([b['occupancy_percentage'] for b in BUSES if b['status'] == 'Active']) / active_buses if active_buses > 0 else 0,
            'expired_documents': len([b for b in BUSES if b['is_insurance_expired'] or b['is_rc_expired']]),
            'revenue_today': 15000,
            'fuel_consumption': 450
        }
    })

# Additional endpoints for complete functionality
@app.route('/api/drivers', methods=['GET'])
@jwt_required()
def get_drivers():
    return jsonify({'success': True, 'data': {'drivers': DRIVERS}})

@app.route('/api/routes', methods=['GET'])
@jwt_required()
def get_routes():
    return jsonify({'success': True, 'data': {'routes': ROUTES}})

@app.route('/api/students', methods=['GET'])
@jwt_required()
def get_students():
    return jsonify({'success': True, 'data': {'students': STUDENTS}})

@app.route('/api/buses/<int:bus_id>/documents', methods=['GET'])
@jwt_required()
def get_bus_documents(bus_id):
    bus = next((b for b in BUSES if b['id'] == bus_id), None)
    if not bus:
        return jsonify({'success': False, 'error': 'Bus not found'}), 404
    
    documents = [
        {
            'id': 1, 'document_type': 'Insurance', 'document_name': 'Vehicle Insurance',
            'document_number': bus['insurance_number'], 'file_size_mb': 1.2,
            'is_expired': bus['is_insurance_expired'], 'is_expiring_soon': bus['days_to_insurance_expiry'] < 30,
            'days_to_expiry': bus['days_to_insurance_expiry'], 'issue_date': '2023-06-30',
            'expiry_date': '2024-06-30', 'issuing_authority': 'National Insurance Company'
        },
        {
            'id': 2, 'document_type': 'RC', 'document_name': 'Registration Certificate',
            'document_number': bus['rc_number'], 'file_size_mb': 0.8,
            'is_expired': bus['is_rc_expired'], 'is_expiring_soon': False,
            'days_to_expiry': 365, 'issue_date': '2020-01-15',
            'expiry_date': '2025-01-15', 'issuing_authority': 'RTO Karnataka'
        }
    ]
    
    return jsonify({
        'success': True,
        'data': {
            'bus_id': bus_id,
            'bus_number': bus['bus_number'],
            'documents': documents
        }
    })

if __name__ == '__main__':
    print("🚌 Starting Complete Transport Management System (Admin Only)")
    print("=" * 60)
    print("📍 Backend: http://localhost:5001")
    print("🎯 Health: http://localhost:5001/api/health")
    print("👤 Admin Login: admin / admin123")
    print("✨ Features: Buses, Drivers, Routes, Students, Documents")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5001)

{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Map = createLucideIcon(\"Map\", [[\"polygon\", {\n  points: \"3 6 9 3 15 6 21 3 21 18 15 21 9 18 3 21\",\n  key: \"ok2ie8\"\n}], [\"line\", {\n  x1: \"9\",\n  x2: \"9\",\n  y1: \"3\",\n  y2: \"18\",\n  key: \"w34qz5\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"15\",\n  y1: \"6\",\n  y2: \"21\",\n  key: \"volv9a\"\n}]]);\nexport { Map as default };", "map": {"version": 3, "names": ["Map", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\map.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Map\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjMgNiA5IDMgMTUgNiAyMSAzIDIxIDE4IDE1IDIxIDkgMTggMyAyMSIgLz4KICA8bGluZSB4MT0iOSIgeDI9IjkiIHkxPSIzIiB5Mj0iMTgiIC8+CiAgPGxpbmUgeDE9IjE1IiB4Mj0iMTUiIHkxPSI2IiB5Mj0iMjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Map = createLucideIcon('Map', [\n  [\n    'polygon',\n    { points: '3 6 9 3 15 6 21 3 21 18 15 21 9 18 3 21', key: 'ok2ie8' },\n  ],\n  ['line', { x1: '9', x2: '9', y1: '3', y2: '18', key: 'w34qz5' }],\n  ['line', { x1: '15', x2: '15', y1: '6', y2: '21', key: 'volv9a' }],\n]);\n\nexport default Map;\n"], "mappings": ";;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CACE,WACA;EAAEC,MAAA,EAAQ,yCAA2C;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
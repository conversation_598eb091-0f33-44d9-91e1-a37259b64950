import React from 'react';
import { Query } from '../core';
import { Theme } from './theme';
export declare const isServer: boolean;
declare type StyledComponent<T> = T extends 'button' ? React.DetailedHTMLProps<React.ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> : T extends 'input' ? React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> : T extends 'select' ? React.DetailedHTMLProps<React.SelectHTMLAttributes<HTMLSelectElement>, HTMLSelectElement> : T extends keyof HTMLElementTagNameMap ? React.HTMLAttributes<HTMLElementTagNameMap[T]> : never;
export declare function getQueryStatusColor(query: Query, theme: Theme): "#3f4e60" | "#00ab52" | "#006bff" | "#ffb200";
export declare function getQueryStatusLabel(query: Query): "inactive" | "fetching" | "stale" | "fresh";
declare type Styles = React.CSSProperties | ((props: Record<string, any>, theme: Theme) => React.CSSProperties);
export declare function styled<T extends keyof HTMLElementTagNameMap>(type: T, newStyles: Styles, queries?: Record<string, Styles>): React.ForwardRefExoticComponent<React.PropsWithoutRef<StyledComponent<T>> & React.RefAttributes<HTMLElementTagNameMap[T]>>;
export declare function useIsMounted(): () => boolean;
/**
 * This hook is a safe useState version which schedules state updates in microtasks
 * to prevent updating a component state while React is rendering different components
 * or when the component is not mounted anymore.
 */
export declare function useSafeState<T>(initialState: T): [T, (value: T) => void];
/**
 * Displays a string regardless the type of the data
 * @param {unknown} value Value to be stringified
 */
export declare const displayValue: (value: unknown) => string;
export {};

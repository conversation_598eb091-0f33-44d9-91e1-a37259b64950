{"name": "postcss-normalize-repeat-style", "version": "5.1.1", "description": "Convert two value syntax for repeat-style into one value.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["LICENSE-MIT", "src", "types"], "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "homepage": "https://github.com/cssnano/cssnano", "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}}
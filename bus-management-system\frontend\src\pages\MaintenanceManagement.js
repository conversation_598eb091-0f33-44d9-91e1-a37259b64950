import React from 'react';
import { Wrench, Plus, AlertTriangle } from 'lucide-react';

const MaintenanceManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Maintenance Management</h1>
          <p className="text-gray-600">Predictive maintenance with AI-powered alerts</p>
        </div>
        <button className="btn btn-primary flex items-center space-x-2">
          <Plus className="h-5 w-5" />
          <span>Schedule Maintenance</span>
        </button>
      </div>

      <div className="card text-center py-12">
        <Wrench className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Maintenance Management</h3>
        <p className="text-gray-600 mb-6">
          Smart maintenance scheduling with predictive analytics and cost tracking.
        </p>
        <div className="space-y-2 text-sm text-gray-500">
          <p>✓ Predictive maintenance alerts using AI</p>
          <p>✓ Maintenance scheduling and tracking</p>
          <p>✓ Cost analysis and budgeting</p>
          <p>✓ Service provider management</p>
          <p>✓ Maintenance history and reports</p>
        </div>
      </div>
    </div>
  );
};

export default MaintenanceManagement;

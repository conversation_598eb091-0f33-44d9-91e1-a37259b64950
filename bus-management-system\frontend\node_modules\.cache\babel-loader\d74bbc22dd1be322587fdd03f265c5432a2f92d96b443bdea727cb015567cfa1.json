{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Bus = createLucideIcon(\"Bus\", [[\"path\", {\n  d: \"M19 17h2l.64-2.54c.24-.959.24-1.962 0-2.92l-1.07-4.27A3 3 0 0 0 17.66 5H4a2 2 0 0 0-2 2v10h2\",\n  key: \"wfsdqh\"\n}], [\"path\", {\n  d: \"M14 17H9\",\n  key: \"o2noo5\"\n}], [\"circle\", {\n  cx: \"6.5\",\n  cy: \"17.5\",\n  r: \"2.5\",\n  key: \"gc8oob\"\n}], [\"circle\", {\n  cx: \"16.5\",\n  cy: \"17.5\",\n  r: \"2.5\",\n  key: \"4btu0q\"\n}]]);\nexport { Bus as default };", "map": {"version": 3, "names": ["Bus", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\bus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Bus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTdoMmwuNjQtMi41NGMuMjQtLjk1OS4yNC0xLjk2MiAwLTIuOTJsLTEuMDctNC4yN0EzIDMgMCAwIDAgMTcuNjYgNUg0YTIgMiAwIDAgMC0yIDJ2MTBoMiIgLz4KICA8cGF0aCBkPSJNMTQgMTdIOSIgLz4KICA8Y2lyY2xlIGN4PSI2LjUiIGN5PSIxNy41IiByPSIyLjUiIC8+CiAgPGNpcmNsZSBjeD0iMTYuNSIgY3k9IjE3LjUiIHI9IjIuNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bus = createLucideIcon('Bus', [\n  [\n    'path',\n    {\n      d: 'M19 17h2l.64-2.54c.24-.959.24-1.962 0-2.92l-1.07-4.27A3 3 0 0 0 17.66 5H4a2 2 0 0 0-2 2v10h2',\n      key: 'wfsdqh',\n    },\n  ],\n  ['path', { d: 'M14 17H9', key: 'o2noo5' }],\n  ['circle', { cx: '6.5', cy: '17.5', r: '2.5', key: 'gc8oob' }],\n  ['circle', { cx: '16.5', cy: '17.5', r: '2.5', key: '4btu0q' }],\n]);\n\nexport default Bus;\n"], "mappings": ";;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAOC,EAAI;EAAQC,CAAG;EAAOH,GAAK;AAAA,CAAU,GAC7D,CAAC,QAAU;EAAEC,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAOH,GAAK;AAAA,CAAU,EAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
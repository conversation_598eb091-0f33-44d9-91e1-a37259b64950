{"ast": null, "code": "import React from 'react';\nexport default function useMediaQuery(query) {\n  // Keep track of the preference in state, start with the current match\n  var _React$useState = React.useState(function () {\n      if (typeof window !== 'undefined') {\n        return window.matchMedia && window.matchMedia(query).matches;\n      }\n    }),\n    isMatch = _React$useState[0],\n    setIsMatch = _React$useState[1]; // Watch for changes\n\n  React.useEffect(function () {\n    if (typeof window !== 'undefined') {\n      if (!window.matchMedia) {\n        return;\n      } // Create a matcher\n\n      var matcher = window.matchMedia(query); // Create our handler\n\n      var onChange = function onChange(_ref) {\n        var matches = _ref.matches;\n        return setIsMatch(matches);\n      }; // Listen for changes\n\n      matcher.addListener(onChange);\n      return function () {\n        // Stop listening for changes\n        matcher.removeListener(onChange);\n      };\n    }\n  }, [isMatch, query, setIsMatch]);\n  return isMatch;\n}", "map": {"version": 3, "names": ["React", "useMediaQuery", "query", "_React$useState", "useState", "window", "matchMedia", "matches", "isMatch", "setIsMatch", "useEffect", "matcher", "onChange", "_ref", "addListener", "removeListener"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/devtools/useMediaQuery.js"], "sourcesContent": ["import React from 'react';\nexport default function useMediaQuery(query) {\n  // Keep track of the preference in state, start with the current match\n  var _React$useState = React.useState(function () {\n    if (typeof window !== 'undefined') {\n      return window.matchMedia && window.matchMedia(query).matches;\n    }\n  }),\n      isMatch = _React$useState[0],\n      setIsMatch = _React$useState[1]; // Watch for changes\n\n\n  React.useEffect(function () {\n    if (typeof window !== 'undefined') {\n      if (!window.matchMedia) {\n        return;\n      } // Create a matcher\n\n\n      var matcher = window.matchMedia(query); // Create our handler\n\n      var onChange = function onChange(_ref) {\n        var matches = _ref.matches;\n        return setIsMatch(matches);\n      }; // Listen for changes\n\n\n      matcher.addListener(onChange);\n      return function () {\n        // Stop listening for changes\n        matcher.removeListener(onChange);\n      };\n    }\n  }, [isMatch, query, setIsMatch]);\n  return isMatch;\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C;EACA,IAAIC,eAAe,GAAGH,KAAK,CAACI,QAAQ,CAAC,YAAY;MAC/C,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;QACjC,OAAOA,MAAM,CAACC,UAAU,IAAID,MAAM,CAACC,UAAU,CAACJ,KAAK,CAAC,CAACK,OAAO;MAC9D;IACF,CAAC,CAAC;IACEC,OAAO,GAAGL,eAAe,CAAC,CAAC,CAAC;IAC5BM,UAAU,GAAGN,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGrCH,KAAK,CAACU,SAAS,CAAC,YAAY;IAC1B,IAAI,OAAOL,MAAM,KAAK,WAAW,EAAE;MACjC,IAAI,CAACA,MAAM,CAACC,UAAU,EAAE;QACtB;MACF,CAAC,CAAC;;MAGF,IAAIK,OAAO,GAAGN,MAAM,CAACC,UAAU,CAACJ,KAAK,CAAC,CAAC,CAAC;;MAExC,IAAIU,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;QACrC,IAAIN,OAAO,GAAGM,IAAI,CAACN,OAAO;QAC1B,OAAOE,UAAU,CAACF,OAAO,CAAC;MAC5B,CAAC,CAAC,CAAC;;MAGHI,OAAO,CAACG,WAAW,CAACF,QAAQ,CAAC;MAC7B,OAAO,YAAY;QACjB;QACAD,OAAO,CAACI,cAAc,CAACH,QAAQ,CAAC;MAClC,CAAC;IACH;EACF,CAAC,EAAE,CAACJ,OAAO,EAAEN,KAAK,EAAEO,UAAU,CAAC,CAAC;EAChC,OAAOD,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\pages\\\\DriverManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useQuery } from 'react-query';\nimport { Users, Plus, Search, Filter, Star, AlertTriangle, Phone, Calendar } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DriverManagement = () => {\n  _s();\n  const [filters, setFilters] = useState({\n    search: '',\n    driver_type: '',\n    status: ''\n  });\n\n  // Mock driver data - in real app this would come from API\n  const drivers = [{\n    id: 1,\n    employee_id: 'DRV001',\n    full_name: '<PERSON>',\n    driver_type: 'Driver',\n    license_number: 'DL123456789',\n    license_expiry: '2025-12-31',\n    phone: '+**********',\n    status: 'Available',\n    shift_type: 'Morning',\n    rating: 4.8,\n    experience_years: 8,\n    assigned_bus: 'TRP001',\n    total_trips: 1250,\n    on_time_percentage: 96\n  }, {\n    id: 2,\n    employee_id: 'DRV002',\n    full_name: '<PERSON>',\n    driver_type: 'Driver',\n    license_number: 'DL987654321',\n    license_expiry: '2025-08-15',\n    phone: '+**********',\n    status: 'On Trip',\n    shift_type: 'Evening',\n    rating: 4.6,\n    experience_years: 5,\n    assigned_bus: 'TRP002',\n    total_trips: 890,\n    on_time_percentage: 94\n  }, {\n    id: 3,\n    employee_id: 'CON001',\n    full_name: 'Mike Wilson',\n    driver_type: 'Conductor',\n    license_number: 'DL456789123',\n    license_expiry: '2024-10-20',\n    phone: '+**********',\n    status: 'Available',\n    shift_type: 'Full Day',\n    rating: 4.7,\n    experience_years: 3,\n    assigned_bus: 'TRP001',\n    total_trips: 650,\n    on_time_percentage: 92\n  }, {\n    id: 4,\n    employee_id: 'DRV003',\n    full_name: 'Robert Davis',\n    driver_type: 'Driver',\n    license_number: 'DL789123456',\n    license_expiry: '2025-06-30',\n    phone: '+**********',\n    status: 'On Trip',\n    shift_type: 'Morning',\n    rating: 4.9,\n    experience_years: 12,\n    assigned_bus: 'TRP004',\n    total_trips: 2100,\n    on_time_percentage: 98\n  }, {\n    id: 5,\n    employee_id: 'DRV004',\n    full_name: 'Emily Chen',\n    driver_type: 'Driver',\n    license_number: 'DL321654987',\n    license_expiry: '2026-03-15',\n    phone: '+**********',\n    status: 'Available',\n    shift_type: 'Evening',\n    rating: 4.5,\n    experience_years: 4,\n    assigned_bus: 'TRP005',\n    total_trips: 720,\n    on_time_percentage: 91\n  }];\n  const handleFilterChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const getStatusBadge = status => {\n    const statusClasses = {\n      Available: 'badge-success',\n      'On Trip': 'badge-primary',\n      'Off Duty': 'badge-secondary',\n      'On Leave': 'badge-warning'\n    };\n    return `badge ${statusClasses[status] || 'badge-secondary'}`;\n  };\n  const isLicenseExpiring = expiryDate => {\n    const today = new Date();\n    const expiry = new Date(expiryDate);\n    const daysToExpiry = Math.ceil((expiry - today) / (1000 * 60 * 60 * 24));\n    return daysToExpiry <= 90; // Alert if expiring within 90 days\n  };\n  const filteredDrivers = drivers.filter(driver => {\n    const matchesSearch = !filters.search || driver.full_name.toLowerCase().includes(filters.search.toLowerCase()) || driver.employee_id.toLowerCase().includes(filters.search.toLowerCase());\n    const matchesType = !filters.driver_type || driver.driver_type === filters.driver_type;\n    const matchesStatus = !filters.status || driver.status === filters.status;\n    return matchesSearch && matchesType && matchesStatus;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Driver Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage drivers and conductors with performance tracking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Add Driver\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"search\",\n            placeholder: \"Search drivers...\",\n            value: filters.search,\n            onChange: handleFilterChange,\n            className: \"input pl-10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"driver_type\",\n          value: filters.driver_type,\n          onChange: handleFilterChange,\n          className: \"input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Types\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Driver\",\n            children: \"Driver\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Conductor\",\n            children: \"Conductor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"status\",\n          value: filters.status,\n          onChange: handleFilterChange,\n          className: \"input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Available\",\n            children: \"Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"On Trip\",\n            children: \"On Trip\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Off Duty\",\n            children: \"Off Duty\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"On Leave\",\n            children: \"On Leave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"More Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: filteredDrivers.map(driver => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card hover:shadow-lg transition-shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-start mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: driver.full_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: [driver.employee_id, \" \\u2022 \", driver.driver_type]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [isLicenseExpiring(driver.license_expiry) && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n              className: \"h-5 w-5 text-yellow-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: getStatusBadge(driver.status),\n              children: driver.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Experience:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [driver.experience_years, \" years\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Rating:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1\",\n              children: [/*#__PURE__*/_jsxDEV(Star, {\n                className: \"h-4 w-4 text-yellow-500 fill-current\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: driver.rating\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Assigned Bus:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: driver.assigned_bus || 'Not Assigned'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Shift:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: driver.shift_type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Total Trips:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: driver.total_trips.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"On-Time Rate:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [driver.on_time_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"License:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [isLicenseExpiring(driver.license_expiry) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-yellow-600 text-xs\",\n                children: \"Expires Soon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-600 text-xs\",\n                children: \"Valid\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n                className: \"h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: [\"Expires: \", new Date(driver.license_expiry).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"text-primary-600 hover:text-primary-900\",\n              children: /*#__PURE__*/_jsxDEV(Users, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"text-blue-600 hover:text-blue-900\",\n              children: /*#__PURE__*/_jsxDEV(Phone, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-outline btn-sm\",\n            children: \"View Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)]\n      }, driver.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), filteredDrivers.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Users, {\n        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-500 mb-4\",\n        children: \"No drivers found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        children: \"Add Your First Driver\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(DriverManagement, \"J2hAPKnM/O7CkUTnplNtVLijsr4=\");\n_c = DriverManagement;\nexport default DriverManagement;\nvar _c;\n$RefreshReg$(_c, \"DriverManagement\");", "map": {"version": 3, "names": ["React", "useState", "useQuery", "Users", "Plus", "Search", "Filter", "Star", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Phone", "Calendar", "jsxDEV", "_jsxDEV", "DriverManagement", "_s", "filters", "setFilters", "search", "driver_type", "status", "drivers", "id", "employee_id", "full_name", "license_number", "license_expiry", "phone", "shift_type", "rating", "experience_years", "assigned_bus", "total_trips", "on_time_percentage", "handleFilterChange", "e", "name", "value", "target", "prev", "getStatusBadge", "statusClasses", "Available", "isLicenseExpiring", "expiryDate", "today", "Date", "expiry", "daysToExpiry", "Math", "ceil", "filteredDrivers", "filter", "driver", "matchesSearch", "toLowerCase", "includes", "matchesType", "matchesStatus", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "map", "toLocaleString", "toLocaleDateString", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/pages/DriverManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useQuery } from 'react-query';\nimport { Users, Plus, Search, Filter, Star, AlertTriangle, Phone, Calendar } from 'lucide-react';\n\nconst DriverManagement = () => {\n  const [filters, setFilters] = useState({\n    search: '',\n    driver_type: '',\n    status: '',\n  });\n\n  // Mock driver data - in real app this would come from API\n  const drivers = [\n    {\n      id: 1, employee_id: 'DRV001', full_name: '<PERSON>',\n      driver_type: 'Driver', license_number: 'DL123456789',\n      license_expiry: '2025-12-31', phone: '+**********',\n      status: 'Available', shift_type: 'Morning', rating: 4.8,\n      experience_years: 8, assigned_bus: 'TRP001',\n      total_trips: 1250, on_time_percentage: 96\n    },\n    {\n      id: 2, employee_id: 'DRV002', full_name: '<PERSON>',\n      driver_type: 'Driver', license_number: 'DL987654321',\n      license_expiry: '2025-08-15', phone: '+**********',\n      status: 'On Trip', shift_type: 'Evening', rating: 4.6,\n      experience_years: 5, assigned_bus: 'TRP002',\n      total_trips: 890, on_time_percentage: 94\n    },\n    {\n      id: 3, employee_id: 'CON001', full_name: 'Mike Wilson',\n      driver_type: 'Conductor', license_number: 'DL456789123',\n      license_expiry: '2024-10-20', phone: '+**********',\n      status: 'Available', shift_type: 'Full Day', rating: 4.7,\n      experience_years: 3, assigned_bus: 'TRP001',\n      total_trips: 650, on_time_percentage: 92\n    },\n    {\n      id: 4, employee_id: 'DRV003', full_name: 'Robert Davis',\n      driver_type: 'Driver', license_number: 'DL789123456',\n      license_expiry: '2025-06-30', phone: '+**********',\n      status: 'On Trip', shift_type: 'Morning', rating: 4.9,\n      experience_years: 12, assigned_bus: 'TRP004',\n      total_trips: 2100, on_time_percentage: 98\n    },\n    {\n      id: 5, employee_id: 'DRV004', full_name: 'Emily Chen',\n      driver_type: 'Driver', license_number: 'DL321654987',\n      license_expiry: '2026-03-15', phone: '+**********',\n      status: 'Available', shift_type: 'Evening', rating: 4.5,\n      experience_years: 4, assigned_bus: 'TRP005',\n      total_trips: 720, on_time_percentage: 91\n    }\n  ];\n\n  const handleFilterChange = (e) => {\n    const { name, value } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const getStatusBadge = (status) => {\n    const statusClasses = {\n      Available: 'badge-success',\n      'On Trip': 'badge-primary',\n      'Off Duty': 'badge-secondary',\n      'On Leave': 'badge-warning',\n    };\n    return `badge ${statusClasses[status] || 'badge-secondary'}`;\n  };\n\n  const isLicenseExpiring = (expiryDate) => {\n    const today = new Date();\n    const expiry = new Date(expiryDate);\n    const daysToExpiry = Math.ceil((expiry - today) / (1000 * 60 * 60 * 24));\n    return daysToExpiry <= 90; // Alert if expiring within 90 days\n  };\n\n  const filteredDrivers = drivers.filter(driver => {\n    const matchesSearch = !filters.search ||\n      driver.full_name.toLowerCase().includes(filters.search.toLowerCase()) ||\n      driver.employee_id.toLowerCase().includes(filters.search.toLowerCase());\n    const matchesType = !filters.driver_type || driver.driver_type === filters.driver_type;\n    const matchesStatus = !filters.status || driver.status === filters.status;\n\n    return matchesSearch && matchesType && matchesStatus;\n  });\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Driver Management</h1>\n          <p className=\"text-gray-600\">Manage drivers and conductors with performance tracking</p>\n        </div>\n        <button className=\"btn btn-primary flex items-center space-x-2\">\n          <Plus className=\"h-5 w-5\" />\n          <span>Add Driver</span>\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"card\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n            <input\n              type=\"text\"\n              name=\"search\"\n              placeholder=\"Search drivers...\"\n              value={filters.search}\n              onChange={handleFilterChange}\n              className=\"input pl-10\"\n            />\n          </div>\n\n          <select\n            name=\"driver_type\"\n            value={filters.driver_type}\n            onChange={handleFilterChange}\n            className=\"input\"\n          >\n            <option value=\"\">All Types</option>\n            <option value=\"Driver\">Driver</option>\n            <option value=\"Conductor\">Conductor</option>\n          </select>\n\n          <select\n            name=\"status\"\n            value={filters.status}\n            onChange={handleFilterChange}\n            className=\"input\"\n          >\n            <option value=\"\">All Status</option>\n            <option value=\"Available\">Available</option>\n            <option value=\"On Trip\">On Trip</option>\n            <option value=\"Off Duty\">Off Duty</option>\n            <option value=\"On Leave\">On Leave</option>\n          </select>\n\n          <button className=\"btn btn-outline flex items-center space-x-2\">\n            <Filter className=\"h-5 w-5\" />\n            <span>More Filters</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Drivers Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredDrivers.map((driver) => (\n          <div key={driver.id} className=\"card hover:shadow-lg transition-shadow\">\n            <div className=\"flex justify-between items-start mb-4\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">\n                  {driver.full_name}\n                </h3>\n                <p className=\"text-sm text-gray-600\">{driver.employee_id} • {driver.driver_type}</p>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                {isLicenseExpiring(driver.license_expiry) && (\n                  <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />\n                )}\n                <span className={getStatusBadge(driver.status)}>\n                  {driver.status}\n                </span>\n              </div>\n            </div>\n\n            <div className=\"space-y-2 text-sm\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Experience:</span>\n                <span className=\"font-medium\">{driver.experience_years} years</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Rating:</span>\n                <div className=\"flex items-center space-x-1\">\n                  <Star className=\"h-4 w-4 text-yellow-500 fill-current\" />\n                  <span className=\"font-medium\">{driver.rating}</span>\n                </div>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Assigned Bus:</span>\n                <span className=\"font-medium\">{driver.assigned_bus || 'Not Assigned'}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Shift:</span>\n                <span className=\"font-medium\">{driver.shift_type}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Total Trips:</span>\n                <span className=\"font-medium\">{driver.total_trips.toLocaleString()}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">On-Time Rate:</span>\n                <span className=\"font-medium\">{driver.on_time_percentage}%</span>\n              </div>\n            </div>\n\n            {/* License Status */}\n            <div className=\"mt-4 pt-4 border-t border-gray-200\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-gray-600\">License:</span>\n                <div className=\"flex items-center space-x-2\">\n                  {isLicenseExpiring(driver.license_expiry) ? (\n                    <span className=\"text-yellow-600 text-xs\">Expires Soon</span>\n                  ) : (\n                    <span className=\"text-green-600 text-xs\">Valid</span>\n                  )}\n                  <Calendar className=\"h-4 w-4 text-gray-400\" />\n                </div>\n              </div>\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Expires: {new Date(driver.license_expiry).toLocaleDateString()}\n              </p>\n            </div>\n\n            {/* Actions */}\n            <div className=\"mt-4 flex justify-between items-center\">\n              <div className=\"flex space-x-2\">\n                <button className=\"text-primary-600 hover:text-primary-900\">\n                  <Users className=\"h-4 w-4\" />\n                </button>\n                <button className=\"text-blue-600 hover:text-blue-900\">\n                  <Phone className=\"h-4 w-4\" />\n                </button>\n              </div>\n\n              <button className=\"btn btn-outline btn-sm\">\n                View Profile\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Empty State */}\n      {filteredDrivers.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Users className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <div className=\"text-gray-500 mb-4\">No drivers found</div>\n          <button className=\"btn btn-primary\">\n            Add Your First Driver\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DriverManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,aAAa,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC;IACrCgB,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,EAAE,EAAE,CAAC;IAAEC,WAAW,EAAE,QAAQ;IAAEC,SAAS,EAAE,YAAY;IACrDL,WAAW,EAAE,QAAQ;IAAEM,cAAc,EAAE,aAAa;IACpDC,cAAc,EAAE,YAAY;IAAEC,KAAK,EAAE,aAAa;IAClDP,MAAM,EAAE,WAAW;IAAEQ,UAAU,EAAE,SAAS;IAAEC,MAAM,EAAE,GAAG;IACvDC,gBAAgB,EAAE,CAAC;IAAEC,YAAY,EAAE,QAAQ;IAC3CC,WAAW,EAAE,IAAI;IAAEC,kBAAkB,EAAE;EACzC,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IAAEC,WAAW,EAAE,QAAQ;IAAEC,SAAS,EAAE,eAAe;IACxDL,WAAW,EAAE,QAAQ;IAAEM,cAAc,EAAE,aAAa;IACpDC,cAAc,EAAE,YAAY;IAAEC,KAAK,EAAE,aAAa;IAClDP,MAAM,EAAE,SAAS;IAAEQ,UAAU,EAAE,SAAS;IAAEC,MAAM,EAAE,GAAG;IACrDC,gBAAgB,EAAE,CAAC;IAAEC,YAAY,EAAE,QAAQ;IAC3CC,WAAW,EAAE,GAAG;IAAEC,kBAAkB,EAAE;EACxC,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IAAEC,WAAW,EAAE,QAAQ;IAAEC,SAAS,EAAE,aAAa;IACtDL,WAAW,EAAE,WAAW;IAAEM,cAAc,EAAE,aAAa;IACvDC,cAAc,EAAE,YAAY;IAAEC,KAAK,EAAE,aAAa;IAClDP,MAAM,EAAE,WAAW;IAAEQ,UAAU,EAAE,UAAU;IAAEC,MAAM,EAAE,GAAG;IACxDC,gBAAgB,EAAE,CAAC;IAAEC,YAAY,EAAE,QAAQ;IAC3CC,WAAW,EAAE,GAAG;IAAEC,kBAAkB,EAAE;EACxC,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IAAEC,WAAW,EAAE,QAAQ;IAAEC,SAAS,EAAE,cAAc;IACvDL,WAAW,EAAE,QAAQ;IAAEM,cAAc,EAAE,aAAa;IACpDC,cAAc,EAAE,YAAY;IAAEC,KAAK,EAAE,aAAa;IAClDP,MAAM,EAAE,SAAS;IAAEQ,UAAU,EAAE,SAAS;IAAEC,MAAM,EAAE,GAAG;IACrDC,gBAAgB,EAAE,EAAE;IAAEC,YAAY,EAAE,QAAQ;IAC5CC,WAAW,EAAE,IAAI;IAAEC,kBAAkB,EAAE;EACzC,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IAAEC,WAAW,EAAE,QAAQ;IAAEC,SAAS,EAAE,YAAY;IACrDL,WAAW,EAAE,QAAQ;IAAEM,cAAc,EAAE,aAAa;IACpDC,cAAc,EAAE,YAAY;IAAEC,KAAK,EAAE,aAAa;IAClDP,MAAM,EAAE,WAAW;IAAEQ,UAAU,EAAE,SAAS;IAAEC,MAAM,EAAE,GAAG;IACvDC,gBAAgB,EAAE,CAAC;IAAEC,YAAY,EAAE,QAAQ;IAC3CC,WAAW,EAAE,GAAG;IAAEC,kBAAkB,EAAE;EACxC,CAAC,CACF;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCrB,UAAU,CAACsB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,cAAc,GAAIpB,MAAM,IAAK;IACjC,MAAMqB,aAAa,GAAG;MACpBC,SAAS,EAAE,eAAe;MAC1B,SAAS,EAAE,eAAe;MAC1B,UAAU,EAAE,iBAAiB;MAC7B,UAAU,EAAE;IACd,CAAC;IACD,OAAO,SAASD,aAAa,CAACrB,MAAM,CAAC,IAAI,iBAAiB,EAAE;EAC9D,CAAC;EAED,MAAMuB,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,MAAM,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACnC,MAAMI,YAAY,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACH,MAAM,GAAGF,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACxE,OAAOG,YAAY,IAAI,EAAE,CAAC,CAAC;EAC7B,CAAC;EAED,MAAMG,eAAe,GAAG9B,OAAO,CAAC+B,MAAM,CAACC,MAAM,IAAI;IAC/C,MAAMC,aAAa,GAAG,CAACtC,OAAO,CAACE,MAAM,IACnCmC,MAAM,CAAC7B,SAAS,CAAC+B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxC,OAAO,CAACE,MAAM,CAACqC,WAAW,CAAC,CAAC,CAAC,IACrEF,MAAM,CAAC9B,WAAW,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxC,OAAO,CAACE,MAAM,CAACqC,WAAW,CAAC,CAAC,CAAC;IACzE,MAAME,WAAW,GAAG,CAACzC,OAAO,CAACG,WAAW,IAAIkC,MAAM,CAAClC,WAAW,KAAKH,OAAO,CAACG,WAAW;IACtF,MAAMuC,aAAa,GAAG,CAAC1C,OAAO,CAACI,MAAM,IAAIiC,MAAM,CAACjC,MAAM,KAAKJ,OAAO,CAACI,MAAM;IAEzE,OAAOkC,aAAa,IAAIG,WAAW,IAAIC,aAAa;EACtD,CAAC,CAAC;EAEF,oBACE7C,OAAA;IAAK8C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/C,OAAA;MAAK8C,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD/C,OAAA;QAAA+C,QAAA,gBACE/C,OAAA;UAAI8C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEnD,OAAA;UAAG8C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC,eACNnD,OAAA;QAAQ8C,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC7D/C,OAAA,CAACR,IAAI;UAACsD,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BnD,OAAA;UAAA+C,QAAA,EAAM;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnD,OAAA;MAAK8C,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB/C,OAAA;QAAK8C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD/C,OAAA;UAAK8C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/C,OAAA,CAACP,MAAM;YAACqD,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FnD,OAAA;YACEoD,IAAI,EAAC,MAAM;YACX7B,IAAI,EAAC,QAAQ;YACb8B,WAAW,EAAC,mBAAmB;YAC/B7B,KAAK,EAAErB,OAAO,CAACE,MAAO;YACtBiD,QAAQ,EAAEjC,kBAAmB;YAC7ByB,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnD,OAAA;UACEuB,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAErB,OAAO,CAACG,WAAY;UAC3BgD,QAAQ,EAAEjC,kBAAmB;UAC7ByB,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAEjB/C,OAAA;YAAQwB,KAAK,EAAC,EAAE;YAAAuB,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACnCnD,OAAA;YAAQwB,KAAK,EAAC,QAAQ;YAAAuB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCnD,OAAA;YAAQwB,KAAK,EAAC,WAAW;YAAAuB,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAETnD,OAAA;UACEuB,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAErB,OAAO,CAACI,MAAO;UACtB+C,QAAQ,EAAEjC,kBAAmB;UAC7ByB,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAEjB/C,OAAA;YAAQwB,KAAK,EAAC,EAAE;YAAAuB,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpCnD,OAAA;YAAQwB,KAAK,EAAC,WAAW;YAAAuB,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CnD,OAAA;YAAQwB,KAAK,EAAC,SAAS;YAAAuB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCnD,OAAA;YAAQwB,KAAK,EAAC,UAAU;YAAAuB,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CnD,OAAA;YAAQwB,KAAK,EAAC,UAAU;YAAAuB,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAETnD,OAAA;UAAQ8C,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC7D/C,OAAA,CAACN,MAAM;YAACoD,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BnD,OAAA;YAAA+C,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAK8C,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClET,eAAe,CAACiB,GAAG,CAAEf,MAAM,iBAC1BxC,OAAA;QAAqB8C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrE/C,OAAA;UAAK8C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD/C,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAI8C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAChDP,MAAM,CAAC7B;YAAS;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACLnD,OAAA;cAAG8C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAEP,MAAM,CAAC9B,WAAW,EAAC,UAAG,EAAC8B,MAAM,CAAClC,WAAW;YAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACzCjB,iBAAiB,CAACU,MAAM,CAAC3B,cAAc,CAAC,iBACvCb,OAAA,CAACJ,aAAa;cAACkD,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACrD,eACDnD,OAAA;cAAM8C,SAAS,EAAEnB,cAAc,CAACa,MAAM,CAACjC,MAAM,CAAE;cAAAwC,QAAA,EAC5CP,MAAM,CAACjC;YAAM;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAK8C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/C,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/C,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDnD,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAEP,MAAM,CAACvB,gBAAgB,EAAC,QAAM;YAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/C,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9CnD,OAAA;cAAK8C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C/C,OAAA,CAACL,IAAI;gBAACmD,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDnD,OAAA;gBAAM8C,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEP,MAAM,CAACxB;cAAM;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/C,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDnD,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEP,MAAM,CAACtB,YAAY,IAAI;YAAc;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/C,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CnD,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEP,MAAM,CAACzB;YAAU;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/C,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDnD,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEP,MAAM,CAACrB,WAAW,CAACqC,cAAc,CAAC;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/C,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDnD,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAEP,MAAM,CAACpB,kBAAkB,EAAC,GAAC;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnD,OAAA;UAAK8C,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjD/C,OAAA;YAAK8C,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACxD/C,OAAA;cAAM8C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CnD,OAAA;cAAK8C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GACzCjB,iBAAiB,CAACU,MAAM,CAAC3B,cAAc,CAAC,gBACvCb,OAAA;gBAAM8C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAE7DnD,OAAA;gBAAM8C,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACrD,eACDnD,OAAA,CAACF,QAAQ;gBAACgD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnD,OAAA;YAAG8C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GAAC,WAC/B,EAAC,IAAId,IAAI,CAACO,MAAM,CAAC3B,cAAc,CAAC,CAAC4C,kBAAkB,CAAC,CAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNnD,OAAA;UAAK8C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD/C,OAAA;YAAK8C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/C,OAAA;cAAQ8C,SAAS,EAAC,yCAAyC;cAAAC,QAAA,eACzD/C,OAAA,CAACT,KAAK;gBAACuD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACTnD,OAAA;cAAQ8C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eACnD/C,OAAA,CAACH,KAAK;gBAACiD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENnD,OAAA;YAAQ8C,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GAhFEX,MAAM,CAAC/B,EAAE;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiFd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLb,eAAe,CAACoB,MAAM,KAAK,CAAC,iBAC3B1D,OAAA;MAAK8C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/C,OAAA,CAACT,KAAK;QAACuD,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1DnD,OAAA;QAAK8C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1DnD,OAAA;QAAQ8C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAEpC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjD,EAAA,CAtPID,gBAAgB;AAAA0D,EAAA,GAAhB1D,gBAAgB;AAwPtB,eAAeA,gBAAgB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
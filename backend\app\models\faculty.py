from datetime import datetime, date
from app import db, ma

class Faculty(db.Model):
    """Faculty model."""
    __tablename__ = 'faculty'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON><PERSON>('users.id'), nullable=False, unique=True)
    employee_id = db.Column(db.String(20), nullable=False, unique=True, index=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    date_of_birth = db.Column(db.Date)
    gender = db.Column(db.Enum('Male', 'Female', 'Other', name='gender_types'))
    phone = db.Column(db.String(15))
    address = db.Column(db.Text)
    department_id = db.Column(db.Integer, db.<PERSON>('departments.id'), nullable=False)
    designation = db.Column(db.String(50))  # Professor, Associate Professor, Assistant Professor, etc.
    qualification = db.Column(db.String(200))
    specialization = db.Column(db.String(200))
    experience_years = db.Column(db.Integer)
    joining_date = db.Column(db.Date, default=date.today)
    salary = db.Column(db.Decimal(10, 2))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    courses = db.relationship('Course', backref='faculty', lazy='dynamic')
    attendances = db.relationship('Attendance', backref='faculty', lazy='dynamic')
    grades = db.relationship('Grade', backref='faculty', lazy='dynamic')
    
    @property
    def full_name(self):
        """Get full name."""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self):
        """Calculate age from date of birth."""
        if self.date_of_birth:
            today = date.today()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None
    
    def get_assigned_courses(self):
        """Get courses assigned to this faculty."""
        return self.courses.filter_by(is_active=True).all()
    
    def to_dict(self):
        """Convert faculty to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'employee_id': self.employee_id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'date_of_birth': self.date_of_birth.isoformat() if self.date_of_birth else None,
            'age': self.age,
            'gender': self.gender,
            'phone': self.phone,
            'address': self.address,
            'department_id': self.department_id,
            'department_name': self.department.name if self.department else None,
            'designation': self.designation,
            'qualification': self.qualification,
            'specialization': self.specialization,
            'experience_years': self.experience_years,
            'joining_date': self.joining_date.isoformat() if self.joining_date else None,
            'salary': float(self.salary) if self.salary else None,
            'is_active': self.is_active,
            'email': self.user.email if self.user else None,
            'course_count': self.courses.filter_by(is_active=True).count(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Faculty {self.employee_id}: {self.full_name}>'

class FacultySchema(ma.SQLAlchemyAutoSchema):
    """Faculty serialization schema."""
    class Meta:
        model = Faculty
        load_instance = True
        include_fk = True

faculty_schema = FacultySchema()
faculties_schema = FacultySchema(many=True)

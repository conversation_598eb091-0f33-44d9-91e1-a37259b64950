# @babel/plugin-transform-regexp-modifiers

> Compile inline regular expression modifiers

See our website [@babel/plugin-transform-regexp-modifiers](https://babeljs.io/docs/babel-plugin-transform-regexp-modifiers) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-regexp-modifiers
```

or using yarn:

```sh
yarn add @babel/plugin-transform-regexp-modifiers --dev
```

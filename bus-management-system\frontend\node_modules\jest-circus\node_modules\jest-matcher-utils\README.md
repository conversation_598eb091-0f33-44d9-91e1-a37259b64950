# jest-matcher-utils

This package's exports are most notably used by `expect`'s [`this.utils`](https://jestjs.io/docs/expect#thisutils).

## Installation

To add this package as a dependency of a project, run either of the following commands:

- `npm install jest-matcher-utils`
- `yarn add jest-matcher-utils`

## Exports ([src/index.ts](https://github.com/facebook/jest/blob/HEAD/packages/jest-matcher-utils/src/index.ts))

### Functions

`stringify` `highlightTrailingWhitespace` `printReceived` `printExpected` `printWithType` `ensureNoExpected` `ensureActualIsNumber` `ensureExpectedIsNumber` `ensureNumbers` `ensureExpectedIsNonNegativeInteger` `printDiffOrStringify` `diff` `pluralize` `getLabelPrinter` `matcherErrorMessage` `matcherHint`

### Types

`MatcherHintOptions` `DiffOptions`

### Constants

`EXPECTED_COLOR` `RECEIVED_COLOR` `INVERTED_COLOR` `BOLD_WEIGHT` `DIM_COLOR` `SUGGEST_TO_CONTAIN_EQUAL`

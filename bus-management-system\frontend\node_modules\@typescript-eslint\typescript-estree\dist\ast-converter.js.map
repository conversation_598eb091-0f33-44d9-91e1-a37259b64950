{"version": 3, "file": "ast-converter.js", "sourceRoot": "", "sources": ["../src/ast-converter.ts"], "names": [], "mappings": ";;;AAGA,uCAAoD;AACpD,yDAAqD;AACrD,6CAA6C;AAE7C,uDAAmD;AAGnD,SAAgB,YAAY,CAC1B,GAAe,EACf,aAA4B,EAC5B,sBAA+B;IAE/B;;;OAGG;IACH,MAAM,EAAE,gBAAgB,EAAE,GAAG,GAAG,CAAC;IACjC,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,MAAM,IAAA,sBAAY,EAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;KACzC;IAED;;OAEG;IACH,MAAM,QAAQ,GAAG,IAAI,mBAAS,CAAC,GAAG,EAAE;QAClC,qBAAqB,EAAE,aAAa,CAAC,qBAAqB,IAAI,KAAK;QACnE,sBAAsB;KACvB,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;IAEzC;;OAEG;IACH,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE;QAC9C,IAAA,gCAAc,EAAC,MAAM,EAAE;YACrB,KAAK,EAAE,IAAI,CAAC,EAAE;gBACZ,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;oBACxB,4HAA4H;oBAC5H,mBAAmB;oBACnB,OAAO,IAAI,CAAC,KAAK,CAAC;iBACnB;gBACD,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE;oBACtB,4HAA4H;oBAC5H,mBAAmB;oBACnB,OAAO,IAAI,CAAC,GAAG,CAAC;iBACjB;YACH,CAAC;SACF,CAAC,CAAC;KACJ;IAED;;OAEG;IACH,IAAI,aAAa,CAAC,MAAM,EAAE;QACxB,MAAM,CAAC,MAAM,GAAG,IAAA,0BAAa,EAAC,GAAG,CAAC,CAAC;KACpC;IAED;;OAEG;IACH,IAAI,aAAa,CAAC,OAAO,EAAE;QACzB,MAAM,CAAC,QAAQ,GAAG,IAAA,kCAAe,EAAC,GAAG,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;KAC5D;IAED,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;IAEtC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;AAC7B,CAAC;AA7DD,oCA6DC"}
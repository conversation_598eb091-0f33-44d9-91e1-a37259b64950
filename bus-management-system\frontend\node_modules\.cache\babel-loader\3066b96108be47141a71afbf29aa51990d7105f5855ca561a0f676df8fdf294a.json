{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileArchive = createLucideIcon(\"FileArchive\", [[\"path\", {\n  d: \"M4 22V4c0-.5.2-1 .6-1.4C5 2.2 5.5 2 6 2h8.5L20 7.5V20c0 .5-.2 1-.6 1.4-.4.4-.9.6-1.4.6h-2\",\n  key: \"1u864v\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"20\",\n  r: \"2\",\n  key: \"1xzdoj\"\n}], [\"path\", {\n  d: \"M10 7V6\",\n  key: \"dljcrl\"\n}], [\"path\", {\n  d: \"M10 12v-1\",\n  key: \"v7bkov\"\n}], [\"path\", {\n  d: \"M10 18v-2\",\n  key: \"1cjy8d\"\n}]]);\nexport { FileArchive as default };", "map": {"version": 3, "names": ["FileArchive", "createLucideIcon", "d", "key", "points", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\file-archive.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileArchive\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAyMlY0YzAtLjUuMi0xIC42LTEuNEM1IDIuMiA1LjUgMiA2IDJoOC41TDIwIDcuNVYyMGMwIC41LS4yIDEtLjYgMS40LS40LjQtLjkuNi0xLjQuNmgtMiIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNCAyIDE0IDggMjAgOCIgLz4KICA8Y2lyY2xlIGN4PSIxMCIgY3k9IjIwIiByPSIyIiAvPgogIDxwYXRoIGQ9Ik0xMCA3VjYiIC8+CiAgPHBhdGggZD0iTTEwIDEydi0xIiAvPgogIDxwYXRoIGQ9Ik0xMCAxOHYtMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/file-archive\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileArchive = createLucideIcon('FileArchive', [\n  [\n    'path',\n    {\n      d: 'M4 22V4c0-.5.2-1 .6-1.4C5 2.2 5.5 2 6 2h8.5L20 7.5V20c0 .5-.2 1-.6 1.4-.4.4-.9.6-1.4.6h-2',\n      key: '1u864v',\n    },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['circle', { cx: '10', cy: '20', r: '2', key: '1xzdoj' }],\n  ['path', { d: 'M10 7V6', key: 'dljcrl' }],\n  ['path', { d: 'M10 12v-1', key: 'v7bkov' }],\n  ['path', { d: 'M10 18v-2', key: '1cjy8d' }],\n]);\n\nexport default FileArchive;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEE,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
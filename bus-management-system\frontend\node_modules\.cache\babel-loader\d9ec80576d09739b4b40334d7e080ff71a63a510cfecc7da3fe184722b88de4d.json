{"ast": null, "code": "'use strict';\n\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar $Object = Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return $Object(requireObjectCoercible(argument));\n};", "map": {"version": 3, "names": ["requireObjectCoercible", "require", "$Object", "Object", "module", "exports", "argument"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/core-js-pure/internals/to-object.js"], "sourcesContent": ["'use strict';\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar $Object = Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return $Object(requireObjectCoercible(argument));\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,uCAAuC,CAAC;AAE7E,IAAIC,OAAO,GAAGC,MAAM;;AAEpB;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,OAAOJ,OAAO,CAACF,sBAAsB,CAACM,QAAQ,CAAC,CAAC;AAClD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
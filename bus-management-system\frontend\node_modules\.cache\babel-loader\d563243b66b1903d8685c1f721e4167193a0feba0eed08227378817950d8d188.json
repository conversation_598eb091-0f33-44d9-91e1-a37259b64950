{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\pages\\\\StudentManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useQuery } from 'react-query';\nimport { UserCheck, Plus, QrCode, Search, Filter, Award, DollarSign, Phone, MapPin } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StudentManagement = () => {\n  _s();\n  const [filters, setFilters] = useState({\n    search: '',\n    class_name: '',\n    scholarship_type: '',\n    status: ''\n  });\n\n  // Mock student data - in real app this would come from API\n  const students = [{\n    id: 1,\n    student_id: 'STU001',\n    full_name: '<PERSON>',\n    class_name: 'Grade 10',\n    section: 'A',\n    roll_number: '101',\n    parent_name: '<PERSON>',\n    parent_phone: '+1234567896',\n    route_id: 1,\n    pickup_stop: 'City Center',\n    drop_stop: 'School Gate',\n    base_monthly_fee: 2000,\n    scholarship_type: 'Merit',\n    scholarship_percentage: 25,\n    final_monthly_fee: 1500,\n    status: 'Active',\n    attendance_percentage: 95,\n    family_income: 45000,\n    academic_score: 92\n  }, {\n    id: 2,\n    student_id: 'STU002',\n    full_name: 'Bob Davis',\n    class_name: 'Grade 11',\n    section: 'B',\n    roll_number: '205',\n    parent_name: 'Linda Davis',\n    parent_phone: '+1234567897',\n    route_id: 2,\n    pickup_stop: 'Sunrise Apartments',\n    drop_stop: 'University Gate',\n    base_monthly_fee: 2200,\n    scholarship_type: 'Need-based',\n    scholarship_percentage: 40,\n    final_monthly_fee: 1320,\n    status: 'Active',\n    attendance_percentage: 88,\n    family_income: 25000,\n    academic_score: 78\n  }, {\n    id: 3,\n    student_id: 'STU003',\n    full_name: 'Carol Wilson',\n    class_name: 'Grade 9',\n    section: 'C',\n    roll_number: '315',\n    parent_name: 'James Wilson',\n    parent_phone: '+1234567898',\n    route_id: 3,\n    pickup_stop: 'Industrial Gate',\n    drop_stop: 'Tech Park',\n    base_monthly_fee: 1800,\n    scholarship_type: 'Sports',\n    scholarship_percentage: 50,\n    final_monthly_fee: 900,\n    status: 'Active',\n    attendance_percentage: 97,\n    family_income: 35000,\n    academic_score: 85\n  }, {\n    id: 4,\n    student_id: 'STU004',\n    full_name: 'David Kumar',\n    class_name: 'Grade 12',\n    section: 'A',\n    roll_number: '120',\n    parent_name: 'Raj Kumar',\n    parent_phone: '+1234567899',\n    route_id: 4,\n    pickup_stop: 'Metro Station',\n    drop_stop: 'Airport',\n    base_monthly_fee: 2500,\n    scholarship_type: null,\n    scholarship_percentage: 0,\n    final_monthly_fee: 2500,\n    status: 'Active',\n    attendance_percentage: 91,\n    family_income: 75000,\n    academic_score: 88\n  }, {\n    id: 5,\n    student_id: 'STU005',\n    full_name: 'Emma Thompson',\n    class_name: 'Grade 10',\n    section: 'B',\n    roll_number: '210',\n    parent_name: 'Sarah Thompson',\n    parent_phone: '+1234567800',\n    route_id: 1,\n    pickup_stop: 'City Center',\n    drop_stop: 'School Gate',\n    base_monthly_fee: 2000,\n    scholarship_type: 'Sibling',\n    scholarship_percentage: 15,\n    final_monthly_fee: 1700,\n    status: 'Active',\n    attendance_percentage: 93,\n    family_income: 55000,\n    academic_score: 89\n  }];\n  const handleFilterChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const getScholarshipBadge = scholarshipType => {\n    if (!scholarshipType) return null;\n    const scholarshipClasses = {\n      Merit: 'badge-success',\n      'Need-based': 'badge-warning',\n      Sports: 'badge-primary',\n      Sibling: 'badge-secondary',\n      'Single Parent': 'badge-danger'\n    };\n    return `badge ${scholarshipClasses[scholarshipType] || 'badge-secondary'}`;\n  };\n  const getStatusBadge = status => {\n    const statusClasses = {\n      Active: 'badge-success',\n      Inactive: 'badge-secondary',\n      Suspended: 'badge-danger'\n    };\n    return `badge ${statusClasses[status] || 'badge-secondary'}`;\n  };\n  const filteredStudents = students.filter(student => {\n    const matchesSearch = !filters.search || student.full_name.toLowerCase().includes(filters.search.toLowerCase()) || student.student_id.toLowerCase().includes(filters.search.toLowerCase());\n    const matchesClass = !filters.class_name || student.class_name === filters.class_name;\n    const matchesScholarship = !filters.scholarship_type || student.scholarship_type === filters.scholarship_type;\n    const matchesStatus = !filters.status || student.status === filters.status;\n    return matchesSearch && matchesClass && matchesScholarship && matchesStatus;\n  });\n\n  // Calculate summary statistics\n  const totalStudents = students.length;\n  const scholarshipStudents = students.filter(s => s.scholarship_type);\n  const totalBaseFees = students.reduce((sum, s) => sum + s.base_monthly_fee, 0);\n  const totalFinalFees = students.reduce((sum, s) => sum + s.final_monthly_fee, 0);\n  const totalSavings = totalBaseFees - totalFinalFees;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Student Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage students with scholarship-based fee reduction\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Add Student\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-lg bg-blue-100\",\n            children: /*#__PURE__*/_jsxDEV(UserCheck, {\n              className: \"h-6 w-6 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Students\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: totalStudents\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-lg bg-green-100\",\n            children: /*#__PURE__*/_jsxDEV(Award, {\n              className: \"h-6 w-6 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Scholarship Recipients\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: scholarshipStudents.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: [Math.round(scholarshipStudents.length / totalStudents * 100), \"% of students\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-lg bg-purple-100\",\n            children: /*#__PURE__*/_jsxDEV(DollarSign, {\n              className: \"h-6 w-6 text-purple-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Monthly Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [\"\\u20B9\", totalFinalFees.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"After scholarships\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-lg bg-orange-100\",\n            children: /*#__PURE__*/_jsxDEV(Award, {\n              className: \"h-6 w-6 text-orange-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Scholarship Savings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: [\"\\u20B9\", totalSavings.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Monthly assistance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-5 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"search\",\n            placeholder: \"Search students...\",\n            value: filters.search,\n            onChange: handleFilterChange,\n            className: \"input pl-10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"class_name\",\n          value: filters.class_name,\n          onChange: handleFilterChange,\n          className: \"input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Classes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Grade 9\",\n            children: \"Grade 9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Grade 10\",\n            children: \"Grade 10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Grade 11\",\n            children: \"Grade 11\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Grade 12\",\n            children: \"Grade 12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"scholarship_type\",\n          value: filters.scholarship_type,\n          onChange: handleFilterChange,\n          className: \"input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Scholarships\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Merit\",\n            children: \"Merit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Need-based\",\n            children: \"Need-based\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Sports\",\n            children: \"Sports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Sibling\",\n            children: \"Sibling\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Single Parent\",\n            children: \"Single Parent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"status\",\n          value: filters.status,\n          onChange: handleFilterChange,\n          className: \"input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Active\",\n            children: \"Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Inactive\",\n            children: \"Inactive\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Suspended\",\n            children: \"Suspended\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"More Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n      children: filteredStudents.map(student => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card hover:shadow-lg transition-shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-start mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: student.full_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: [student.student_id, \" \\u2022 \", student.class_name, \" \", student.section]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-end space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: getStatusBadge(student.status),\n              children: student.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), student.scholarship_type && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: getScholarshipBadge(student.scholarship_type),\n              children: student.scholarship_type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2 text-sm mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Roll Number:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: student.roll_number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Parent:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: student.parent_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Academic Score:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [student.academic_score, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Attendance:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [student.attendance_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 rounded-lg p-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-900 mb-2\",\n            children: \"Fee Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Base Fee:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [\"\\u20B9\", student.base_monthly_fee]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), student.scholarship_type && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Scholarship:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600 font-medium\",\n                  children: [\"-\", student.scholarship_percentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Savings:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600 font-medium\",\n                  children: [\"\\u20B9\", student.base_monthly_fee - student.final_monthly_fee]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between border-t pt-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-900 font-medium\",\n                children: \"Final Fee:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-900 font-bold\",\n                children: [\"\\u20B9\", student.final_monthly_fee]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 text-sm text-gray-600 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(MapPin, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [student.pickup_stop, \" \\u2192 \", student.drop_stop]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"text-primary-600 hover:text-primary-900\",\n              children: /*#__PURE__*/_jsxDEV(QrCode, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"text-blue-600 hover:text-blue-900\",\n              children: /*#__PURE__*/_jsxDEV(Phone, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"text-green-600 hover:text-green-900\",\n              children: /*#__PURE__*/_jsxDEV(Award, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-outline btn-sm\",\n            children: \"View Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this)]\n      }, student.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Scholarship Breakdown\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-5 gap-4\",\n        children: ['Merit', 'Need-based', 'Sports', 'Sibling', 'Single Parent'].map(type => {\n          const typeStudents = students.filter(s => s.scholarship_type === type);\n          const typeSavings = typeStudents.reduce((sum, s) => sum + (s.base_monthly_fee - s.final_monthly_fee), 0);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-4 bg-gray-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-gray-900\",\n              children: typeStudents.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-green-600 font-medium\",\n              children: [\"\\u20B9\", typeSavings.toLocaleString(), \" saved\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)]\n          }, type, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), filteredStudents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(UserCheck, {\n        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-500 mb-4\",\n        children: \"No students found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        children: \"Add Your First Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentManagement, \"VWnArkRYQOmaqKq91fwFOmfZoLU=\");\n_c = StudentManagement;\nexport default StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");", "map": {"version": 3, "names": ["React", "useState", "useQuery", "UserCheck", "Plus", "QrCode", "Search", "Filter", "Award", "DollarSign", "Phone", "MapPin", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StudentManagement", "_s", "filters", "setFilters", "search", "class_name", "scholarship_type", "status", "students", "id", "student_id", "full_name", "section", "roll_number", "parent_name", "parent_phone", "route_id", "pickup_stop", "drop_stop", "base_monthly_fee", "scholarship_percentage", "final_monthly_fee", "attendance_percentage", "family_income", "academic_score", "handleFilterChange", "e", "name", "value", "target", "prev", "getScholarshipBadge", "scholarshipType", "scholarshipClasses", "Merit", "Sports", "Sibling", "getStatusBadge", "statusClasses", "Active", "Inactive", "Suspended", "filteredStudents", "filter", "student", "matchesSearch", "toLowerCase", "includes", "matchesClass", "matchesScholarship", "matchesStatus", "totalStudents", "length", "scholarshipStudents", "s", "totalBaseFees", "reduce", "sum", "totalFinalFees", "totalSavings", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Math", "round", "toLocaleString", "type", "placeholder", "onChange", "map", "typeStudents", "typeSavings", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/pages/StudentManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useQuery } from 'react-query';\nimport { UserCheck, Plus, QrCode, Search, Filter, Award, DollarSign, Phone, MapPin } from 'lucide-react';\n\nconst StudentManagement = () => {\n  const [filters, setFilters] = useState({\n    search: '',\n    class_name: '',\n    scholarship_type: '',\n    status: '',\n  });\n\n  // Mock student data - in real app this would come from API\n  const students = [\n    {\n      id: 1, student_id: 'STU001', full_name: '<PERSON>',\n      class_name: 'Grade 10', section: 'A', roll_number: '101',\n      parent_name: '<PERSON>', parent_phone: '+1234567896',\n      route_id: 1, pickup_stop: 'City Center', drop_stop: 'School Gate',\n      base_monthly_fee: 2000, scholarship_type: 'Merit', scholarship_percentage: 25,\n      final_monthly_fee: 1500, status: 'Active', attendance_percentage: 95,\n      family_income: 45000, academic_score: 92\n    },\n    {\n      id: 2, student_id: 'STU002', full_name: '<PERSON>',\n      class_name: 'Grade 11', section: 'B', roll_number: '205',\n      parent_name: '<PERSON>', parent_phone: '+1234567897',\n      route_id: 2, pickup_stop: 'Sunrise Apartments', drop_stop: 'University Gate',\n      base_monthly_fee: 2200, scholarship_type: 'Need-based', scholarship_percentage: 40,\n      final_monthly_fee: 1320, status: 'Active', attendance_percentage: 88,\n      family_income: 25000, academic_score: 78\n    },\n    {\n      id: 3, student_id: 'STU003', full_name: 'Carol Wilson',\n      class_name: 'Grade 9', section: 'C', roll_number: '315',\n      parent_name: 'James Wilson', parent_phone: '+1234567898',\n      route_id: 3, pickup_stop: 'Industrial Gate', drop_stop: 'Tech Park',\n      base_monthly_fee: 1800, scholarship_type: 'Sports', scholarship_percentage: 50,\n      final_monthly_fee: 900, status: 'Active', attendance_percentage: 97,\n      family_income: 35000, academic_score: 85\n    },\n    {\n      id: 4, student_id: 'STU004', full_name: 'David Kumar',\n      class_name: 'Grade 12', section: 'A', roll_number: '120',\n      parent_name: 'Raj Kumar', parent_phone: '+1234567899',\n      route_id: 4, pickup_stop: 'Metro Station', drop_stop: 'Airport',\n      base_monthly_fee: 2500, scholarship_type: null, scholarship_percentage: 0,\n      final_monthly_fee: 2500, status: 'Active', attendance_percentage: 91,\n      family_income: 75000, academic_score: 88\n    },\n    {\n      id: 5, student_id: 'STU005', full_name: 'Emma Thompson',\n      class_name: 'Grade 10', section: 'B', roll_number: '210',\n      parent_name: 'Sarah Thompson', parent_phone: '+1234567800',\n      route_id: 1, pickup_stop: 'City Center', drop_stop: 'School Gate',\n      base_monthly_fee: 2000, scholarship_type: 'Sibling', scholarship_percentage: 15,\n      final_monthly_fee: 1700, status: 'Active', attendance_percentage: 93,\n      family_income: 55000, academic_score: 89\n    }\n  ];\n\n  const handleFilterChange = (e) => {\n    const { name, value } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const getScholarshipBadge = (scholarshipType) => {\n    if (!scholarshipType) return null;\n\n    const scholarshipClasses = {\n      Merit: 'badge-success',\n      'Need-based': 'badge-warning',\n      Sports: 'badge-primary',\n      Sibling: 'badge-secondary',\n      'Single Parent': 'badge-danger',\n    };\n    return `badge ${scholarshipClasses[scholarshipType] || 'badge-secondary'}`;\n  };\n\n  const getStatusBadge = (status) => {\n    const statusClasses = {\n      Active: 'badge-success',\n      Inactive: 'badge-secondary',\n      Suspended: 'badge-danger',\n    };\n    return `badge ${statusClasses[status] || 'badge-secondary'}`;\n  };\n\n  const filteredStudents = students.filter(student => {\n    const matchesSearch = !filters.search ||\n      student.full_name.toLowerCase().includes(filters.search.toLowerCase()) ||\n      student.student_id.toLowerCase().includes(filters.search.toLowerCase());\n    const matchesClass = !filters.class_name || student.class_name === filters.class_name;\n    const matchesScholarship = !filters.scholarship_type || student.scholarship_type === filters.scholarship_type;\n    const matchesStatus = !filters.status || student.status === filters.status;\n\n    return matchesSearch && matchesClass && matchesScholarship && matchesStatus;\n  });\n\n  // Calculate summary statistics\n  const totalStudents = students.length;\n  const scholarshipStudents = students.filter(s => s.scholarship_type);\n  const totalBaseFees = students.reduce((sum, s) => sum + s.base_monthly_fee, 0);\n  const totalFinalFees = students.reduce((sum, s) => sum + s.final_monthly_fee, 0);\n  const totalSavings = totalBaseFees - totalFinalFees;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Student Management</h1>\n          <p className=\"text-gray-600\">Manage students with scholarship-based fee reduction</p>\n        </div>\n        <button className=\"btn btn-primary flex items-center space-x-2\">\n          <Plus className=\"h-5 w-5\" />\n          <span>Add Student</span>\n        </button>\n      </div>\n\n      {/* Scholarship Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"card\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-lg bg-blue-100\">\n              <UserCheck className=\"h-6 w-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Students</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalStudents}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-lg bg-green-100\">\n              <Award className=\"h-6 w-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Scholarship Recipients</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{scholarshipStudents.length}</p>\n              <p className=\"text-xs text-gray-500\">{Math.round((scholarshipStudents.length / totalStudents) * 100)}% of students</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-lg bg-purple-100\">\n              <DollarSign className=\"h-6 w-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Monthly Revenue</p>\n              <p className=\"text-2xl font-bold text-gray-900\">₹{totalFinalFees.toLocaleString()}</p>\n              <p className=\"text-xs text-gray-500\">After scholarships</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-lg bg-orange-100\">\n              <Award className=\"h-6 w-6 text-orange-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Scholarship Savings</p>\n              <p className=\"text-2xl font-bold text-gray-900\">₹{totalSavings.toLocaleString()}</p>\n              <p className=\"text-xs text-gray-500\">Monthly assistance</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"card\">\n        <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n            <input\n              type=\"text\"\n              name=\"search\"\n              placeholder=\"Search students...\"\n              value={filters.search}\n              onChange={handleFilterChange}\n              className=\"input pl-10\"\n            />\n          </div>\n\n          <select\n            name=\"class_name\"\n            value={filters.class_name}\n            onChange={handleFilterChange}\n            className=\"input\"\n          >\n            <option value=\"\">All Classes</option>\n            <option value=\"Grade 9\">Grade 9</option>\n            <option value=\"Grade 10\">Grade 10</option>\n            <option value=\"Grade 11\">Grade 11</option>\n            <option value=\"Grade 12\">Grade 12</option>\n          </select>\n\n          <select\n            name=\"scholarship_type\"\n            value={filters.scholarship_type}\n            onChange={handleFilterChange}\n            className=\"input\"\n          >\n            <option value=\"\">All Scholarships</option>\n            <option value=\"Merit\">Merit</option>\n            <option value=\"Need-based\">Need-based</option>\n            <option value=\"Sports\">Sports</option>\n            <option value=\"Sibling\">Sibling</option>\n            <option value=\"Single Parent\">Single Parent</option>\n          </select>\n\n          <select\n            name=\"status\"\n            value={filters.status}\n            onChange={handleFilterChange}\n            className=\"input\"\n          >\n            <option value=\"\">All Status</option>\n            <option value=\"Active\">Active</option>\n            <option value=\"Inactive\">Inactive</option>\n            <option value=\"Suspended\">Suspended</option>\n          </select>\n\n          <button className=\"btn btn-outline flex items-center space-x-2\">\n            <Filter className=\"h-5 w-5\" />\n            <span>More Filters</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Students Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n        {filteredStudents.map((student) => (\n          <div key={student.id} className=\"card hover:shadow-lg transition-shadow\">\n            <div className=\"flex justify-between items-start mb-4\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">\n                  {student.full_name}\n                </h3>\n                <p className=\"text-sm text-gray-600\">\n                  {student.student_id} • {student.class_name} {student.section}\n                </p>\n              </div>\n              <div className=\"flex flex-col items-end space-y-1\">\n                <span className={getStatusBadge(student.status)}>\n                  {student.status}\n                </span>\n                {student.scholarship_type && (\n                  <span className={getScholarshipBadge(student.scholarship_type)}>\n                    {student.scholarship_type}\n                  </span>\n                )}\n              </div>\n            </div>\n\n            {/* Student Details */}\n            <div className=\"space-y-2 text-sm mb-4\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Roll Number:</span>\n                <span className=\"font-medium\">{student.roll_number}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Parent:</span>\n                <span className=\"font-medium\">{student.parent_name}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Academic Score:</span>\n                <span className=\"font-medium\">{student.academic_score}%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Attendance:</span>\n                <span className=\"font-medium\">{student.attendance_percentage}%</span>\n              </div>\n            </div>\n\n            {/* Fee Information */}\n            <div className=\"bg-gray-50 rounded-lg p-3 mb-4\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Fee Details</h4>\n              <div className=\"space-y-1 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Base Fee:</span>\n                  <span className=\"font-medium\">₹{student.base_monthly_fee}</span>\n                </div>\n                {student.scholarship_type && (\n                  <>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Scholarship:</span>\n                      <span className=\"text-green-600 font-medium\">-{student.scholarship_percentage}%</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Savings:</span>\n                      <span className=\"text-green-600 font-medium\">\n                        ₹{student.base_monthly_fee - student.final_monthly_fee}\n                      </span>\n                    </div>\n                  </>\n                )}\n                <div className=\"flex justify-between border-t pt-1\">\n                  <span className=\"text-gray-900 font-medium\">Final Fee:</span>\n                  <span className=\"text-gray-900 font-bold\">₹{student.final_monthly_fee}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Route Information */}\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600 mb-4\">\n              <MapPin className=\"h-4 w-4\" />\n              <span>{student.pickup_stop} → {student.drop_stop}</span>\n            </div>\n\n            {/* Actions */}\n            <div className=\"flex justify-between items-center\">\n              <div className=\"flex space-x-2\">\n                <button className=\"text-primary-600 hover:text-primary-900\">\n                  <QrCode className=\"h-4 w-4\" />\n                </button>\n                <button className=\"text-blue-600 hover:text-blue-900\">\n                  <Phone className=\"h-4 w-4\" />\n                </button>\n                <button className=\"text-green-600 hover:text-green-900\">\n                  <Award className=\"h-4 w-4\" />\n                </button>\n              </div>\n\n              <button className=\"btn btn-outline btn-sm\">\n                View Profile\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Scholarship Breakdown */}\n      <div className=\"card\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Scholarship Breakdown</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n          {['Merit', 'Need-based', 'Sports', 'Sibling', 'Single Parent'].map(type => {\n            const typeStudents = students.filter(s => s.scholarship_type === type);\n            const typeSavings = typeStudents.reduce((sum, s) => sum + (s.base_monthly_fee - s.final_monthly_fee), 0);\n\n            return (\n              <div key={type} className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                <div className=\"text-lg font-bold text-gray-900\">{typeStudents.length}</div>\n                <div className=\"text-sm text-gray-600\">{type}</div>\n                <div className=\"text-xs text-green-600 font-medium\">₹{typeSavings.toLocaleString()} saved</div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Empty State */}\n      {filteredStudents.length === 0 && (\n        <div className=\"text-center py-12\">\n          <UserCheck className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <div className=\"text-gray-500 mb-4\">No students found</div>\n          <button className=\"btn btn-primary\">\n            Add Your First Student\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StudentManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzG,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC;IACrCmB,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,EAAE;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,QAAQ,GAAG,CACf;IACEC,EAAE,EAAE,CAAC;IAAEC,UAAU,EAAE,QAAQ;IAAEC,SAAS,EAAE,aAAa;IACrDN,UAAU,EAAE,UAAU;IAAEO,OAAO,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IACxDC,WAAW,EAAE,cAAc;IAAEC,YAAY,EAAE,aAAa;IACxDC,QAAQ,EAAE,CAAC;IAAEC,WAAW,EAAE,aAAa;IAAEC,SAAS,EAAE,aAAa;IACjEC,gBAAgB,EAAE,IAAI;IAAEb,gBAAgB,EAAE,OAAO;IAAEc,sBAAsB,EAAE,EAAE;IAC7EC,iBAAiB,EAAE,IAAI;IAAEd,MAAM,EAAE,QAAQ;IAAEe,qBAAqB,EAAE,EAAE;IACpEC,aAAa,EAAE,KAAK;IAAEC,cAAc,EAAE;EACxC,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IAAEC,UAAU,EAAE,QAAQ;IAAEC,SAAS,EAAE,WAAW;IACnDN,UAAU,EAAE,UAAU;IAAEO,OAAO,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IACxDC,WAAW,EAAE,aAAa;IAAEC,YAAY,EAAE,aAAa;IACvDC,QAAQ,EAAE,CAAC;IAAEC,WAAW,EAAE,oBAAoB;IAAEC,SAAS,EAAE,iBAAiB;IAC5EC,gBAAgB,EAAE,IAAI;IAAEb,gBAAgB,EAAE,YAAY;IAAEc,sBAAsB,EAAE,EAAE;IAClFC,iBAAiB,EAAE,IAAI;IAAEd,MAAM,EAAE,QAAQ;IAAEe,qBAAqB,EAAE,EAAE;IACpEC,aAAa,EAAE,KAAK;IAAEC,cAAc,EAAE;EACxC,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IAAEC,UAAU,EAAE,QAAQ;IAAEC,SAAS,EAAE,cAAc;IACtDN,UAAU,EAAE,SAAS;IAAEO,OAAO,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IACvDC,WAAW,EAAE,cAAc;IAAEC,YAAY,EAAE,aAAa;IACxDC,QAAQ,EAAE,CAAC;IAAEC,WAAW,EAAE,iBAAiB;IAAEC,SAAS,EAAE,WAAW;IACnEC,gBAAgB,EAAE,IAAI;IAAEb,gBAAgB,EAAE,QAAQ;IAAEc,sBAAsB,EAAE,EAAE;IAC9EC,iBAAiB,EAAE,GAAG;IAAEd,MAAM,EAAE,QAAQ;IAAEe,qBAAqB,EAAE,EAAE;IACnEC,aAAa,EAAE,KAAK;IAAEC,cAAc,EAAE;EACxC,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IAAEC,UAAU,EAAE,QAAQ;IAAEC,SAAS,EAAE,aAAa;IACrDN,UAAU,EAAE,UAAU;IAAEO,OAAO,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IACxDC,WAAW,EAAE,WAAW;IAAEC,YAAY,EAAE,aAAa;IACrDC,QAAQ,EAAE,CAAC;IAAEC,WAAW,EAAE,eAAe;IAAEC,SAAS,EAAE,SAAS;IAC/DC,gBAAgB,EAAE,IAAI;IAAEb,gBAAgB,EAAE,IAAI;IAAEc,sBAAsB,EAAE,CAAC;IACzEC,iBAAiB,EAAE,IAAI;IAAEd,MAAM,EAAE,QAAQ;IAAEe,qBAAqB,EAAE,EAAE;IACpEC,aAAa,EAAE,KAAK;IAAEC,cAAc,EAAE;EACxC,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IAAEC,UAAU,EAAE,QAAQ;IAAEC,SAAS,EAAE,eAAe;IACvDN,UAAU,EAAE,UAAU;IAAEO,OAAO,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IACxDC,WAAW,EAAE,gBAAgB;IAAEC,YAAY,EAAE,aAAa;IAC1DC,QAAQ,EAAE,CAAC;IAAEC,WAAW,EAAE,aAAa;IAAEC,SAAS,EAAE,aAAa;IACjEC,gBAAgB,EAAE,IAAI;IAAEb,gBAAgB,EAAE,SAAS;IAAEc,sBAAsB,EAAE,EAAE;IAC/EC,iBAAiB,EAAE,IAAI;IAAEd,MAAM,EAAE,QAAQ;IAAEe,qBAAqB,EAAE,EAAE;IACpEC,aAAa,EAAE,KAAK;IAAEC,cAAc,EAAE;EACxC,CAAC,CACF;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC1B,UAAU,CAAC2B,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,mBAAmB,GAAIC,eAAe,IAAK;IAC/C,IAAI,CAACA,eAAe,EAAE,OAAO,IAAI;IAEjC,MAAMC,kBAAkB,GAAG;MACzBC,KAAK,EAAE,eAAe;MACtB,YAAY,EAAE,eAAe;MAC7BC,MAAM,EAAE,eAAe;MACvBC,OAAO,EAAE,iBAAiB;MAC1B,eAAe,EAAE;IACnB,CAAC;IACD,OAAO,SAASH,kBAAkB,CAACD,eAAe,CAAC,IAAI,iBAAiB,EAAE;EAC5E,CAAC;EAED,MAAMK,cAAc,GAAI9B,MAAM,IAAK;IACjC,MAAM+B,aAAa,GAAG;MACpBC,MAAM,EAAE,eAAe;MACvBC,QAAQ,EAAE,iBAAiB;MAC3BC,SAAS,EAAE;IACb,CAAC;IACD,OAAO,SAASH,aAAa,CAAC/B,MAAM,CAAC,IAAI,iBAAiB,EAAE;EAC9D,CAAC;EAED,MAAMmC,gBAAgB,GAAGlC,QAAQ,CAACmC,MAAM,CAACC,OAAO,IAAI;IAClD,MAAMC,aAAa,GAAG,CAAC3C,OAAO,CAACE,MAAM,IACnCwC,OAAO,CAACjC,SAAS,CAACmC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7C,OAAO,CAACE,MAAM,CAAC0C,WAAW,CAAC,CAAC,CAAC,IACtEF,OAAO,CAAClC,UAAU,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7C,OAAO,CAACE,MAAM,CAAC0C,WAAW,CAAC,CAAC,CAAC;IACzE,MAAME,YAAY,GAAG,CAAC9C,OAAO,CAACG,UAAU,IAAIuC,OAAO,CAACvC,UAAU,KAAKH,OAAO,CAACG,UAAU;IACrF,MAAM4C,kBAAkB,GAAG,CAAC/C,OAAO,CAACI,gBAAgB,IAAIsC,OAAO,CAACtC,gBAAgB,KAAKJ,OAAO,CAACI,gBAAgB;IAC7G,MAAM4C,aAAa,GAAG,CAAChD,OAAO,CAACK,MAAM,IAAIqC,OAAO,CAACrC,MAAM,KAAKL,OAAO,CAACK,MAAM;IAE1E,OAAOsC,aAAa,IAAIG,YAAY,IAAIC,kBAAkB,IAAIC,aAAa;EAC7E,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAG3C,QAAQ,CAAC4C,MAAM;EACrC,MAAMC,mBAAmB,GAAG7C,QAAQ,CAACmC,MAAM,CAACW,CAAC,IAAIA,CAAC,CAAChD,gBAAgB,CAAC;EACpE,MAAMiD,aAAa,GAAG/C,QAAQ,CAACgD,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAGH,CAAC,CAACnC,gBAAgB,EAAE,CAAC,CAAC;EAC9E,MAAMuC,cAAc,GAAGlD,QAAQ,CAACgD,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAGH,CAAC,CAACjC,iBAAiB,EAAE,CAAC,CAAC;EAChF,MAAMsC,YAAY,GAAGJ,aAAa,GAAGG,cAAc;EAEnD,oBACE7D,OAAA;IAAK+D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBhE,OAAA;MAAK+D,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDhE,OAAA;QAAAgE,QAAA,gBACEhE,OAAA;UAAI+D,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEpE,OAAA;UAAG+D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC,eACNpE,OAAA;QAAQ+D,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC7DhE,OAAA,CAACT,IAAI;UAACwE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BpE,OAAA;UAAAgE,QAAA,EAAM;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNpE,OAAA;MAAK+D,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDhE,OAAA;QAAK+D,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhE,OAAA;UAAK+D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChChE,OAAA;YAAK+D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzChE,OAAA,CAACV,SAAS;cAACyE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNpE,OAAA;YAAK+D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhE,OAAA;cAAG+D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnEpE,OAAA;cAAG+D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEV;YAAa;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpE,OAAA;QAAK+D,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhE,OAAA;UAAK+D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChChE,OAAA;YAAK+D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1ChE,OAAA,CAACL,KAAK;cAACoE,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNpE,OAAA;YAAK+D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhE,OAAA;cAAG+D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3EpE,OAAA;cAAG+D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAER,mBAAmB,CAACD;YAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFpE,OAAA;cAAG+D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAEK,IAAI,CAACC,KAAK,CAAEd,mBAAmB,CAACD,MAAM,GAAGD,aAAa,GAAI,GAAG,CAAC,EAAC,eAAa;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpE,OAAA;QAAK+D,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhE,OAAA;UAAK+D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChChE,OAAA;YAAK+D,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3ChE,OAAA,CAACJ,UAAU;cAACmE,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNpE,OAAA;YAAK+D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhE,OAAA;cAAG+D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEpE,OAAA;cAAG+D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,QAAC,EAACH,cAAc,CAACU,cAAc,CAAC,CAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtFpE,OAAA;cAAG+D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpE,OAAA;QAAK+D,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhE,OAAA;UAAK+D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChChE,OAAA;YAAK+D,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3ChE,OAAA,CAACL,KAAK;cAACoE,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNpE,OAAA;YAAK+D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhE,OAAA;cAAG+D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxEpE,OAAA;cAAG+D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,QAAC,EAACF,YAAY,CAACS,cAAc,CAAC,CAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpFpE,OAAA;cAAG+D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpE,OAAA;MAAK+D,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBhE,OAAA;QAAK+D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDhE,OAAA;UAAK+D,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhE,OAAA,CAACP,MAAM;YAACsE,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/FpE,OAAA;YACEwE,IAAI,EAAC,MAAM;YACX1C,IAAI,EAAC,QAAQ;YACb2C,WAAW,EAAC,oBAAoB;YAChC1C,KAAK,EAAE1B,OAAO,CAACE,MAAO;YACtBmE,QAAQ,EAAE9C,kBAAmB;YAC7BmC,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpE,OAAA;UACE8B,IAAI,EAAC,YAAY;UACjBC,KAAK,EAAE1B,OAAO,CAACG,UAAW;UAC1BkE,QAAQ,EAAE9C,kBAAmB;UAC7BmC,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAEjBhE,OAAA;YAAQ+B,KAAK,EAAC,EAAE;YAAAiC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrCpE,OAAA;YAAQ+B,KAAK,EAAC,SAAS;YAAAiC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCpE,OAAA;YAAQ+B,KAAK,EAAC,UAAU;YAAAiC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CpE,OAAA;YAAQ+B,KAAK,EAAC,UAAU;YAAAiC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CpE,OAAA;YAAQ+B,KAAK,EAAC,UAAU;YAAAiC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAETpE,OAAA;UACE8B,IAAI,EAAC,kBAAkB;UACvBC,KAAK,EAAE1B,OAAO,CAACI,gBAAiB;UAChCiE,QAAQ,EAAE9C,kBAAmB;UAC7BmC,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAEjBhE,OAAA;YAAQ+B,KAAK,EAAC,EAAE;YAAAiC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CpE,OAAA;YAAQ+B,KAAK,EAAC,OAAO;YAAAiC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpCpE,OAAA;YAAQ+B,KAAK,EAAC,YAAY;YAAAiC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9CpE,OAAA;YAAQ+B,KAAK,EAAC,QAAQ;YAAAiC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCpE,OAAA;YAAQ+B,KAAK,EAAC,SAAS;YAAAiC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCpE,OAAA;YAAQ+B,KAAK,EAAC,eAAe;YAAAiC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eAETpE,OAAA;UACE8B,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAE1B,OAAO,CAACK,MAAO;UACtBgE,QAAQ,EAAE9C,kBAAmB;UAC7BmC,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAEjBhE,OAAA;YAAQ+B,KAAK,EAAC,EAAE;YAAAiC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpCpE,OAAA;YAAQ+B,KAAK,EAAC,QAAQ;YAAAiC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCpE,OAAA;YAAQ+B,KAAK,EAAC,UAAU;YAAAiC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CpE,OAAA;YAAQ+B,KAAK,EAAC,WAAW;YAAAiC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAETpE,OAAA;UAAQ+D,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC7DhE,OAAA,CAACN,MAAM;YAACqE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BpE,OAAA;YAAAgE,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpE,OAAA;MAAK+D,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEnB,gBAAgB,CAAC8B,GAAG,CAAE5B,OAAO,iBAC5B/C,OAAA;QAAsB+D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACtEhE,OAAA;UAAK+D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDhE,OAAA;YAAAgE,QAAA,gBACEhE,OAAA;cAAI+D,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAChDjB,OAAO,CAACjC;YAAS;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACLpE,OAAA;cAAG+D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GACjCjB,OAAO,CAAClC,UAAU,EAAC,UAAG,EAACkC,OAAO,CAACvC,UAAU,EAAC,GAAC,EAACuC,OAAO,CAAChC,OAAO;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNpE,OAAA;YAAK+D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDhE,OAAA;cAAM+D,SAAS,EAAEvB,cAAc,CAACO,OAAO,CAACrC,MAAM,CAAE;cAAAsD,QAAA,EAC7CjB,OAAO,CAACrC;YAAM;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,EACNrB,OAAO,CAACtC,gBAAgB,iBACvBT,OAAA;cAAM+D,SAAS,EAAE7B,mBAAmB,CAACa,OAAO,CAACtC,gBAAgB,CAAE;cAAAuD,QAAA,EAC5DjB,OAAO,CAACtC;YAAgB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpE,OAAA;UAAK+D,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrChE,OAAA;YAAK+D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnChE,OAAA;cAAM+D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDpE,OAAA;cAAM+D,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEjB,OAAO,CAAC/B;YAAW;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNpE,OAAA;YAAK+D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnChE,OAAA;cAAM+D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9CpE,OAAA;cAAM+D,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEjB,OAAO,CAAC9B;YAAW;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNpE,OAAA;YAAK+D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnChE,OAAA;cAAM+D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDpE,OAAA;cAAM+D,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAEjB,OAAO,CAACpB,cAAc,EAAC,GAAC;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNpE,OAAA;YAAK+D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnChE,OAAA;cAAM+D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDpE,OAAA;cAAM+D,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAEjB,OAAO,CAACtB,qBAAqB,EAAC,GAAC;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpE,OAAA;UAAK+D,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7ChE,OAAA;YAAI+D,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEpE,OAAA;YAAK+D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChChE,OAAA;cAAK+D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnChE,OAAA;gBAAM+D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDpE,OAAA;gBAAM+D,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,QAAC,EAACjB,OAAO,CAACzB,gBAAgB;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,EACLrB,OAAO,CAACtC,gBAAgB,iBACvBT,OAAA,CAAAE,SAAA;cAAA8D,QAAA,gBACEhE,OAAA;gBAAK+D,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnChE,OAAA;kBAAM+D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDpE,OAAA;kBAAM+D,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,GAAC,EAACjB,OAAO,CAACxB,sBAAsB,EAAC,GAAC;gBAAA;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACNpE,OAAA;gBAAK+D,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnChE,OAAA;kBAAM+D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CpE,OAAA;kBAAM+D,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,QAC1C,EAACjB,OAAO,CAACzB,gBAAgB,GAAGyB,OAAO,CAACvB,iBAAiB;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,eACN,CACH,eACDpE,OAAA;cAAK+D,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACjDhE,OAAA;gBAAM+D,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7DpE,OAAA;gBAAM+D,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GAAC,QAAC,EAACjB,OAAO,CAACvB,iBAAiB;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpE,OAAA;UAAK+D,SAAS,EAAC,wDAAwD;UAAAC,QAAA,gBACrEhE,OAAA,CAACF,MAAM;YAACiE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BpE,OAAA;YAAAgE,QAAA,GAAOjB,OAAO,CAAC3B,WAAW,EAAC,UAAG,EAAC2B,OAAO,CAAC1B,SAAS;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAGNpE,OAAA;UAAK+D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDhE,OAAA;YAAK+D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhE,OAAA;cAAQ+D,SAAS,EAAC,yCAAyC;cAAAC,QAAA,eACzDhE,OAAA,CAACR,MAAM;gBAACuE,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACTpE,OAAA;cAAQ+D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eACnDhE,OAAA,CAACH,KAAK;gBAACkE,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACTpE,OAAA;cAAQ+D,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eACrDhE,OAAA,CAACL,KAAK;gBAACoE,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENpE,OAAA;YAAQ+D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GA9FErB,OAAO,CAACnC,EAAE;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+Ff,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpE,OAAA;MAAK+D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBhE,OAAA;QAAI+D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjFpE,OAAA;QAAK+D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnD,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,CAAC,CAACW,GAAG,CAACH,IAAI,IAAI;UACzE,MAAMI,YAAY,GAAGjE,QAAQ,CAACmC,MAAM,CAACW,CAAC,IAAIA,CAAC,CAAChD,gBAAgB,KAAK+D,IAAI,CAAC;UACtE,MAAMK,WAAW,GAAGD,YAAY,CAACjB,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,IAAIH,CAAC,CAACnC,gBAAgB,GAAGmC,CAAC,CAACjC,iBAAiB,CAAC,EAAE,CAAC,CAAC;UAExG,oBACExB,OAAA;YAAgB+D,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAC/DhE,OAAA;cAAK+D,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAEY,YAAY,CAACrB;YAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5EpE,OAAA;cAAK+D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEQ;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDpE,OAAA;cAAK+D,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,QAAC,EAACa,WAAW,CAACN,cAAc,CAAC,CAAC,EAAC,QAAM;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA,GAHvFI,IAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIT,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLvB,gBAAgB,CAACU,MAAM,KAAK,CAAC,iBAC5BvD,OAAA;MAAK+D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChE,OAAA,CAACV,SAAS;QAACyE,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9DpE,OAAA;QAAK+D,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC3DpE,OAAA;QAAQ+D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAEpC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChE,EAAA,CA/WID,iBAAiB;AAAA2E,EAAA,GAAjB3E,iBAAiB;AAiXvB,eAAeA,iBAAiB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
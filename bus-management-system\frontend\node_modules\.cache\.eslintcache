[{"C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Login.js": "4", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\Layout.js": "6", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Register.js": "7", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Dashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\RouteManagement.js": "9", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\BusManagement.js": "10", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\StudentManagement.js": "11", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\DriverManagement.js": "12", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\AttendanceManagement.js": "13", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\TripManagement.js": "14", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\MaintenanceManagement.js": "15", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\LiveTracking.js": "16", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\FeeManagement.js": "17", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Reports.js": "18", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Profile.js": "19", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\NotFound.js": "20", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\LoadingSpinner.js": "21", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\DocumentModal.js": "22", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\BusModal.js": "23", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\services\\api.js": "24", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\SimpleLayout.js": "25"}, {"size": 1413, "mtime": 1753052593713, "results": "26", "hashOfConfig": "27"}, {"size": 4794, "mtime": 1753059499036, "results": "28", "hashOfConfig": "27"}, {"size": 7270, "mtime": 1753052690132, "results": "29", "hashOfConfig": "27"}, {"size": 6747, "mtime": 1753053110183, "results": "30", "hashOfConfig": "27"}, {"size": 2083, "mtime": 1753052995267, "results": "31", "hashOfConfig": "27"}, {"size": 7757, "mtime": 1753059214335, "results": "32", "hashOfConfig": "27"}, {"size": 11236, "mtime": 1753053151823, "results": "33", "hashOfConfig": "27"}, {"size": 12067, "mtime": 1753059709825, "results": "34", "hashOfConfig": "27"}, {"size": 1731, "mtime": 1753059777585, "results": "35", "hashOfConfig": "27"}, {"size": 12149, "mtime": 1753053203631, "results": "36", "hashOfConfig": "27"}, {"size": 1367, "mtime": 1753053456098, "results": "37", "hashOfConfig": "27"}, {"size": 1462, "mtime": 1753053414812, "results": "38", "hashOfConfig": "27"}, {"size": 1388, "mtime": 1753053489110, "results": "39", "hashOfConfig": "27"}, {"size": 1331, "mtime": 1753053473920, "results": "40", "hashOfConfig": "27"}, {"size": 1378, "mtime": 1753053519270, "results": "41", "hashOfConfig": "27"}, {"size": 1241, "mtime": 1753053537736, "results": "42", "hashOfConfig": "27"}, {"size": 1350, "mtime": 1753053503675, "results": "43", "hashOfConfig": "27"}, {"size": 1421, "mtime": 1753053553699, "results": "44", "hashOfConfig": "27"}, {"size": 1275, "mtime": 1753053575488, "results": "45", "hashOfConfig": "27"}, {"size": 1259, "mtime": 1753053592750, "results": "46", "hashOfConfig": "27"}, {"size": 713, "mtime": 1753053009407, "results": "47", "hashOfConfig": "27"}, {"size": 17787, "mtime": 1753053334639, "results": "48", "hashOfConfig": "27"}, {"size": 16605, "mtime": 1753053265713, "results": "49", "hashOfConfig": "27"}, {"size": 8169, "mtime": 1753052654694, "results": "50", "hashOfConfig": "27"}, {"size": 7585, "mtime": 1753059438917, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j8150s", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Login.js", ["127"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\RouteManagement.js", ["128", "129"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\BusManagement.js", ["130"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\StudentManagement.js", ["131"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\DriverManagement.js", ["132", "133"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\AttendanceManagement.js", ["134"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\TripManagement.js", ["135"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\MaintenanceManagement.js", ["136"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\LiveTracking.js", ["137", "138"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\FeeManagement.js", ["139"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Reports.js", ["140"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Profile.js", ["141"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\DocumentModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\BusModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\SimpleLayout.js", [], [], {"ruleId": "142", "severity": 1, "message": "143", "line": 5, "column": 8, "nodeType": "144", "messageId": "145", "endLine": 5, "endColumn": 13}, {"ruleId": "142", "severity": 1, "message": "146", "line": 2, "column": 24, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 30}, {"ruleId": "142", "severity": 1, "message": "147", "line": 2, "column": 32, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 38}, {"ruleId": "142", "severity": 1, "message": "148", "line": 1, "column": 27, "nodeType": "144", "messageId": "145", "endLine": 1, "endColumn": 36}, {"ruleId": "142", "severity": 1, "message": "149", "line": 2, "column": 27, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 33}, {"ruleId": "142", "severity": 1, "message": "146", "line": 2, "column": 23, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 29}, {"ruleId": "142", "severity": 1, "message": "147", "line": 2, "column": 31, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 37}, {"ruleId": "142", "severity": 1, "message": "150", "line": 2, "column": 29, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 39}, {"ruleId": "142", "severity": 1, "message": "151", "line": 2, "column": 26, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 31}, {"ruleId": "142", "severity": 1, "message": "152", "line": 2, "column": 24, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 37}, {"ruleId": "142", "severity": 1, "message": "153", "line": 2, "column": 18, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 28}, {"ruleId": "142", "severity": 1, "message": "151", "line": 2, "column": 30, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 35}, {"ruleId": "142", "severity": 1, "message": "154", "line": 2, "column": 28, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 38}, {"ruleId": "142", "severity": 1, "message": "147", "line": 2, "column": 31, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 37}, {"ruleId": "142", "severity": 1, "message": "155", "line": 2, "column": 22, "nodeType": "144", "messageId": "145", "endLine": 2, "endColumn": 25}, "no-unused-vars", "'toast' is defined but never used.", "Identifier", "unusedVar", "'Search' is defined but never used.", "'Filter' is defined but never used.", "'useEffect' is defined but never used.", "'QrCode' is defined but never used.", "'Smartphone' is defined but never used.", "'Clock' is defined but never used.", "'AlertTriangle' is defined but never used.", "'Navigation' is defined but never used.", "'CreditCard' is defined but never used.", "'Key' is defined but never used."]
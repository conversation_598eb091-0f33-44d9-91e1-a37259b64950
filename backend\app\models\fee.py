from datetime import datetime, date
from app import db, ma

class Fee(db.Model):
    """Student fee management model."""
    __tablename__ = 'fees'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON>('students.id'), nullable=False)
    fee_type = db.Column(db.Enum('Tuition', 'Hostel', 'Transport', 'Library', 'Lab', 'Exam', 
                                'Miscellaneous', name='fee_types'), nullable=False)
    amount = db.Column(db.Decimal(10, 2), nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_amount = db.Column(db.Decimal(10, 2), default=0)
    payment_date = db.Column(db.Date)
    payment_method = db.Column(db.Enum('Cash', 'Card', 'Online', 'Cheque', 'DD', 
                                      name='payment_methods'))
    transaction_id = db.Column(db.String(100))
    status = db.Column(db.Enum('Pending', 'Paid', 'Partial', 'Overdue', name='fee_status'), 
                      default='Pending')
    academic_year = db.Column(db.String(10))  # e.g., "2023-24"
    semester = db.Column(db.Integer)
    remarks = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    @property
    def balance_amount(self):
        """Calculate remaining balance."""
        return float(self.amount - self.paid_amount)
    
    @property
    def is_overdue(self):
        """Check if fee is overdue."""
        return self.status != 'Paid' and date.today() > self.due_date
    
    @property
    def days_overdue(self):
        """Get number of days overdue."""
        if self.is_overdue:
            return (date.today() - self.due_date).days
        return 0
    
    def update_status(self):
        """Update fee status based on payment."""
        if self.paid_amount >= self.amount:
            self.status = 'Paid'
        elif self.paid_amount > 0:
            self.status = 'Partial'
        elif self.is_overdue:
            self.status = 'Overdue'
        else:
            self.status = 'Pending'
    
    def to_dict(self):
        """Convert fee to dictionary."""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'student_name': self.student.full_name if self.student else None,
            'student_roll': self.student.roll_number if self.student else None,
            'fee_type': self.fee_type,
            'amount': float(self.amount),
            'paid_amount': float(self.paid_amount),
            'balance_amount': self.balance_amount,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'payment_date': self.payment_date.isoformat() if self.payment_date else None,
            'payment_method': self.payment_method,
            'transaction_id': self.transaction_id,
            'status': self.status,
            'is_overdue': self.is_overdue,
            'days_overdue': self.days_overdue,
            'academic_year': self.academic_year,
            'semester': self.semester,
            'remarks': self.remarks,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @staticmethod
    def get_student_fee_summary(student_id):
        """Get fee summary for a student."""
        fees = Fee.query.filter_by(student_id=student_id).all()
        
        total_amount = sum(float(fee.amount) for fee in fees)
        paid_amount = sum(float(fee.paid_amount) for fee in fees)
        balance_amount = total_amount - paid_amount
        overdue_count = sum(1 for fee in fees if fee.is_overdue)
        
        return {
            'total_amount': total_amount,
            'paid_amount': paid_amount,
            'balance_amount': balance_amount,
            'overdue_count': overdue_count,
            'total_fees': len(fees)
        }
    
    def __repr__(self):
        return f'<Fee {self.student.roll_number} - {self.fee_type} - {self.amount}>'

class FeeSchema(ma.SQLAlchemyAutoSchema):
    """Fee serialization schema."""
    class Meta:
        model = Fee
        load_instance = True
        include_fk = True

fee_schema = FeeSchema()
fees_schema = FeeSchema(many=True)

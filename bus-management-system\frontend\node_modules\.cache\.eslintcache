[{"C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\SimpleLayout.js": "4", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Login.js": "6", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Register.js": "7", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Dashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\DriverManagement.js": "9", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\BusManagement.js": "10", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\TripManagement.js": "11", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\MaintenanceManagement.js": "12", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\RouteManagement.js": "13", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\AttendanceManagement.js": "14", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\FeeManagement.js": "15", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\StudentManagement.js": "16", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\LiveTracking.js": "17", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Reports.js": "18", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Profile.js": "19", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\NotFound.js": "20", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\LoadingSpinner.js": "21", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\BusModal.js": "22", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\DocumentModal.js": "23", "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\services\\api.js": "24"}, {"size": 1413, "mtime": 1753052593713, "results": "25", "hashOfConfig": "26"}, {"size": 4794, "mtime": 1753059499036, "results": "27", "hashOfConfig": "26"}, {"size": 7270, "mtime": 1753052690132, "results": "28", "hashOfConfig": "26"}, {"size": 7585, "mtime": 1753059438917, "results": "29", "hashOfConfig": "26"}, {"size": 2083, "mtime": 1753052995267, "results": "30", "hashOfConfig": "26"}, {"size": 6747, "mtime": 1753053110183, "results": "31", "hashOfConfig": "26"}, {"size": 11236, "mtime": 1753053151823, "results": "32", "hashOfConfig": "26"}, {"size": 20553, "mtime": 1754179212279, "results": "33", "hashOfConfig": "26"}, {"size": 9850, "mtime": 1754179309301, "results": "34", "hashOfConfig": "26"}, {"size": 12823, "mtime": 1754179244615, "results": "35", "hashOfConfig": "26"}, {"size": 1331, "mtime": 1753053473920, "results": "36", "hashOfConfig": "26"}, {"size": 1378, "mtime": 1753053519270, "results": "37", "hashOfConfig": "26"}, {"size": 11285, "mtime": 1754179378217, "results": "38", "hashOfConfig": "26"}, {"size": 1388, "mtime": 1753053489110, "results": "39", "hashOfConfig": "26"}, {"size": 17603, "mtime": 1754180103634, "results": "40", "hashOfConfig": "26"}, {"size": 15617, "mtime": 1754180012959, "results": "41", "hashOfConfig": "26"}, {"size": 1241, "mtime": 1753053537736, "results": "42", "hashOfConfig": "26"}, {"size": 1421, "mtime": 1753053553699, "results": "43", "hashOfConfig": "26"}, {"size": 1275, "mtime": 1753053575488, "results": "44", "hashOfConfig": "26"}, {"size": 1259, "mtime": 1753053592750, "results": "45", "hashOfConfig": "26"}, {"size": 713, "mtime": 1753053009407, "results": "46", "hashOfConfig": "26"}, {"size": 16605, "mtime": 1753053265713, "results": "47", "hashOfConfig": "26"}, {"size": 17787, "mtime": 1753053334639, "results": "48", "hashOfConfig": "26"}, {"size": 8169, "mtime": 1753052654694, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j8150s", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\SimpleLayout.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Login.js", ["122"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\DriverManagement.js", ["123"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\BusManagement.js", ["124"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\TripManagement.js", ["125"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\MaintenanceManagement.js", ["126"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\RouteManagement.js", ["127", "128"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\AttendanceManagement.js", ["129"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\FeeManagement.js", ["130"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\StudentManagement.js", ["131"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\LiveTracking.js", ["132", "133"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Reports.js", ["134"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\Profile.js", ["135"], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\BusModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\components\\DocumentModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\src\\services\\api.js", [], [], {"ruleId": "136", "severity": 1, "message": "137", "line": 5, "column": 8, "nodeType": "138", "messageId": "139", "endLine": 5, "endColumn": 13}, {"ruleId": "136", "severity": 1, "message": "140", "line": 2, "column": 10, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 18}, {"ruleId": "136", "severity": 1, "message": "141", "line": 1, "column": 27, "nodeType": "138", "messageId": "139", "endLine": 1, "endColumn": 36}, {"ruleId": "136", "severity": 1, "message": "142", "line": 2, "column": 26, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 31}, {"ruleId": "136", "severity": 1, "message": "143", "line": 2, "column": 24, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 37}, {"ruleId": "136", "severity": 1, "message": "144", "line": 2, "column": 32, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 38}, {"ruleId": "136", "severity": 1, "message": "145", "line": 2, "column": 59, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 62}, {"ruleId": "136", "severity": 1, "message": "146", "line": 2, "column": 29, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 39}, {"ruleId": "136", "severity": 1, "message": "147", "line": 2, "column": 28, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 38}, {"ruleId": "136", "severity": 1, "message": "140", "line": 2, "column": 10, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 18}, {"ruleId": "136", "severity": 1, "message": "148", "line": 2, "column": 18, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 28}, {"ruleId": "136", "severity": 1, "message": "142", "line": 2, "column": 30, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 35}, {"ruleId": "136", "severity": 1, "message": "144", "line": 2, "column": 31, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 37}, {"ruleId": "136", "severity": 1, "message": "149", "line": 2, "column": 22, "nodeType": "138", "messageId": "139", "endLine": 2, "endColumn": 25}, "no-unused-vars", "'toast' is defined but never used.", "Identifier", "unusedVar", "'useQuery' is defined but never used.", "'useEffect' is defined but never used.", "'Clock' is defined but never used.", "'AlertTriangle' is defined but never used.", "'Filter' is defined but never used.", "'Bus' is defined but never used.", "'Smartphone' is defined but never used.", "'CreditCard' is defined but never used.", "'Navigation' is defined but never used.", "'Key' is defined but never used."]
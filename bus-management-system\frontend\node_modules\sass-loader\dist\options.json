{"title": "Sass Loader options", "type": "object", "properties": {"implementation": {"description": "The implementation of the sass to be used.", "link": "https://github.com/webpack-contrib/sass-loader#implementation", "anyOf": [{"type": "string"}, {"type": "object"}]}, "api": {"description": "Switch between old and modern API for `sass` (`Dart Sass`) and `Sass Embedded` implementations.", "link": "https://github.com/webpack-contrib/sass-loader#sassoptions", "enum": ["legacy", "modern"]}, "sassOptions": {"description": "Options for `node-sass` or `sass` (`Dart Sass`) implementation.", "link": "https://github.com/webpack-contrib/sass-loader#sassoptions", "anyOf": [{"type": "object", "additionalProperties": true}, {"instanceof": "Function"}]}, "additionalData": {"description": "Prepends/Appends `Sass`/`SCSS` code before the actual entry file.", "link": "https://github.com/webpack-contrib/sass-loader#additionaldata", "anyOf": [{"type": "string"}, {"instanceof": "Function"}]}, "sourceMap": {"description": "Enables/Disables generation of source maps.", "link": "https://github.com/webpack-contrib/sass-loader#sourcemap", "type": "boolean"}, "webpackImporter": {"description": "Enables/Disables default `webpack` importer.", "link": "https://github.com/webpack-contrib/sass-loader#webpackimporter", "type": "boolean"}, "warnRuleAsWarning": {"description": "Treats the '@warn' rule as a webpack warning.", "link": "https://github.com/webpack-contrib/sass-loader#warnruleaswarning", "type": "boolean"}}, "additionalProperties": false}
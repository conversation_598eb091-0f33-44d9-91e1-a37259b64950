{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Dumbbell = createLucideIcon(\"<PERSON>mb<PERSON>\", [[\"path\", {\n  d: \"m6.5 6.5 11 11\",\n  key: \"f7oqzb\"\n}], [\"path\", {\n  d: \"m21 21-1-1\",\n  key: \"cpc6if\"\n}], [\"path\", {\n  d: \"m3 3 1 1\",\n  key: \"d3rpuf\"\n}], [\"path\", {\n  d: \"m18 22 4-4\",\n  key: \"1e32o6\"\n}], [\"path\", {\n  d: \"m2 6 4-4\",\n  key: \"189tqz\"\n}], [\"path\", {\n  d: \"m3 10 7-7\",\n  key: \"1bxui2\"\n}], [\"path\", {\n  d: \"m14 21 7-7\",\n  key: \"16x78n\"\n}]]);\nexport { Dumbbell as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\dumbbell.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Dumbbell\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNi41IDYuNSAxMSAxMSIgLz4KICA8cGF0aCBkPSJtMjEgMjEtMS0xIiAvPgogIDxwYXRoIGQ9Im0zIDMgMSAxIiAvPgogIDxwYXRoIGQ9Im0xOCAyMiA0LTQiIC8+CiAgPHBhdGggZD0ibTIgNiA0LTQiIC8+CiAgPHBhdGggZD0ibTMgMTAgNy03IiAvPgogIDxwYXRoIGQ9Im0xNCAyMSA3LTciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/dumbbell\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Dumbbell = createLucideIcon('Dumbbell', [\n  ['path', { d: 'm6.5 6.5 11 11', key: 'f7oqzb' }],\n  ['path', { d: 'm21 21-1-1', key: 'cpc6if' }],\n  ['path', { d: 'm3 3 1 1', key: 'd3rpuf' }],\n  ['path', { d: 'm18 22 4-4', key: '1e32o6' }],\n  ['path', { d: 'm2 6 4-4', key: '189tqz' }],\n  ['path', { d: 'm3 10 7-7', key: '1bxui2' }],\n  ['path', { d: 'm14 21 7-7', key: '16x78n' }],\n]);\n\nexport default Dumbbell;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
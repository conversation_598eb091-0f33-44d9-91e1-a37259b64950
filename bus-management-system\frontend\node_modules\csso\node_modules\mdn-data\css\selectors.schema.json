{"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"syntax": {"type": "string"}, "groups": {"type": "array", "minitems": 1, "uniqueItems": true, "items": {"$ref": "definitions.json#/groupList"}}, "status": {"enum": ["standard", "nonstandard", "experimental"]}, "mdn_url": {"type": "string", "pattern": "^https://developer.mozilla.org/docs/Web/CSS/"}}, "required": ["syntax", "groups", "status"]}}
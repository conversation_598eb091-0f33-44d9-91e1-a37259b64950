{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst GitBranchPlus = createLucideIcon(\"GitBranchPlus\", [[\"path\", {\n  d: \"M6 3v12\",\n  key: \"qpgusn\"\n}], [\"path\", {\n  d: \"M18 9a3 3 0 1 0 0-6 3 3 0 0 0 0 6z\",\n  key: \"1d02ji\"\n}], [\"path\", {\n  d: \"M6 21a3 3 0 1 0 0-6 3 3 0 0 0 0 6z\",\n  key: \"chk6ph\"\n}], [\"path\", {\n  d: \"M15 6a9 9 0 0 0-9 9\",\n  key: \"or332x\"\n}], [\"path\", {\n  d: \"M18 15v6\",\n  key: \"9wciyi\"\n}], [\"path\", {\n  d: \"M21 18h-6\",\n  key: \"139f0c\"\n}]]);\nexport { GitBranchPlus as default };", "map": {"version": 3, "names": ["GitBranchPlus", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\git-branch-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name GitBranchPlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAzdjEyIiAvPgogIDxwYXRoIGQ9Ik0xOCA5YTMgMyAwIDEgMCAwLTYgMyAzIDAgMCAwIDAgNnoiIC8+CiAgPHBhdGggZD0iTTYgMjFhMyAzIDAgMSAwIDAtNiAzIDMgMCAwIDAgMCA2eiIgLz4KICA8cGF0aCBkPSJNMTUgNmE5IDkgMCAwIDAtOSA5IiAvPgogIDxwYXRoIGQ9Ik0xOCAxNXY2IiAvPgogIDxwYXRoIGQ9Ik0yMSAxOGgtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/git-branch-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GitBranchPlus = createLucideIcon('GitBranchPlus', [\n  ['path', { d: 'M6 3v12', key: 'qpgusn' }],\n  ['path', { d: 'M18 9a3 3 0 1 0 0-6 3 3 0 0 0 0 6z', key: '1d02ji' }],\n  ['path', { d: 'M6 21a3 3 0 1 0 0-6 3 3 0 0 0 0 6z', key: 'chk6ph' }],\n  ['path', { d: 'M15 6a9 9 0 0 0-9 9', key: 'or332x' }],\n  ['path', { d: 'M18 15v6', key: '9wciyi' }],\n  ['path', { d: 'M21 18h-6', key: '139f0c' }],\n]);\n\nexport default GitBranchPlus;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
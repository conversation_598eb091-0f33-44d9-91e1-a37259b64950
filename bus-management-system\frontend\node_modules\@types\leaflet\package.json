{"name": "@types/leaflet", "version": "1.9.20", "description": "TypeScript definitions for leaflet", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/leaflet", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "alejo90", "url": "https://github.com/alejo90"}, {"name": "<PERSON><PERSON>", "githubUsername": "atd-schubert", "url": "https://github.com/atd-schubert"}, {"name": "<PERSON>", "githubUsername": "m<PERSON>uer", "url": "https://github.com/mcauer"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ronikar"}, {"name": "<PERSON>", "githubUsername": "life777", "url": "https://github.com/life777"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/henry<PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "captain-<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/captain-igloo"}, {"name": "<PERSON>", "githubUsername": "someonewithpc", "url": "https://github.com/someonewithpc"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/leaflet"}, "scripts": {}, "dependencies": {"@types/geojson": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "6129f7d1b7c45052292fd02a136a3cdfe14399eb5d7a026e642a7d849492bd0a", "typeScriptVersion": "5.1"}
from flask import Blueprint, request
from flask_jwt_extended import jwt_required
from app import db
from app.models.department import Department
from app.models.faculty import Faculty
from app.utils.decorators import admin_required
from app.utils.helpers import success_response, error_response, paginate_query

departments_bp = Blueprint('departments', __name__)

@departments_bp.route('', methods=['GET'])
@jwt_required()
def get_departments():
    """Get all departments with pagination and filtering."""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '').strip()
        
        query = Department.query.filter_by(is_active=True)
        
        if search:
            query = query.filter(
                (Department.name.contains(search)) |
                (Department.code.contains(search))
            )
        
        query = query.order_by(Department.name)
        paginated = paginate_query(query, page, per_page)
        
        if not paginated:
            return error_response('Invalid pagination parameters')
        
        departments_data = [dept.to_dict() for dept in paginated['items']]
        
        return success_response('Departments retrieved successfully', {
            'departments': departments_data,
            'pagination': {
                'total': paginated['total'],
                'pages': paginated['pages'],
                'current_page': paginated['current_page'],
                'per_page': paginated['per_page'],
                'has_next': paginated['has_next'],
                'has_prev': paginated['has_prev']
            }
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve departments: {str(e)}', 500)

@departments_bp.route('/<int:department_id>', methods=['GET'])
@jwt_required()
def get_department(department_id):
    """Get a specific department by ID."""
    try:
        department = Department.query.filter_by(id=department_id, is_active=True).first()
        
        if not department:
            return error_response('Department not found', 404)
        
        return success_response('Department retrieved successfully', {
            'department': department.to_dict()
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve department: {str(e)}', 500)

@departments_bp.route('', methods=['POST'])
@admin_required
def create_department(current_user):
    """Create a new department."""
    try:
        data = request.get_json()
        
        if not data:
            return error_response('No data provided')
        
        required_fields = ['name', 'code']
        for field in required_fields:
            if not data.get(field):
                return error_response(f'{field.replace("_", " ").title()} is required')
        
        # Check if department name or code already exists
        name = data.get('name').strip()
        code = data.get('code').strip().upper()
        
        if Department.query.filter_by(name=name).first():
            return error_response('Department name already exists')
        
        if Department.query.filter_by(code=code).first():
            return error_response('Department code already exists')
        
        # Validate HOD if provided
        hod_id = data.get('hod_id')
        if hod_id:
            hod = Faculty.query.get(hod_id)
            if not hod:
                return error_response('HOD not found')
        
        department = Department(
            name=name,
            code=code,
            description=data.get('description'),
            hod_id=hod_id,
            established_year=data.get('established_year')
        )
        
        db.session.add(department)
        db.session.commit()
        
        return success_response('Department created successfully', {
            'department': department.to_dict()
        }, 201)
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to create department: {str(e)}', 500)

@departments_bp.route('/<int:department_id>', methods=['PUT'])
@admin_required
def update_department(current_user, department_id):
    """Update a department."""
    try:
        department = Department.query.filter_by(id=department_id, is_active=True).first()
        
        if not department:
            return error_response('Department not found', 404)
        
        data = request.get_json()
        if not data:
            return error_response('No data provided')
        
        # Update fields if provided
        if 'name' in data:
            name = data['name'].strip()
            existing_dept = Department.query.filter(
                Department.name == name, Department.id != department_id
            ).first()
            if existing_dept:
                return error_response('Department name already exists')
            department.name = name
        
        if 'code' in data:
            code = data['code'].strip().upper()
            existing_dept = Department.query.filter(
                Department.code == code, Department.id != department_id
            ).first()
            if existing_dept:
                return error_response('Department code already exists')
            department.code = code
        
        if 'description' in data:
            department.description = data['description']
        
        if 'hod_id' in data:
            hod_id = data['hod_id']
            if hod_id:
                hod = Faculty.query.get(hod_id)
                if not hod:
                    return error_response('HOD not found')
            department.hod_id = hod_id
        
        if 'established_year' in data:
            department.established_year = data['established_year']
        
        db.session.commit()
        
        return success_response('Department updated successfully', {
            'department': department.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to update department: {str(e)}', 500)

@departments_bp.route('/<int:department_id>', methods=['DELETE'])
@admin_required
def delete_department(current_user, department_id):
    """Delete (deactivate) a department."""
    try:
        department = Department.query.filter_by(id=department_id, is_active=True).first()
        
        if not department:
            return error_response('Department not found', 404)
        
        # Check if department has active students or faculty
        if department.students.filter_by(is_active=True).count() > 0:
            return error_response('Cannot delete department with active students')
        
        if department.faculty.filter_by(is_active=True).count() > 0:
            return error_response('Cannot delete department with active faculty')
        
        department.is_active = False
        db.session.commit()
        
        return success_response('Department deleted successfully')
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to delete department: {str(e)}', 500)

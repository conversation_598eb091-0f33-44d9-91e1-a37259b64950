{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst AlignHorizontalSpaceBetween = createLucideIcon(\"AlignHorizontalSpaceBetween\", [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"3\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"j77dae\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"15\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"bq30hj\"\n}], [\"path\", {\n  d: \"M3 2v20\",\n  key: \"1d2pfg\"\n}], [\"path\", {\n  d: \"M21 2v20\",\n  key: \"p059bm\"\n}]]);\nexport { AlignHorizontalSpaceBetween as default };", "map": {"version": 3, "names": ["AlignHorizontalSpaceBetween", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\align-horizontal-space-between.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignHorizontalSpaceBetween\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSIxNCIgeD0iMyIgeT0iNSIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjYiIGhlaWdodD0iMTAiIHg9IjE1IiB5PSI3IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNMyAydjIwIiAvPgogIDxwYXRoIGQ9Ik0yMSAydjIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/align-horizontal-space-between\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignHorizontalSpaceBetween = createLucideIcon(\n  'AlignHorizontalSpaceBetween',\n  [\n    [\n      'rect',\n      { width: '6', height: '14', x: '3', y: '5', rx: '2', key: 'j77dae' },\n    ],\n    [\n      'rect',\n      { width: '6', height: '10', x: '15', y: '7', rx: '2', key: 'bq30hj' },\n    ],\n    ['path', { d: 'M3 2v20', key: '1d2pfg' }],\n    ['path', { d: 'M21 2v20', key: 'p059bm' }],\n  ],\n);\n\nexport default AlignHorizontalSpaceBetween;\n"], "mappings": ";;;;;AAaA,MAAMA,2BAA8B,GAAAC,gBAAA,CAClC,+BACA,CACE,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CACE,QACA;EAAEL,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAE7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
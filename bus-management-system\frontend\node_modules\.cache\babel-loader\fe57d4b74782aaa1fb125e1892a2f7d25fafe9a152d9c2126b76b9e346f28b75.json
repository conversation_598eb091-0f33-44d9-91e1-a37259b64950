{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ChevronsRightLeft = createLucideIcon(\"ChevronsRightLeft\", [[\"path\", {\n  d: \"m20 17-5-5 5-5\",\n  key: \"30x0n2\"\n}], [\"path\", {\n  d: \"m4 17 5-5-5-5\",\n  key: \"16spf4\"\n}]]);\nexport { ChevronsRightLeft as default };", "map": {"version": 3, "names": ["ChevronsRightLeft", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\chevrons-right-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronsRightLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjAgMTctNS01IDUtNSIgLz4KICA8cGF0aCBkPSJtNCAxNyA1LTUtNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevrons-right-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronsRightLeft = createLucideIcon('ChevronsRightLeft', [\n  ['path', { d: 'm20 17-5-5 5-5', key: '30x0n2' }],\n  ['path', { d: 'm4 17 5-5-5-5', key: '16spf4' }],\n]);\n\nexport default ChevronsRightLeft;\n"], "mappings": ";;;;;AAaM,MAAAA,iBAAA,GAAoBC,gBAAA,CAAiB,mBAAqB,GAC9D,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import os
from app import create_app, db
from app.models import User, Student, Faculty, Course, Department

app = create_app(os.getenv('FLASK_CONFIG') or 'default')

@app.shell_context_processor
def make_shell_context():
    return dict(db=db, User=User, Student=Student, Faculty=Faculty, 
                Course=Course, Department=Department)

@app.cli.command()
def create_admin():
    """Create an admin user."""
    from app.models import User
    from werkzeug.security import generate_password_hash
    
    admin = User(
        username='admin',
        email='<EMAIL>',
        password_hash=generate_password_hash('admin123'),
        role='admin',
        is_active=True
    )
    
    db.session.add(admin)
    db.session.commit()
    print('Admin user created successfully!')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

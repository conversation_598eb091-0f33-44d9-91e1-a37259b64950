{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Luggage = createLucideIcon(\"Luggage\", [[\"path\", {\n  d: \"M6 20h0a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h0\",\n  key: \"1h5fkc\"\n}], [\"path\", {\n  d: \"M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14\",\n  key: \"1l99gc\"\n}], [\"path\", {\n  d: \"M10 20h4\",\n  key: \"ni2waw\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"20\",\n  r: \"2\",\n  key: \"1vifvg\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"20\",\n  r: \"2\",\n  key: \"ckkr5m\"\n}]]);\nexport { Luggage as default };", "map": {"version": 3, "names": ["Luggage", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\luggage.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Luggage\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAyMGgwYTIgMiAwIDAgMS0yLTJWOGEyIDIgMCAwIDEgMi0yaDEyYTIgMiAwIDAgMSAyIDJ2MTBhMiAyIDAgMCAxLTIgMmgwIiAvPgogIDxwYXRoIGQ9Ik04IDE4VjRhMiAyIDAgMCAxIDItMmg0YTIgMiAwIDAgMSAyIDJ2MTQiIC8+CiAgPHBhdGggZD0iTTEwIDIwaDQiIC8+CiAgPGNpcmNsZSBjeD0iMTYiIGN5PSIyMCIgcj0iMiIgLz4KICA8Y2lyY2xlIGN4PSI4IiBjeT0iMjAiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/luggage\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Luggage = createLucideIcon('Luggage', [\n  [\n    'path',\n    {\n      d: 'M6 20h0a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h0',\n      key: '1h5fkc',\n    },\n  ],\n  ['path', { d: 'M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14', key: '1l99gc' }],\n  ['path', { d: 'M10 20h4', key: 'ni2waw' }],\n  ['circle', { cx: '16', cy: '20', r: '2', key: '1vifvg' }],\n  ['circle', { cx: '8', cy: '20', r: '2', key: 'ckkr5m' }],\n]);\n\nexport default Luggage;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
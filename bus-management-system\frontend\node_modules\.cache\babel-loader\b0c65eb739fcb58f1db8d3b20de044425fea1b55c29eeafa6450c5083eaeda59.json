{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst BookKey = createLucideIcon(\"BookKey\", [[\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H14\",\n  key: \"1gfsgw\"\n}], [\"path\", {\n  d: \"M20 8v14H6.5a2.5 2.5 0 0 1 0-5H20\",\n  key: \"zb0ngp\"\n}], [\"circle\", {\n  cx: \"14\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"u49eql\"\n}], [\"path\", {\n  d: \"m20 2-4.5 4.5\",\n  key: \"1sppr8\"\n}], [\"path\", {\n  d: \"m19 3 1 1\",\n  key: \"ze14oc\"\n}]]);\nexport { BookKey as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\book-key.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BookKey\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxOS41di0xNUEyLjUgMi41IDAgMCAxIDYuNSAySDE0IiAvPgogIDxwYXRoIGQ9Ik0yMCA4djE0SDYuNWEyLjUgMi41IDAgMCAxIDAtNUgyMCIgLz4KICA8Y2lyY2xlIGN4PSIxNCIgY3k9IjgiIHI9IjIiIC8+CiAgPHBhdGggZD0ibTIwIDItNC41IDQuNSIgLz4KICA8cGF0aCBkPSJtMTkgMyAxIDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/book-key\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookKey = createLucideIcon('BookKey', [\n  ['path', { d: 'M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H14', key: '1gfsgw' }],\n  ['path', { d: 'M20 8v14H6.5a2.5 2.5 0 0 1 0-5H20', key: 'zb0ngp' }],\n  ['circle', { cx: '14', cy: '8', r: '2', key: 'u49eql' }],\n  ['path', { d: 'm20 2-4.5 4.5', key: '1sppr8' }],\n  ['path', { d: 'm19 3 1 1', key: 'ze14oc' }],\n]);\n\nexport default BookKey;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAED,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": 3, "file": "array.from.mjs", "names": ["toStr", "Object", "prototype", "toString", "isCallable", "fn", "call", "toInteger", "value", "number", "Number", "isNaN", "isFinite", "Math", "floor", "abs", "maxSafeInteger", "pow", "to<PERSON><PERSON><PERSON>", "len", "min", "max", "arrayFrom", "arrayLike", "mapFn", "C", "Array", "items", "TypeError", "length", "A", "k", "kValue"], "sources": ["../../sources/polyfills/array.from.ts"], "sourcesContent": ["/**\n * @source {https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Polyfill}\n * but without thisArg (too hard to type, no need to `this`)\n */\nconst toStr = Object.prototype.toString;\nfunction isCallable(fn: unknown): boolean {\n\treturn typeof fn === \"function\" || toStr.call(fn) === \"[object Function]\";\n}\nfunction toInteger(value: unknown): number {\n\tconst number = Number(value);\n\tif (isNaN(number)) {\n\t\treturn 0;\n\t}\n\tif (number === 0 || !isFinite(number)) {\n\t\treturn number;\n\t}\n\treturn (number > 0 ? 1 : -1) * Math.floor(Math.abs(number));\n}\nconst maxSafeInteger = Math.pow(2, 53) - 1;\nfunction toLength(value: unknown): number {\n\tconst len = toInteger(value);\n\treturn Math.min(Math.max(len, 0), maxSafeInteger);\n}\n/**\n * Creates an array from an iterable object.\n * @param iterable An iterable object to convert to an array.\n */\nexport default function arrayFrom<T>(iterable: Iterable<T> | ArrayLike<T>): T[];\n/**\n * Creates an array from an iterable object.\n * @param iterable An iterable object to convert to an array.\n * @param mapfn A mapping function to call on every element of the array.\n * @param thisArg Value of 'this' used to invoke the mapfn.\n */\nexport default function arrayFrom<T, U>(\n\tarrayLike: Iterable<T> | ArrayLike<T>,\n\tmapFn?: (v: T, k: number) => U\n): U[] {\n\t// 1. Let C be the this value.\n\t// edit(@eps1lon): we're not calling it as Array.from\n\tconst C = Array;\n\n\t// 2. Let items be ToObject(arrayLike).\n\tconst items = Object(arrayLike);\n\n\t// 3. ReturnIfAbrupt(items).\n\tif (arrayLike == null) {\n\t\tthrow new TypeError(\n\t\t\t\"Array.from requires an array-like object - not null or undefined\"\n\t\t);\n\t}\n\n\t// 4. If mapfn is undefined, then let mapping be false.\n\t// const mapFn = arguments.length > 1 ? arguments[1] : void undefined;\n\n\tif (typeof mapFn !== \"undefined\") {\n\t\t// 5. else\n\t\t// 5. a If IsCallable(mapfn) is false, throw a TypeError exception.\n\t\tif (!isCallable(mapFn)) {\n\t\t\tthrow new TypeError(\n\t\t\t\t\"Array.from: when provided, the second argument must be a function\"\n\t\t\t);\n\t\t}\n\t}\n\n\t// 10. Let lenValue be Get(items, \"length\").\n\t// 11. Let len be ToLength(lenValue).\n\tconst len = toLength(items.length);\n\n\t// 13. If IsConstructor(C) is true, then\n\t// 13. a. Let A be the result of calling the [[Construct]] internal method\n\t// of C with an argument list containing the single item len.\n\t// 14. a. Else, Let A be ArrayCreate(len).\n\tconst A = isCallable(C) ? Object(new C(len)) : new Array(len);\n\n\t// 16. Let k be 0.\n\tlet k = 0;\n\t// 17. Repeat, while k < len… (also steps a - h)\n\tlet kValue;\n\twhile (k < len) {\n\t\tkValue = items[k];\n\t\tif (mapFn) {\n\t\t\tA[k] = mapFn(kValue, k);\n\t\t} else {\n\t\t\tA[k] = kValue;\n\t\t}\n\t\tk += 1;\n\t}\n\t// 18. Let putStatus be Put(A, \"length\", len, true).\n\tA.length = len;\n\t// 20. Return A.\n\treturn A;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAMA,KAAK,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ;AACvC,SAASC,UAAU,CAACC,EAAW,EAAW;EACzC,OAAO,OAAOA,EAAE,KAAK,UAAU,IAAIL,KAAK,CAACM,IAAI,CAACD,EAAE,CAAC,KAAK,mBAAmB;AAC1E;AACA,SAASE,SAAS,CAACC,KAAc,EAAU;EAC1C,IAAMC,MAAM,GAAGC,MAAM,CAACF,KAAK,CAAC;EAC5B,IAAIG,KAAK,CAACF,MAAM,CAAC,EAAE;IAClB,OAAO,CAAC;EACT;EACA,IAAIA,MAAM,KAAK,CAAC,IAAI,CAACG,QAAQ,CAACH,MAAM,CAAC,EAAE;IACtC,OAAOA,MAAM;EACd;EACA,OAAO,CAACA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAII,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,MAAM,CAAC,CAAC;AAC5D;AACA,IAAMO,cAAc,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AAC1C,SAASC,QAAQ,CAACV,KAAc,EAAU;EACzC,IAAMW,GAAG,GAAGZ,SAAS,CAACC,KAAK,CAAC;EAC5B,OAAOK,IAAI,CAACO,GAAG,CAACP,IAAI,CAACQ,GAAG,CAACF,GAAG,EAAE,CAAC,CAAC,EAAEH,cAAc,CAAC;AAClD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASM,SAAS,CAChCC,SAAqC,EACrCC,KAA8B,EACxB;EACN;EACA;EACA,IAAMC,CAAC,GAAGC,KAAK;;EAEf;EACA,IAAMC,KAAK,GAAG1B,MAAM,CAACsB,SAAS,CAAC;;EAE/B;EACA,IAAIA,SAAS,IAAI,IAAI,EAAE;IACtB,MAAM,IAAIK,SAAS,CAClB,kEAAkE,CAClE;EACF;;EAEA;EACA;;EAEA,IAAI,OAAOJ,KAAK,KAAK,WAAW,EAAE;IACjC;IACA;IACA,IAAI,CAACpB,UAAU,CAACoB,KAAK,CAAC,EAAE;MACvB,MAAM,IAAII,SAAS,CAClB,mEAAmE,CACnE;IACF;EACD;;EAEA;EACA;EACA,IAAMT,GAAG,GAAGD,QAAQ,CAACS,KAAK,CAACE,MAAM,CAAC;;EAElC;EACA;EACA;EACA;EACA,IAAMC,CAAC,GAAG1B,UAAU,CAACqB,CAAC,CAAC,GAAGxB,MAAM,CAAC,IAAIwB,CAAC,CAACN,GAAG,CAAC,CAAC,GAAG,IAAIO,KAAK,CAACP,GAAG,CAAC;;EAE7D;EACA,IAAIY,CAAC,GAAG,CAAC;EACT;EACA,IAAIC,MAAM;EACV,OAAOD,CAAC,GAAGZ,GAAG,EAAE;IACfa,MAAM,GAAGL,KAAK,CAACI,CAAC,CAAC;IACjB,IAAIP,KAAK,EAAE;MACVM,CAAC,CAACC,CAAC,CAAC,GAAGP,KAAK,CAACQ,MAAM,EAAED,CAAC,CAAC;IACxB,CAAC,MAAM;MACND,CAAC,CAACC,CAAC,CAAC,GAAGC,MAAM;IACd;IACAD,CAAC,IAAI,CAAC;EACP;EACA;EACAD,CAAC,CAACD,MAAM,GAAGV,GAAG;EACd;EACA,OAAOW,CAAC;AACT"}
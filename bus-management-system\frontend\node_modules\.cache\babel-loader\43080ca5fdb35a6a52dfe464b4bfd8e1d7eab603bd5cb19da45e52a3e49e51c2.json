{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Grab = createLucideIcon(\"Grab\", [[\"path\", {\n  d: \"M18 11.5V9a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v1.4\",\n  key: \"n5nng\"\n}], [\"path\", {\n  d: \"M14 10V8a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v2\",\n  key: \"185i9d\"\n}], [\"path\", {\n  d: \"M10 9.9V9a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v5\",\n  key: \"11pz95\"\n}], [\"path\", {\n  d: \"M6 14v0a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0\",\n  key: \"16yk7l\"\n}], [\"path\", {\n  d: \"M18 11v0a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-4a8 8 0 0 1-8-8 2 2 0 1 1 4 0\",\n  key: \"nzvb1c\"\n}]]);\nexport { <PERSON>rab as default };", "map": {"version": 3, "names": ["<PERSON>rab", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\grab.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Grab\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggMTEuNVY5YTIgMiAwIDAgMC0yLTJ2MGEyIDIgMCAwIDAtMiAydjEuNCIgLz4KICA8cGF0aCBkPSJNMTQgMTBWOGEyIDIgMCAwIDAtMi0ydjBhMiAyIDAgMCAwLTIgMnYyIiAvPgogIDxwYXRoIGQ9Ik0xMCA5LjlWOWEyIDIgMCAwIDAtMi0ydjBhMiAyIDAgMCAwLTIgMnY1IiAvPgogIDxwYXRoIGQ9Ik02IDE0djBhMiAyIDAgMCAwLTItMnYwYTIgMiAwIDAgMC0yIDJ2MCIgLz4KICA8cGF0aCBkPSJNMTggMTF2MGEyIDIgMCAxIDEgNCAwdjNhOCA4IDAgMCAxLTggOGgtNGE4IDggMCAwIDEtOC04IDIgMiAwIDEgMSA0IDAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/grab\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Grab = createLucideIcon('Grab', [\n  ['path', { d: 'M18 11.5V9a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v1.4', key: 'n5nng' }],\n  ['path', { d: 'M14 10V8a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v2', key: '185i9d' }],\n  ['path', { d: 'M10 9.9V9a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v5', key: '11pz95' }],\n  ['path', { d: 'M6 14v0a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0', key: '16yk7l' }],\n  [\n    'path',\n    {\n      d: 'M18 11v0a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-4a8 8 0 0 1-8-8 2 2 0 1 1 4 0',\n      key: 'nzvb1c',\n    },\n  ],\n]);\n\nexport default Grab;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAA,CAAS,GAC5E,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
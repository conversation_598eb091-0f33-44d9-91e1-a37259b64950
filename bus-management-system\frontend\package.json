{"name": "bus-management-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "axios": "^1.3.4", "react-query": "^3.39.3", "react-hook-form": "^7.43.9", "react-hot-toast": "^2.4.0", "leaflet": "^1.9.3", "react-leaflet": "^4.2.1", "qrcode.react": "^3.1.0", "html5-qrcode": "^2.3.8", "date-fns": "^2.29.3", "recharts": "^2.5.0", "lucide-react": "^0.263.1", "clsx": "^1.2.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.2.7", "autoprefixer": "^10.4.14", "postcss": "^8.4.21", "@types/leaflet": "^1.9.3"}, "proxy": "http://localhost:5000"}
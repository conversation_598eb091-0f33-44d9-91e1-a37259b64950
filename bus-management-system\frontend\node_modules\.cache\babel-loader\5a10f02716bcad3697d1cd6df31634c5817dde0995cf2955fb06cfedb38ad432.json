{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FlipVertical = createLucideIcon(\"FlipVertical\", [[\"path\", {\n  d: \"M21 8V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3\",\n  key: \"14bfxa\"\n}], [\"path\", {\n  d: \"M21 16v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-3\",\n  key: \"14rx03\"\n}], [\"path\", {\n  d: \"M4 12H2\",\n  key: \"rhcxmi\"\n}], [\"path\", {\n  d: \"M10 12H8\",\n  key: \"s88cx1\"\n}], [\"path\", {\n  d: \"M16 12h-2\",\n  key: \"10asgb\"\n}], [\"path\", {\n  d: \"M22 12h-2\",\n  key: \"14jgyd\"\n}]]);\nexport { FlipVertical as default };", "map": {"version": 3, "names": ["FlipVertical", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\flip-vertical.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FlipVertical\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgOFY1YTIgMiAwIDAgMC0yLTJINWEyIDIgMCAwIDAtMiAydjMiIC8+CiAgPHBhdGggZD0iTTIxIDE2djNhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJ2LTMiIC8+CiAgPHBhdGggZD0iTTQgMTJIMiIgLz4KICA8cGF0aCBkPSJNMTAgMTJIOCIgLz4KICA8cGF0aCBkPSJNMTYgMTJoLTIiIC8+CiAgPHBhdGggZD0iTTIyIDEyaC0yIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/flip-vertical\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FlipVertical = createLucideIcon('FlipVertical', [\n  ['path', { d: 'M21 8V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3', key: '14bfxa' }],\n  ['path', { d: 'M21 16v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-3', key: '14rx03' }],\n  ['path', { d: 'M4 12H2', key: 'rhcxmi' }],\n  ['path', { d: 'M10 12H8', key: 's88cx1' }],\n  ['path', { d: 'M16 12h-2', key: '10asgb' }],\n  ['path', { d: 'M22 12h-2', key: '14jgyd' }],\n]);\n\nexport default FlipVertical;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
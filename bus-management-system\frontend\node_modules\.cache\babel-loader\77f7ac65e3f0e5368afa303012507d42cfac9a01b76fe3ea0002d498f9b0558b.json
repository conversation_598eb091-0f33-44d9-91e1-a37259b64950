{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CloudSun = createLucideIcon(\"CloudSun\", [[\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}], [\"path\", {\n  d: \"m4.93 4.93 1.41 1.41\",\n  key: \"149t6j\"\n}], [\"path\", {\n  d: \"M20 12h2\",\n  key: \"1q8mjw\"\n}], [\"path\", {\n  d: \"m19.07 4.93-1.41 1.41\",\n  key: \"1shlcs\"\n}], [\"path\", {\n  d: \"M15.947 12.65a4 4 0 0 0-5.925-4.128\",\n  key: \"dpwdj0\"\n}], [\"path\", {\n  d: \"M13 22H7a5 5 0 1 1 4.9-6H13a3 3 0 0 1 0 6Z\",\n  key: \"s09mg5\"\n}]]);\nexport { CloudSun as default };", "map": {"version": 3, "names": ["CloudSun", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\cloud-sun.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CloudSun\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnYyIiAvPgogIDxwYXRoIGQ9Im00LjkzIDQuOTMgMS40MSAxLjQxIiAvPgogIDxwYXRoIGQ9Ik0yMCAxMmgyIiAvPgogIDxwYXRoIGQ9Im0xOS4wNyA0LjkzLTEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJNMTUuOTQ3IDEyLjY1YTQgNCAwIDAgMC01LjkyNS00LjEyOCIgLz4KICA8cGF0aCBkPSJNMTMgMjJIN2E1IDUgMCAxIDEgNC45LTZIMTNhMyAzIDAgMCAxIDAgNloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/cloud-sun\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CloudSun = createLucideIcon('CloudSun', [\n  ['path', { d: 'M12 2v2', key: 'tus03m' }],\n  ['path', { d: 'm4.93 4.93 1.41 1.41', key: '149t6j' }],\n  ['path', { d: 'M20 12h2', key: '1q8mjw' }],\n  ['path', { d: 'm19.07 4.93-1.41 1.41', key: '1shlcs' }],\n  ['path', { d: 'M15.947 12.65a4 4 0 0 0-5.925-4.128', key: 'dpwdj0' }],\n  ['path', { d: 'M13 22H7a5 5 0 1 1 4.9-6H13a3 3 0 0 1 0 6Z', key: 's09mg5' }],\n]);\n\nexport default CloudSun;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport LoadingSpinner from './LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRoles = []\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading,\n    user\n  } = useAuth();\n  const location = useLocation();\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 12\n    }, this);\n  }\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check role-based access if required\n  if (requiredRoles.length > 0 && !requiredRoles.includes(user === null || user === void 0 ? void 0 : user.role)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md w-full bg-white shadow-lg rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6 text-red-600\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"mt-2 text-sm font-medium text-gray-900\",\n            children: \"Access Denied\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-500\",\n            children: \"You don't have permission to access this page.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.history.back(),\n              className: \"btn btn-primary\",\n              children: \"Go Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"0B33W4ogs5BUjMlRWEY90S/gYlw=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "LoadingSpinner", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRoles", "_s", "isAuthenticated", "isLoading", "user", "location", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "length", "includes", "role", "className", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "window", "history", "back", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport LoadingSpinner from './LoadingSpinner';\n\nconst ProtectedRoute = ({ children, requiredRoles = [] }) => {\n  const { isAuthenticated, isLoading, user } = useAuth();\n  const location = useLocation();\n\n  if (isLoading) {\n    return <LoadingSpinner />;\n  }\n\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // Check role-based access if required\n  if (requiredRoles.length > 0 && !requiredRoles.includes(user?.role)) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"max-w-md w-full bg-white shadow-lg rounded-lg p-6\">\n          <div className=\"text-center\">\n            <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100\">\n              <svg\n                className=\"h-6 w-6 text-red-600\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                />\n              </svg>\n            </div>\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Access Denied</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              You don't have permission to access this page.\n            </p>\n            <div className=\"mt-6\">\n              <button\n                onClick={() => window.history.back()}\n                className=\"btn btn-primary\"\n              >\n                Go Back\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM;IAAEC,eAAe;IAAEC,SAAS;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EACtD,MAAMW,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,IAAIU,SAAS,EAAE;IACb,oBAAON,OAAA,CAACF,cAAc;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,IAAI,CAACP,eAAe,EAAE;IACpB;IACA,oBAAOL,OAAA,CAACL,QAAQ;MAACkB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAEP;MAAS,CAAE;MAACQ,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAIT,aAAa,CAACc,MAAM,GAAG,CAAC,IAAI,CAACd,aAAa,CAACe,QAAQ,CAACX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,IAAI,CAAC,EAAE;IACnE,oBACEnB,OAAA;MAAKoB,SAAS,EAAC,0DAA0D;MAAAlB,QAAA,eACvEF,OAAA;QAAKoB,SAAS,EAAC,mDAAmD;QAAAlB,QAAA,eAChEF,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAlB,QAAA,gBAC1BF,OAAA;YAAKoB,SAAS,EAAC,4EAA4E;YAAAlB,QAAA,eACzFF,OAAA;cACEoB,SAAS,EAAC,sBAAsB;cAChCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAArB,QAAA,eAErBF,OAAA;gBACEwB,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAA2I;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNZ,OAAA;YAAIoB,SAAS,EAAC,wCAAwC;YAAAlB,QAAA,EAAC;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEZ,OAAA;YAAGoB,SAAS,EAAC,4BAA4B;YAAAlB,QAAA,EAAC;UAE1C;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YAAKoB,SAAS,EAAC,MAAM;YAAAlB,QAAA,eACnBF,OAAA;cACE4B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;cACrCX,SAAS,EAAC,iBAAiB;cAAAlB,QAAA,EAC5B;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOV,QAAQ;AACjB,CAAC;AAACE,EAAA,CArDIH,cAAc;EAAA,QAC2BJ,OAAO,EACnCD,WAAW;AAAA;AAAAoC,EAAA,GAFxB/B,cAAc;AAuDpB,eAAeA,cAAc;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
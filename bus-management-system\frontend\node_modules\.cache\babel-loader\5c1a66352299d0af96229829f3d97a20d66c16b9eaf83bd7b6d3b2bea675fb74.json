{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\components\\\\BusModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useMutation } from 'react-query';\nimport { busesAPI } from '../services/api';\nimport { X, Zap } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BusModal = ({\n  bus,\n  drivers,\n  routes,\n  onClose,\n  onSave\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    bus_number: '',\n    registration_number: '',\n    bus_type: 'Non-AC',\n    capacity: 50,\n    manufacturer: '',\n    model: '',\n    year_of_manufacture: new Date().getFullYear(),\n    fuel_type: 'Diesel',\n    mileage: '',\n    insurance_number: '',\n    insurance_expiry: '',\n    rc_number: '',\n    rc_expiry: '',\n    fitness_certificate_expiry: '',\n    permit_expiry: '',\n    route_id: '',\n    driver_id: '',\n    conductor_id: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [aiSuggestions, setAiSuggestions] = useState(null);\n  useEffect(() => {\n    if (bus) {\n      setFormData({\n        bus_number: bus.bus_number || '',\n        registration_number: bus.registration_number || '',\n        bus_type: bus.bus_type || 'Non-AC',\n        capacity: bus.capacity || 50,\n        manufacturer: bus.manufacturer || '',\n        model: bus.model || '',\n        year_of_manufacture: bus.year_of_manufacture || new Date().getFullYear(),\n        fuel_type: bus.fuel_type || 'Diesel',\n        mileage: bus.mileage || '',\n        insurance_number: bus.insurance_number || '',\n        insurance_expiry: bus.insurance_expiry || '',\n        rc_number: bus.rc_number || '',\n        rc_expiry: bus.rc_expiry || '',\n        fitness_certificate_expiry: bus.fitness_certificate_expiry || '',\n        permit_expiry: bus.permit_expiry || '',\n        route_id: bus.route_id || '',\n        driver_id: bus.driver_id || '',\n        conductor_id: bus.conductor_id || ''\n      });\n    }\n  }, [bus]);\n  const createBusMutation = useMutation(busesAPI.create, {\n    onSuccess: () => {\n      toast.success('Bus created successfully');\n      onSave();\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Failed to create bus');\n    }\n  });\n  const updateBusMutation = useMutation(data => busesAPI.update(bus.id, data), {\n    onSuccess: () => {\n      toast.success('Bus updated successfully');\n      onSave();\n    },\n    onError: error => {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || 'Failed to update bus');\n    }\n  });\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    try {\n      if (bus) {\n        await updateBusMutation.mutateAsync(formData);\n      } else {\n        await createBusMutation.mutateAsync(formData);\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // AI-powered auto-fill from RTO database (simulation)\n  const handleAutoFill = async () => {\n    if (!formData.registration_number) {\n      toast.error('Please enter registration number first');\n      return;\n    }\n    setIsLoading(true);\n\n    // Simulate AI auto-fill from RTO database\n    setTimeout(() => {\n      const suggestions = {\n        manufacturer: 'Tata',\n        model: 'Starbus',\n        year_of_manufacture: 2020,\n        fuel_type: 'Diesel',\n        rc_number: `RC${formData.registration_number.replace(/\\s/g, '')}`,\n        rc_expiry: '2025-12-31',\n        fitness_certificate_expiry: '2024-12-31',\n        permit_expiry: '2024-06-30'\n      };\n      setFormData(prev => ({\n        ...prev,\n        ...suggestions\n      }));\n      setAiSuggestions(suggestions);\n      toast.success('Vehicle details auto-filled from RTO database');\n      setIsLoading(false);\n    }, 2000);\n  };\n\n  // AI-powered driver assignment\n  const getSuggestedDriver = () => {\n    if (!formData.route_id || drivers.length === 0) return null;\n\n    // Simple AI logic: suggest driver based on shift compatibility\n    const availableDrivers = drivers.filter(d => d.status === 'Available');\n    if (availableDrivers.length === 0) return null;\n\n    // Prefer drivers with matching shift type or experience\n    return availableDrivers[0];\n  };\n  const suggestedDriver = getSuggestedDriver();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content max-w-4xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: bus ? 'Edit Bus' : 'Add New Bus'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-400 hover:text-gray-500\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-gray-900\",\n                children: \"Basic Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Bus Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"bus_number\",\n                  value: formData.bus_number,\n                  onChange: handleChange,\n                  className: \"input\",\n                  placeholder: \"Auto-generated if empty\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Registration Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"registration_number\",\n                    value: formData.registration_number,\n                    onChange: handleChange,\n                    className: \"input flex-1\",\n                    required: true,\n                    placeholder: \"e.g., KA01AB1234\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: handleAutoFill,\n                    disabled: isLoading,\n                    className: \"btn btn-outline flex items-center space-x-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Zap, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"AI Fill\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"form-help\",\n                  children: \"AI will auto-fill details from RTO database\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Bus Type *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"bus_type\",\n                    value: formData.bus_type,\n                    onChange: handleChange,\n                    className: \"input\",\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"AC\",\n                      children: \"AC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Non-AC\",\n                      children: \"Non-AC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Sleeper\",\n                      children: \"Sleeper\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Seater\",\n                      children: \"Seater\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Capacity *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"capacity\",\n                    value: formData.capacity,\n                    onChange: handleChange,\n                    className: \"input\",\n                    required: true,\n                    min: \"1\",\n                    max: \"100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Manufacturer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"manufacturer\",\n                    value: formData.manufacturer,\n                    onChange: handleChange,\n                    className: \"input\",\n                    placeholder: \"e.g., Tata, Ashok Leyland\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Model\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"model\",\n                    value: formData.model,\n                    onChange: handleChange,\n                    className: \"input\",\n                    placeholder: \"e.g., Starbus, Viking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Year of Manufacture\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"year_of_manufacture\",\n                    value: formData.year_of_manufacture,\n                    onChange: handleChange,\n                    className: \"input\",\n                    min: \"1990\",\n                    max: new Date().getFullYear() + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Fuel Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"fuel_type\",\n                    value: formData.fuel_type,\n                    onChange: handleChange,\n                    className: \"input\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Diesel\",\n                      children: \"Diesel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 285,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Petrol\",\n                      children: \"Petrol\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"CNG\",\n                      children: \"CNG\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Electric\",\n                      children: \"Electric\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Mileage (km/l)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"mileage\",\n                  value: formData.mileage,\n                  onChange: handleChange,\n                  className: \"input\",\n                  step: \"0.1\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-gray-900\",\n                children: \"Documents & Assignment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Insurance Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"insurance_number\",\n                    value: formData.insurance_number,\n                    onChange: handleChange,\n                    className: \"input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Insurance Expiry\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    name: \"insurance_expiry\",\n                    value: formData.insurance_expiry,\n                    onChange: handleChange,\n                    className: \"input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"RC Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"rc_number\",\n                    value: formData.rc_number,\n                    onChange: handleChange,\n                    className: \"input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"RC Expiry\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    name: \"rc_expiry\",\n                    value: formData.rc_expiry,\n                    onChange: handleChange,\n                    className: \"input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Fitness Certificate Expiry\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    name: \"fitness_certificate_expiry\",\n                    value: formData.fitness_certificate_expiry,\n                    onChange: handleChange,\n                    className: \"input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Permit Expiry\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    name: \"permit_expiry\",\n                    value: formData.permit_expiry,\n                    onChange: handleChange,\n                    className: \"input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Route\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"route_id\",\n                  value: formData.route_id,\n                  onChange: handleChange,\n                  className: \"input\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Route\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this), routes.map(route => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: route.id,\n                    children: [route.route_code, \" - \", route.route_name]\n                  }, route.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Driver\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"driver_id\",\n                  value: formData.driver_id,\n                  onChange: handleChange,\n                  className: \"input\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Driver\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 21\n                  }, this), drivers.filter(d => d.driver_type === 'Driver').map(driver => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: driver.id,\n                    children: [driver.full_name, \" (\", driver.employee_id, \")\"]\n                  }, driver.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), suggestedDriver && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"form-help text-blue-600\",\n                  children: [\"AI Suggestion: \", suggestedDriver.full_name, \" (Best match for this route)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Conductor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"conductor_id\",\n                  value: formData.conductor_id,\n                  onChange: handleChange,\n                  className: \"input\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Conductor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 21\n                  }, this), drivers.filter(d => d.driver_type === 'Conductor').map(conductor => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: conductor.id,\n                    children: [conductor.full_name, \" (\", conductor.employee_id, \")\"]\n                  }, conductor.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), aiSuggestions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"text-sm font-medium text-blue-900 mb-2\",\n              children: \"AI Auto-filled from RTO Database:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-blue-800\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Manufacturer: \", aiSuggestions.manufacturer]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Model: \", aiSuggestions.model]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Year: \", aiSuggestions.year_of_manufacture]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Documents updated with latest information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading,\n            className: \"btn btn-primary\",\n            children: isLoading ? 'Saving...' : bus ? 'Update Bus' : 'Create Bus'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(BusModal, \"Y5whCVzC1eiXBW4uZqBrXKuzYKI=\", false, function () {\n  return [useMutation, useMutation];\n});\n_c = BusModal;\nexport default BusModal;\nvar _c;\n$RefreshReg$(_c, \"BusModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMutation", "busesAPI", "X", "Zap", "toast", "jsxDEV", "_jsxDEV", "BusModal", "bus", "drivers", "routes", "onClose", "onSave", "_s", "formData", "setFormData", "bus_number", "registration_number", "bus_type", "capacity", "manufacturer", "model", "year_of_manufacture", "Date", "getFullYear", "fuel_type", "mileage", "insurance_number", "insurance_expiry", "rc_number", "rc_expiry", "fitness_certificate_expiry", "permit_expiry", "route_id", "driver_id", "conductor_id", "isLoading", "setIsLoading", "aiSuggestions", "setAiSuggestions", "createBusMutation", "create", "onSuccess", "success", "onError", "error", "_error$response", "_error$response$data", "response", "data", "updateBusMutation", "update", "id", "_error$response2", "_error$response2$data", "handleChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "mutateAsync", "handleAutoFill", "setTimeout", "suggestions", "replace", "getSuggestedDriver", "length", "availableDrivers", "filter", "d", "status", "suggestedDriver", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "type", "onChange", "placeholder", "required", "disabled", "min", "max", "step", "map", "route", "route_code", "route_name", "driver_type", "driver", "full_name", "employee_id", "conductor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/components/BusModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useMutation } from 'react-query';\nimport { busesAPI } from '../services/api';\nimport { X, Zap } from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst BusModal = ({ bus, drivers, routes, onClose, onSave }) => {\n  const [formData, setFormData] = useState({\n    bus_number: '',\n    registration_number: '',\n    bus_type: 'Non-AC',\n    capacity: 50,\n    manufacturer: '',\n    model: '',\n    year_of_manufacture: new Date().getFullYear(),\n    fuel_type: 'Diesel',\n    mileage: '',\n    insurance_number: '',\n    insurance_expiry: '',\n    rc_number: '',\n    rc_expiry: '',\n    fitness_certificate_expiry: '',\n    permit_expiry: '',\n    route_id: '',\n    driver_id: '',\n    conductor_id: '',\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [aiSuggestions, setAiSuggestions] = useState(null);\n\n  useEffect(() => {\n    if (bus) {\n      setFormData({\n        bus_number: bus.bus_number || '',\n        registration_number: bus.registration_number || '',\n        bus_type: bus.bus_type || 'Non-AC',\n        capacity: bus.capacity || 50,\n        manufacturer: bus.manufacturer || '',\n        model: bus.model || '',\n        year_of_manufacture: bus.year_of_manufacture || new Date().getFullYear(),\n        fuel_type: bus.fuel_type || 'Diesel',\n        mileage: bus.mileage || '',\n        insurance_number: bus.insurance_number || '',\n        insurance_expiry: bus.insurance_expiry || '',\n        rc_number: bus.rc_number || '',\n        rc_expiry: bus.rc_expiry || '',\n        fitness_certificate_expiry: bus.fitness_certificate_expiry || '',\n        permit_expiry: bus.permit_expiry || '',\n        route_id: bus.route_id || '',\n        driver_id: bus.driver_id || '',\n        conductor_id: bus.conductor_id || '',\n      });\n    }\n  }, [bus]);\n\n  const createBusMutation = useMutation(busesAPI.create, {\n    onSuccess: () => {\n      toast.success('Bus created successfully');\n      onSave();\n    },\n    onError: (error) => {\n      toast.error(error.response?.data?.error || 'Failed to create bus');\n    },\n  });\n\n  const updateBusMutation = useMutation(\n    (data) => busesAPI.update(bus.id, data),\n    {\n      onSuccess: () => {\n        toast.success('Bus updated successfully');\n        onSave();\n      },\n      onError: (error) => {\n        toast.error(error.response?.data?.error || 'Failed to update bus');\n      },\n    }\n  );\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsLoading(true);\n\n    try {\n      if (bus) {\n        await updateBusMutation.mutateAsync(formData);\n      } else {\n        await createBusMutation.mutateAsync(formData);\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // AI-powered auto-fill from RTO database (simulation)\n  const handleAutoFill = async () => {\n    if (!formData.registration_number) {\n      toast.error('Please enter registration number first');\n      return;\n    }\n\n    setIsLoading(true);\n    \n    // Simulate AI auto-fill from RTO database\n    setTimeout(() => {\n      const suggestions = {\n        manufacturer: 'Tata',\n        model: 'Starbus',\n        year_of_manufacture: 2020,\n        fuel_type: 'Diesel',\n        rc_number: `RC${formData.registration_number.replace(/\\s/g, '')}`,\n        rc_expiry: '2025-12-31',\n        fitness_certificate_expiry: '2024-12-31',\n        permit_expiry: '2024-06-30',\n      };\n\n      setFormData(prev => ({\n        ...prev,\n        ...suggestions\n      }));\n\n      setAiSuggestions(suggestions);\n      toast.success('Vehicle details auto-filled from RTO database');\n      setIsLoading(false);\n    }, 2000);\n  };\n\n  // AI-powered driver assignment\n  const getSuggestedDriver = () => {\n    if (!formData.route_id || drivers.length === 0) return null;\n    \n    // Simple AI logic: suggest driver based on shift compatibility\n    const availableDrivers = drivers.filter(d => d.status === 'Available');\n    if (availableDrivers.length === 0) return null;\n    \n    // Prefer drivers with matching shift type or experience\n    return availableDrivers[0];\n  };\n\n  const suggestedDriver = getSuggestedDriver();\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"modal-content max-w-4xl\">\n        <div className=\"modal-header\">\n          <h3 className=\"text-lg font-medium text-gray-900\">\n            {bus ? 'Edit Bus' : 'Add New Bus'}\n          </h3>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-500\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"modal-body\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Basic Information */}\n              <div className=\"space-y-4\">\n                <h4 className=\"text-md font-medium text-gray-900\">Basic Information</h4>\n                \n                <div>\n                  <label className=\"form-label\">Bus Number</label>\n                  <input\n                    type=\"text\"\n                    name=\"bus_number\"\n                    value={formData.bus_number}\n                    onChange={handleChange}\n                    className=\"input\"\n                    placeholder=\"Auto-generated if empty\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Registration Number *</label>\n                  <div className=\"flex space-x-2\">\n                    <input\n                      type=\"text\"\n                      name=\"registration_number\"\n                      value={formData.registration_number}\n                      onChange={handleChange}\n                      className=\"input flex-1\"\n                      required\n                      placeholder=\"e.g., KA01AB1234\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={handleAutoFill}\n                      disabled={isLoading}\n                      className=\"btn btn-outline flex items-center space-x-1\"\n                    >\n                      <Zap className=\"h-4 w-4\" />\n                      <span>AI Fill</span>\n                    </button>\n                  </div>\n                  <p className=\"form-help\">AI will auto-fill details from RTO database</p>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"form-label\">Bus Type *</label>\n                    <select\n                      name=\"bus_type\"\n                      value={formData.bus_type}\n                      onChange={handleChange}\n                      className=\"input\"\n                      required\n                    >\n                      <option value=\"AC\">AC</option>\n                      <option value=\"Non-AC\">Non-AC</option>\n                      <option value=\"Sleeper\">Sleeper</option>\n                      <option value=\"Seater\">Seater</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"form-label\">Capacity *</label>\n                    <input\n                      type=\"number\"\n                      name=\"capacity\"\n                      value={formData.capacity}\n                      onChange={handleChange}\n                      className=\"input\"\n                      required\n                      min=\"1\"\n                      max=\"100\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"form-label\">Manufacturer</label>\n                    <input\n                      type=\"text\"\n                      name=\"manufacturer\"\n                      value={formData.manufacturer}\n                      onChange={handleChange}\n                      className=\"input\"\n                      placeholder=\"e.g., Tata, Ashok Leyland\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"form-label\">Model</label>\n                    <input\n                      type=\"text\"\n                      name=\"model\"\n                      value={formData.model}\n                      onChange={handleChange}\n                      className=\"input\"\n                      placeholder=\"e.g., Starbus, Viking\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"form-label\">Year of Manufacture</label>\n                    <input\n                      type=\"number\"\n                      name=\"year_of_manufacture\"\n                      value={formData.year_of_manufacture}\n                      onChange={handleChange}\n                      className=\"input\"\n                      min=\"1990\"\n                      max={new Date().getFullYear() + 1}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"form-label\">Fuel Type</label>\n                    <select\n                      name=\"fuel_type\"\n                      value={formData.fuel_type}\n                      onChange={handleChange}\n                      className=\"input\"\n                    >\n                      <option value=\"Diesel\">Diesel</option>\n                      <option value=\"Petrol\">Petrol</option>\n                      <option value=\"CNG\">CNG</option>\n                      <option value=\"Electric\">Electric</option>\n                    </select>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Mileage (km/l)</label>\n                  <input\n                    type=\"number\"\n                    name=\"mileage\"\n                    value={formData.mileage}\n                    onChange={handleChange}\n                    className=\"input\"\n                    step=\"0.1\"\n                    min=\"0\"\n                  />\n                </div>\n              </div>\n\n              {/* Documents & Assignment */}\n              <div className=\"space-y-4\">\n                <h4 className=\"text-md font-medium text-gray-900\">Documents & Assignment</h4>\n                \n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"form-label\">Insurance Number</label>\n                    <input\n                      type=\"text\"\n                      name=\"insurance_number\"\n                      value={formData.insurance_number}\n                      onChange={handleChange}\n                      className=\"input\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"form-label\">Insurance Expiry</label>\n                    <input\n                      type=\"date\"\n                      name=\"insurance_expiry\"\n                      value={formData.insurance_expiry}\n                      onChange={handleChange}\n                      className=\"input\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"form-label\">RC Number</label>\n                    <input\n                      type=\"text\"\n                      name=\"rc_number\"\n                      value={formData.rc_number}\n                      onChange={handleChange}\n                      className=\"input\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"form-label\">RC Expiry</label>\n                    <input\n                      type=\"date\"\n                      name=\"rc_expiry\"\n                      value={formData.rc_expiry}\n                      onChange={handleChange}\n                      className=\"input\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"form-label\">Fitness Certificate Expiry</label>\n                    <input\n                      type=\"date\"\n                      name=\"fitness_certificate_expiry\"\n                      value={formData.fitness_certificate_expiry}\n                      onChange={handleChange}\n                      className=\"input\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"form-label\">Permit Expiry</label>\n                    <input\n                      type=\"date\"\n                      name=\"permit_expiry\"\n                      value={formData.permit_expiry}\n                      onChange={handleChange}\n                      className=\"input\"\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Route</label>\n                  <select\n                    name=\"route_id\"\n                    value={formData.route_id}\n                    onChange={handleChange}\n                    className=\"input\"\n                  >\n                    <option value=\"\">Select Route</option>\n                    {routes.map(route => (\n                      <option key={route.id} value={route.id}>\n                        {route.route_code} - {route.route_name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Driver</label>\n                  <select\n                    name=\"driver_id\"\n                    value={formData.driver_id}\n                    onChange={handleChange}\n                    className=\"input\"\n                  >\n                    <option value=\"\">Select Driver</option>\n                    {drivers.filter(d => d.driver_type === 'Driver').map(driver => (\n                      <option key={driver.id} value={driver.id}>\n                        {driver.full_name} ({driver.employee_id})\n                      </option>\n                    ))}\n                  </select>\n                  {suggestedDriver && (\n                    <p className=\"form-help text-blue-600\">\n                      AI Suggestion: {suggestedDriver.full_name} (Best match for this route)\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Conductor</label>\n                  <select\n                    name=\"conductor_id\"\n                    value={formData.conductor_id}\n                    onChange={handleChange}\n                    className=\"input\"\n                  >\n                    <option value=\"\">Select Conductor</option>\n                    {drivers.filter(d => d.driver_type === 'Conductor').map(conductor => (\n                      <option key={conductor.id} value={conductor.id}>\n                        {conductor.full_name} ({conductor.employee_id})\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            {/* AI Suggestions Display */}\n            {aiSuggestions && (\n              <div className=\"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n                <h5 className=\"text-sm font-medium text-blue-900 mb-2\">\n                  AI Auto-filled from RTO Database:\n                </h5>\n                <div className=\"text-sm text-blue-800\">\n                  <p>Manufacturer: {aiSuggestions.manufacturer}</p>\n                  <p>Model: {aiSuggestions.model}</p>\n                  <p>Year: {aiSuggestions.year_of_manufacture}</p>\n                  <p>Documents updated with latest information</p>\n                </div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"modal-footer\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"btn btn-secondary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"btn btn-primary\"\n            >\n              {isLoading ? 'Saving...' : (bus ? 'Update Bus' : 'Create Bus')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default BusModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,CAAC,EAAEC,GAAG,QAAQ,cAAc;AACrC,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,GAAG;EAAEC,OAAO;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,UAAU,EAAE,EAAE;IACdC,mBAAmB,EAAE,EAAE;IACvBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,EAAE;IACTC,mBAAmB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC7CC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,EAAE;IACXC,gBAAgB,EAAE,EAAE;IACpBC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,0BAA0B,EAAE,EAAE;IAC9BC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACd,IAAIS,GAAG,EAAE;MACPO,WAAW,CAAC;QACVC,UAAU,EAAER,GAAG,CAACQ,UAAU,IAAI,EAAE;QAChCC,mBAAmB,EAAET,GAAG,CAACS,mBAAmB,IAAI,EAAE;QAClDC,QAAQ,EAAEV,GAAG,CAACU,QAAQ,IAAI,QAAQ;QAClCC,QAAQ,EAAEX,GAAG,CAACW,QAAQ,IAAI,EAAE;QAC5BC,YAAY,EAAEZ,GAAG,CAACY,YAAY,IAAI,EAAE;QACpCC,KAAK,EAAEb,GAAG,CAACa,KAAK,IAAI,EAAE;QACtBC,mBAAmB,EAAEd,GAAG,CAACc,mBAAmB,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACxEC,SAAS,EAAEjB,GAAG,CAACiB,SAAS,IAAI,QAAQ;QACpCC,OAAO,EAAElB,GAAG,CAACkB,OAAO,IAAI,EAAE;QAC1BC,gBAAgB,EAAEnB,GAAG,CAACmB,gBAAgB,IAAI,EAAE;QAC5CC,gBAAgB,EAAEpB,GAAG,CAACoB,gBAAgB,IAAI,EAAE;QAC5CC,SAAS,EAAErB,GAAG,CAACqB,SAAS,IAAI,EAAE;QAC9BC,SAAS,EAAEtB,GAAG,CAACsB,SAAS,IAAI,EAAE;QAC9BC,0BAA0B,EAAEvB,GAAG,CAACuB,0BAA0B,IAAI,EAAE;QAChEC,aAAa,EAAExB,GAAG,CAACwB,aAAa,IAAI,EAAE;QACtCC,QAAQ,EAAEzB,GAAG,CAACyB,QAAQ,IAAI,EAAE;QAC5BC,SAAS,EAAE1B,GAAG,CAAC0B,SAAS,IAAI,EAAE;QAC9BC,YAAY,EAAE3B,GAAG,CAAC2B,YAAY,IAAI;MACpC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC3B,GAAG,CAAC,CAAC;EAET,MAAMgC,iBAAiB,GAAGxC,WAAW,CAACC,QAAQ,CAACwC,MAAM,EAAE;IACrDC,SAAS,EAAEA,CAAA,KAAM;MACftC,KAAK,CAACuC,OAAO,CAAC,0BAA0B,CAAC;MACzC/B,MAAM,CAAC,CAAC;IACV,CAAC;IACDgC,OAAO,EAAGC,KAAK,IAAK;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MAClB3C,KAAK,CAACyC,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACG,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,KAAI,sBAAsB,CAAC;IACpE;EACF,CAAC,CAAC;EAEF,MAAMK,iBAAiB,GAAGlD,WAAW,CAClCiD,IAAI,IAAKhD,QAAQ,CAACkD,MAAM,CAAC3C,GAAG,CAAC4C,EAAE,EAAEH,IAAI,CAAC,EACvC;IACEP,SAAS,EAAEA,CAAA,KAAM;MACftC,KAAK,CAACuC,OAAO,CAAC,0BAA0B,CAAC;MACzC/B,MAAM,CAAC,CAAC;IACV,CAAC;IACDgC,OAAO,EAAGC,KAAK,IAAK;MAAA,IAAAQ,gBAAA,EAAAC,qBAAA;MAClBlD,KAAK,CAACyC,KAAK,CAAC,EAAAQ,gBAAA,GAAAR,KAAK,CAACG,QAAQ,cAAAK,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBT,KAAK,KAAI,sBAAsB,CAAC;IACpE;EACF,CACF,CAAC;EAED,MAAMU,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC5C,WAAW,CAAC6C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBzB,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,IAAI7B,GAAG,EAAE;QACP,MAAM0C,iBAAiB,CAACa,WAAW,CAACjD,QAAQ,CAAC;MAC/C,CAAC,MAAM;QACL,MAAM0B,iBAAiB,CAACuB,WAAW,CAACjD,QAAQ,CAAC;MAC/C;IACF,CAAC,SAAS;MACRuB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM2B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAClD,QAAQ,CAACG,mBAAmB,EAAE;MACjCb,KAAK,CAACyC,KAAK,CAAC,wCAAwC,CAAC;MACrD;IACF;IAEAR,YAAY,CAAC,IAAI,CAAC;;IAElB;IACA4B,UAAU,CAAC,MAAM;MACf,MAAMC,WAAW,GAAG;QAClB9C,YAAY,EAAE,MAAM;QACpBC,KAAK,EAAE,SAAS;QAChBC,mBAAmB,EAAE,IAAI;QACzBG,SAAS,EAAE,QAAQ;QACnBI,SAAS,EAAE,KAAKf,QAAQ,CAACG,mBAAmB,CAACkD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE;QACjErC,SAAS,EAAE,YAAY;QACvBC,0BAA0B,EAAE,YAAY;QACxCC,aAAa,EAAE;MACjB,CAAC;MAEDjB,WAAW,CAAC6C,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,GAAGM;MACL,CAAC,CAAC,CAAC;MAEH3B,gBAAgB,CAAC2B,WAAW,CAAC;MAC7B9D,KAAK,CAACuC,OAAO,CAAC,+CAA+C,CAAC;MAC9DN,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAM+B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACtD,QAAQ,CAACmB,QAAQ,IAAIxB,OAAO,CAAC4D,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;IAE3D;IACA,MAAMC,gBAAgB,GAAG7D,OAAO,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,WAAW,CAAC;IACtE,IAAIH,gBAAgB,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;IAE9C;IACA,OAAOC,gBAAgB,CAAC,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMI,eAAe,GAAGN,kBAAkB,CAAC,CAAC;EAE5C,oBACE9D,OAAA;IAAKqE,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BtE,OAAA;MAAKqE,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCtE,OAAA;QAAKqE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtE,OAAA;UAAIqE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC9CpE,GAAG,GAAG,UAAU,GAAG;QAAa;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACL1E,OAAA;UACE2E,OAAO,EAAEtE,OAAQ;UACjBgE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CtE,OAAA,CAACJ,CAAC;YAACyE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN1E,OAAA;QAAM4E,QAAQ,EAAErB,YAAa;QAAAe,QAAA,gBAC3BtE,OAAA;UAAKqE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtE,OAAA;YAAKqE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEpDtE,OAAA;cAAKqE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBtE,OAAA;gBAAIqE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAExE1E,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA;kBAAOqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChD1E,OAAA;kBACE6E,IAAI,EAAC,MAAM;kBACX1B,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAE5C,QAAQ,CAACE,UAAW;kBAC3BoE,QAAQ,EAAE7B,YAAa;kBACvBoB,SAAS,EAAC,OAAO;kBACjBU,WAAW,EAAC;gBAAyB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1E,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA;kBAAOqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3D1E,OAAA;kBAAKqE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BtE,OAAA;oBACE6E,IAAI,EAAC,MAAM;oBACX1B,IAAI,EAAC,qBAAqB;oBAC1BC,KAAK,EAAE5C,QAAQ,CAACG,mBAAoB;oBACpCmE,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC,cAAc;oBACxBW,QAAQ;oBACRD,WAAW,EAAC;kBAAkB;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACF1E,OAAA;oBACE6E,IAAI,EAAC,QAAQ;oBACbF,OAAO,EAAEjB,cAAe;oBACxBuB,QAAQ,EAAEnD,SAAU;oBACpBuC,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBAEvDtE,OAAA,CAACH,GAAG;sBAACwE,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3B1E,OAAA;sBAAAsE,QAAA,EAAM;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN1E,OAAA;kBAAGqE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAA2C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eAEN1E,OAAA;gBAAKqE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCtE,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChD1E,OAAA;oBACEmD,IAAI,EAAC,UAAU;oBACfC,KAAK,EAAE5C,QAAQ,CAACI,QAAS;oBACzBkE,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC,OAAO;oBACjBW,QAAQ;oBAAAV,QAAA,gBAERtE,OAAA;sBAAQoD,KAAK,EAAC,IAAI;sBAAAkB,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9B1E,OAAA;sBAAQoD,KAAK,EAAC,QAAQ;sBAAAkB,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC1E,OAAA;sBAAQoD,KAAK,EAAC,SAAS;sBAAAkB,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC1E,OAAA;sBAAQoD,KAAK,EAAC,QAAQ;sBAAAkB,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN1E,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChD1E,OAAA;oBACE6E,IAAI,EAAC,QAAQ;oBACb1B,IAAI,EAAC,UAAU;oBACfC,KAAK,EAAE5C,QAAQ,CAACK,QAAS;oBACzBiE,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC,OAAO;oBACjBW,QAAQ;oBACRE,GAAG,EAAC,GAAG;oBACPC,GAAG,EAAC;kBAAK;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1E,OAAA;gBAAKqE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCtE,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClD1E,OAAA;oBACE6E,IAAI,EAAC,MAAM;oBACX1B,IAAI,EAAC,cAAc;oBACnBC,KAAK,EAAE5C,QAAQ,CAACM,YAAa;oBAC7BgE,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC,OAAO;oBACjBU,WAAW,EAAC;kBAA2B;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1E,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3C1E,OAAA;oBACE6E,IAAI,EAAC,MAAM;oBACX1B,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAE5C,QAAQ,CAACO,KAAM;oBACtB+D,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC,OAAO;oBACjBU,WAAW,EAAC;kBAAuB;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1E,OAAA;gBAAKqE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCtE,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzD1E,OAAA;oBACE6E,IAAI,EAAC,QAAQ;oBACb1B,IAAI,EAAC,qBAAqB;oBAC1BC,KAAK,EAAE5C,QAAQ,CAACQ,mBAAoB;oBACpC8D,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC,OAAO;oBACjBa,GAAG,EAAC,MAAM;oBACVC,GAAG,EAAE,IAAIlE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;kBAAE;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1E,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/C1E,OAAA;oBACEmD,IAAI,EAAC,WAAW;oBAChBC,KAAK,EAAE5C,QAAQ,CAACW,SAAU;oBAC1B2D,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC,OAAO;oBAAAC,QAAA,gBAEjBtE,OAAA;sBAAQoD,KAAK,EAAC,QAAQ;sBAAAkB,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC1E,OAAA;sBAAQoD,KAAK,EAAC,QAAQ;sBAAAkB,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC1E,OAAA;sBAAQoD,KAAK,EAAC,KAAK;sBAAAkB,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChC1E,OAAA;sBAAQoD,KAAK,EAAC,UAAU;sBAAAkB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1E,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA;kBAAOqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpD1E,OAAA;kBACE6E,IAAI,EAAC,QAAQ;kBACb1B,IAAI,EAAC,SAAS;kBACdC,KAAK,EAAE5C,QAAQ,CAACY,OAAQ;kBACxB0D,QAAQ,EAAE7B,YAAa;kBACvBoB,SAAS,EAAC,OAAO;kBACjBe,IAAI,EAAC,KAAK;kBACVF,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1E,OAAA;cAAKqE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBtE,OAAA;gBAAIqE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE7E1E,OAAA;gBAAKqE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCtE,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtD1E,OAAA;oBACE6E,IAAI,EAAC,MAAM;oBACX1B,IAAI,EAAC,kBAAkB;oBACvBC,KAAK,EAAE5C,QAAQ,CAACa,gBAAiB;oBACjCyD,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1E,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtD1E,OAAA;oBACE6E,IAAI,EAAC,MAAM;oBACX1B,IAAI,EAAC,kBAAkB;oBACvBC,KAAK,EAAE5C,QAAQ,CAACc,gBAAiB;oBACjCwD,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1E,OAAA;gBAAKqE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCtE,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/C1E,OAAA;oBACE6E,IAAI,EAAC,MAAM;oBACX1B,IAAI,EAAC,WAAW;oBAChBC,KAAK,EAAE5C,QAAQ,CAACe,SAAU;oBAC1BuD,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1E,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/C1E,OAAA;oBACE6E,IAAI,EAAC,MAAM;oBACX1B,IAAI,EAAC,WAAW;oBAChBC,KAAK,EAAE5C,QAAQ,CAACgB,SAAU;oBAC1BsD,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1E,OAAA;gBAAKqE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCtE,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChE1E,OAAA;oBACE6E,IAAI,EAAC,MAAM;oBACX1B,IAAI,EAAC,4BAA4B;oBACjCC,KAAK,EAAE5C,QAAQ,CAACiB,0BAA2B;oBAC3CqD,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1E,OAAA;kBAAAsE,QAAA,gBACEtE,OAAA;oBAAOqE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnD1E,OAAA;oBACE6E,IAAI,EAAC,MAAM;oBACX1B,IAAI,EAAC,eAAe;oBACpBC,KAAK,EAAE5C,QAAQ,CAACkB,aAAc;oBAC9BoD,QAAQ,EAAE7B,YAAa;oBACvBoB,SAAS,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1E,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA;kBAAOqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3C1E,OAAA;kBACEmD,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAE5C,QAAQ,CAACmB,QAAS;kBACzBmD,QAAQ,EAAE7B,YAAa;kBACvBoB,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBAEjBtE,OAAA;oBAAQoD,KAAK,EAAC,EAAE;oBAAAkB,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACrCtE,MAAM,CAACiF,GAAG,CAACC,KAAK,iBACftF,OAAA;oBAAuBoD,KAAK,EAAEkC,KAAK,CAACxC,EAAG;oBAAAwB,QAAA,GACpCgB,KAAK,CAACC,UAAU,EAAC,KAAG,EAACD,KAAK,CAACE,UAAU;kBAAA,GAD3BF,KAAK,CAACxC,EAAE;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN1E,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA;kBAAOqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5C1E,OAAA;kBACEmD,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAE5C,QAAQ,CAACoB,SAAU;kBAC1BkD,QAAQ,EAAE7B,YAAa;kBACvBoB,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBAEjBtE,OAAA;oBAAQoD,KAAK,EAAC,EAAE;oBAAAkB,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACtCvE,OAAO,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACuB,WAAW,KAAK,QAAQ,CAAC,CAACJ,GAAG,CAACK,MAAM,iBACzD1F,OAAA;oBAAwBoD,KAAK,EAAEsC,MAAM,CAAC5C,EAAG;oBAAAwB,QAAA,GACtCoB,MAAM,CAACC,SAAS,EAAC,IAAE,EAACD,MAAM,CAACE,WAAW,EAAC,GAC1C;kBAAA,GAFaF,MAAM,CAAC5C,EAAE;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRN,eAAe,iBACdpE,OAAA;kBAAGqE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GAAC,iBACtB,EAACF,eAAe,CAACuB,SAAS,EAAC,8BAC5C;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1E,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA;kBAAOqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/C1E,OAAA;kBACEmD,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAE5C,QAAQ,CAACqB,YAAa;kBAC7BiD,QAAQ,EAAE7B,YAAa;kBACvBoB,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBAEjBtE,OAAA;oBAAQoD,KAAK,EAAC,EAAE;oBAAAkB,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACzCvE,OAAO,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACuB,WAAW,KAAK,WAAW,CAAC,CAACJ,GAAG,CAACQ,SAAS,iBAC/D7F,OAAA;oBAA2BoD,KAAK,EAAEyC,SAAS,CAAC/C,EAAG;oBAAAwB,QAAA,GAC5CuB,SAAS,CAACF,SAAS,EAAC,IAAE,EAACE,SAAS,CAACD,WAAW,EAAC,GAChD;kBAAA,GAFaC,SAAS,CAAC/C,EAAE;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL1C,aAAa,iBACZhC,OAAA;YAAKqE,SAAS,EAAC,uDAAuD;YAAAC,QAAA,gBACpEtE,OAAA;cAAIqE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAEvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1E,OAAA;cAAKqE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCtE,OAAA;gBAAAsE,QAAA,GAAG,gBAAc,EAACtC,aAAa,CAAClB,YAAY;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjD1E,OAAA;gBAAAsE,QAAA,GAAG,SAAO,EAACtC,aAAa,CAACjB,KAAK;cAAA;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnC1E,OAAA;gBAAAsE,QAAA,GAAG,QAAM,EAACtC,aAAa,CAAChB,mBAAmB;cAAA;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChD1E,OAAA;gBAAAsE,QAAA,EAAG;cAAyC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1E,OAAA;UAAKqE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtE,OAAA;YACE6E,IAAI,EAAC,QAAQ;YACbF,OAAO,EAAEtE,OAAQ;YACjBgE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC9B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1E,OAAA;YACE6E,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAEnD,SAAU;YACpBuC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAE1BxC,SAAS,GAAG,WAAW,GAAI5B,GAAG,GAAG,YAAY,GAAG;UAAa;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CAndIN,QAAQ;EAAA,QAiDcP,WAAW,EAUXA,WAAW;AAAA;AAAAoG,EAAA,GA3DjC7F,QAAQ;AAqdd,eAAeA,QAAQ;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
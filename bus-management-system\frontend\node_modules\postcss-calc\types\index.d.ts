export = pluginCreator;
/**
 * @typedef {{precision?: number | false,
 *          preserve?: boolean,
 *          warnWhenCannotResolve?: boolean,
 *          mediaQueries?: boolean,
 *          selectors?: boolean}} PostCssCalcOptions
 */
/**
 * @type {import('postcss').PluginCreator<PostCssCalcOptions>}
 * @param {PostCssCalcOptions} opts
 * @return {import('postcss').Plugin}
 */
declare function pluginCreator(opts: PostCssCalcOptions): import('postcss').Plugin;
declare namespace pluginCreator {
    export { postcss, PostCssCalcOptions };
}
type PostCssCalcOptions = {
    precision?: number | false;
    preserve?: boolean;
    warnWhenCannotResolve?: boolean;
    mediaQueries?: boolean;
    selectors?: boolean;
};
declare var postcss: true;

{"name": "workbox-navigation-preload", "version": "6.6.0", "license": "MIT", "author": "Google's Web DevRel Team", "description": "This library allows developers to opt-in to using Navigation Preload in their service worker.", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "navigation"], "workbox": {"browserNamespace": "workbox.navigationPreload", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"workbox-core": "6.6.0"}, "gitHead": "252644491d9bb5a67518935ede6df530107c9475"}
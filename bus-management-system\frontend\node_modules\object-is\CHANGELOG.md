# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.6](https://github.com/es-shims/object-is/compare/v1.1.5...v1.1.6) - 2024-02-27

### Commits

- [actions] reuse common workflows [`f8d0c06`](https://github.com/es-shims/object-is/commit/f8d0c0617901b4b220f2bbbaff8c9ec7f22891c6)
- [actions] use `node/install` instead of `node/run` [`90fc019`](https://github.com/es-shims/object-is/commit/90fc01955dc677fef745f0dc4164e2144b047997)
- [actions] update workflows [`37339d8`](https://github.com/es-shims/object-is/commit/37339d870aacad9f524cb685c0075aed78e9bd6e)
- [meta] use `npmignore` to autogenerate an npmignore file [`a257c19`](https://github.com/es-shims/object-is/commit/a257c19d55c275ac44892b1d2d98a5d06e976943)
- [readme] fix badges [`50af053`](https://github.com/es-shims/object-is/commit/50af053d4d8ee02657d71f784c31b372905187a7)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`86058c6`](https://github.com/es-shims/object-is/commit/86058c65153dea90d18ff969244bdc610f382046)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `functions-have-names`, `has-symbols`, `tape` [`d5484eb`](https://github.com/es-shims/object-is/commit/d5484eb02a44482de29ce95ccfd01e885d0ff83b)
- [actions] update rebase action to use reusable workflow [`68075c5`](https://github.com/es-shims/object-is/commit/68075c5bd0e35b51e815e7fd95bb90cc54deb699)
- [actions] update codecov uploader [`f12fbb2`](https://github.com/es-shims/object-is/commit/f12fbb2f23b43c2d982fd00c95ff98b01e8747a1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `has-symbols`, `tape` [`43eb235`](https://github.com/es-shims/object-is/commit/43eb235ffa1dd258ff5ef990fd2f0ce78ce40ccc)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `npmignore`, `tape` [`f306966`](https://github.com/es-shims/object-is/commit/f306966e53fac2e24dd11dfe6cf31a8ae20a08da)
- [readme] add actions and codecov badges [`e443b4a`](https://github.com/es-shims/object-is/commit/e443b4a3d0b01683cb6fec59916ad4ec463e9de7)
- [readme] remove travis badge [`5165adc`](https://github.com/es-shims/object-is/commit/5165adccf9e8373f38a01e7a5887b42a4229ca2b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`e7ccf56`](https://github.com/es-shims/object-is/commit/e7ccf56f84434f6671fae1d1fe4945f5b3a84120)
- [Deps] update `call-bind`, `define-properties` [`a3052db`](https://github.com/es-shims/object-is/commit/a3052db9e272c5d544553a7f43c916689e8cca9a)
- [readme] fix URLs [`ac37ea6`](https://github.com/es-shims/object-is/commit/ac37ea68b2a11ef7ebff694e6c4f308c735240ea)
- [readme] assert.notOk -&gt; assert.equal [`7fe769f`](https://github.com/es-shims/object-is/commit/7fe769fc8293d8756ed2d31c4d12afc0581498b7)
- [Deps] update `call-bind` [`b965cd3`](https://github.com/es-shims/object-is/commit/b965cd319e1c397a5ef7dc6dffc9a5bd4329d408)
- [Dev Deps] update `tape` [`a737830`](https://github.com/es-shims/object-is/commit/a737830a051cdc650c3bfb78b085586c9fd7cd13)
- [Deps] update `define-properties`, `es-abstract` [`441eafb`](https://github.com/es-shims/object-is/commit/441eafbca4e32d76dc16bea6b73ee794296e2c0c)

## [v1.1.5](https://github.com/es-shims/object-is/compare/v1.1.4...v1.1.5) - 2021-02-20

### Commits

- [meta] do not publish github action workflow files [`ec00b0f`](https://github.com/es-shims/object-is/commit/ec00b0f3a5096a7d57884e201c76ded3052a1b9c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`279645e`](https://github.com/es-shims/object-is/commit/279645e330da410da9776455274aa6b3c89b585b)
- [actions] update workflows [`6b4ef5a`](https://github.com/es-shims/object-is/commit/6b4ef5a219fc3982433fc3df2ad9d57994be0761)
- [Dev Deps] update `eslint`, `functions-have-names`, `tape` [`f5fd32a`](https://github.com/es-shims/object-is/commit/f5fd32ace61d4643fe0bf67ca83a6674a65906be)
- [Deps] update `call-bind` [`0fafc13`](https://github.com/es-shims/object-is/commit/0fafc13ec1a20d3075512ae9d2c0b0ee252169a9)
- [Deps] update `call-bind` [`e28a929`](https://github.com/es-shims/object-is/commit/e28a929b71fc1939a4c4d5c92ca0c04a0b27ff81)
- [readme] Add note about es-shim API [`f903cc1`](https://github.com/es-shims/object-is/commit/f903cc11186e0d5c6c4173821d6b7bf2a49f6c01)

## [v1.1.4](https://github.com/es-shims/object-is/compare/v1.1.3...v1.1.4) - 2020-11-26

### Commits

- [Tests] migrate tests to Github Actions [`958ab26`](https://github.com/es-shims/object-is/commit/958ab266fd68396781c076d8a5ee4ba292561362)
- [Tests] add `shimmed` and `implementation` and `index` tests; run `es-shim-api` in postlint; use `tape` runner [`b918fb8`](https://github.com/es-shims/object-is/commit/b918fb849023032d2da61ead95f31b0a03371131)
- [Tests] run `nyc` on all tests [`8f62816`](https://github.com/es-shims/object-is/commit/8f6281683ad58ffe9b5809c2a9e7bb65db344c9c)
- [actions] add "Allow Edits" workflow [`aa419f0`](https://github.com/es-shims/object-is/commit/aa419f0ea2b497844365f9f51a746fa2a57bb6ee)
- [Deps] use `call-bind` instead of `es-abstract` [`4991728`](https://github.com/es-shims/object-is/commit/49917288eddfce31949f5a3351f0e0bb67929a2b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud` [`3ce2ef5`](https://github.com/es-shims/object-is/commit/3ce2ef5e834bf22566ea5741178cd76bb35f8a89)
- [meta] ignore coverage output [`d778383`](https://github.com/es-shims/object-is/commit/d778383fde9222bc5349dd4adcaab9f5ef10254e)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`e0d9b41`](https://github.com/es-shims/object-is/commit/e0d9b41a73f51f1c8b9d9b402da5f754926bc280)

## [v1.1.3](https://github.com/es-shims/object-is/compare/v1.1.2...v1.1.3) - 2020-09-30

### Commits

- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`6f1217f`](https://github.com/es-shims/object-is/commit/6f1217fc82cbe25d3911cad2d3298a8f3f51bd7f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog` [`68d8ab0`](https://github.com/es-shims/object-is/commit/68d8ab07275949aa78f20f0e6270c0a26aba2647)
- [Deps] update `es-abstract` [`d665d57`](https://github.com/es-shims/object-is/commit/d665d570e416f039bbbc577f0c2c77104302d227)
- [Deps] update `es-abstract` [`61b4d08`](https://github.com/es-shims/object-is/commit/61b4d0893212b08ec89ba8c388948fa4377f7a43)

## [v1.1.2](https://github.com/es-shims/object-is/compare/v1.1.1...v1.1.2) - 2020-04-14

### Commits

- [Fix] avoid mutating the builtin `Object.is` in the main entry point [`5988702`](https://github.com/es-shims/object-is/commit/59887020544021d7cf8e72cd84c4d67abcf558c5)

## [v1.1.1](https://github.com/es-shims/object-is/compare/v1.1.0...v1.1.1) - 2020-04-14

### Fixed

- [Deps] add missing `define-properties` [`#10`](https://github.com/es-shims/object-is/issues/10)

## [v1.1.0](https://github.com/es-shims/object-is/compare/v1.0.2...v1.1.0) - 2020-04-14

### Commits

- [New] convert to `es-shim-api` interface [`c8b6e9f`](https://github.com/es-shims/object-is/commit/c8b6e9f249438bfd9dfa315415eddd3a1d436d15)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `safe-publish-latest` [`222a2a9`](https://github.com/es-shims/object-is/commit/222a2a9a2eb08be844bf4c619d1eb711d743c6f3)
- [Dev Deps] update `auto-changelog`, `tape` [`1ea6a92`](https://github.com/es-shims/object-is/commit/1ea6a92153695074c4e3f2e2e0ec26b83f6b091a)
- [Dev Deps] update `auto-changelog`; add `aud` [`1d129e0`](https://github.com/es-shims/object-is/commit/1d129e0dfe386a1e39fa4f3ff991198e885299b1)
- [Tests] only audit prod deps [`ad12386`](https://github.com/es-shims/object-is/commit/ad1238688dcfe9170217b6b9a02122369979d221)

## [v1.0.2](https://github.com/es-shims/object-is/compare/v1.0.1...v1.0.2) - 2019-12-15

### Commits

- [Tests] use shared travis-ci configs [`eb56ddf`](https://github.com/es-shims/object-is/commit/eb56ddf82bd08f56bd50db7333f2a7b6cef2452a)
- [Tests] up to `node` `v12.6`, `v11.15`, `v10.16`, `v9.11`, `v8.16`, `v7.10`, `v6.17`, `v5.12`, `4.9`; use `nvm install-latest-npm` [`a5bb4e3`](https://github.com/es-shims/object-is/commit/a5bb4e3663902dda4eb9c748aecd04532428594a)
- [Tests] remove `jscs` [`1929690`](https://github.com/es-shims/object-is/commit/19296907bbb9864518ccb2364a44f9adda9c910c)
- Using my standard jscs.json file. [`a7621dc`](https://github.com/es-shims/object-is/commit/a7621dcafbdf4befa4bb97c4e132f30fd486addf)
- [meta] run `auto-changelog` [`5c551c4`](https://github.com/es-shims/object-is/commit/5c551c406f8a72d53f8f4bbabc89f0c1892bf262)
- Update `tape`, `covert`, `jscs` [`bd84112`](https://github.com/es-shims/object-is/commit/bd8411263e754f5a464b6d55c01ecd3b1f4c7437)
- [meta] move repo to es-shims org [`15d3cdc`](https://github.com/es-shims/object-is/commit/15d3cdcbd4b78c8dee80507ac8a5f4cff7ab42d4)
- Test up to `io.js` `v2.2` [`d1d2de4`](https://github.com/es-shims/object-is/commit/d1d2de48f72853552e3d3a751b3f178c5dc10ef5)
- Update `tape`, `jscs` [`b40e85f`](https://github.com/es-shims/object-is/commit/b40e85f77cc906fc54246cf672a78b902bd65aab)
- Add `npm run eslint` [`a80ee81`](https://github.com/es-shims/object-is/commit/a80ee81d12d8fb4120bf590c4d4622fb5dc3f67b)
- Only apps should have lockfiles [`f70677a`](https://github.com/es-shims/object-is/commit/f70677adbe0a765ee26e214a95a97d0ddf88f31e)
- [actions] add automatic rebasing / merge commit blocking [`27780d3`](https://github.com/es-shims/object-is/commit/27780d330cbaf3ef96479b5a0a3fe55f71b54bb6)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`cf886ce`](https://github.com/es-shims/object-is/commit/cf886ced70afff6c5a66be6f9ddba2d330034c70)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `has-symbols` [`b70e146`](https://github.com/es-shims/object-is/commit/b70e146496488ac5e1a48651cb94292e67051e48)
- [meta] add version scripts [`25a5308`](https://github.com/es-shims/object-is/commit/25a5308bfcc41733a86ce0461ef69a2459215b5b)
- [Tests] up to `node` `v12.10` [`b6e934c`](https://github.com/es-shims/object-is/commit/b6e934ca8f0e65dac7ed3c30b2b7907d39e36f6b)
- [Dev Deps] update `tape`, `jscs` [`df38b5a`](https://github.com/es-shims/object-is/commit/df38b5a856071aaa4d6b8e089a48d80775cb1ed9)
- Adding license and downloads badges [`c743f09`](https://github.com/es-shims/object-is/commit/c743f0907b0bab73a0821541190a865a5c3e5da9)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape` [`41d1c5b`](https://github.com/es-shims/object-is/commit/41d1c5b8173f6543e01940b18daee93c586058d0)
- Add tests for Symbols. [`8189ca7`](https://github.com/es-shims/object-is/commit/8189ca73b647a13f63162bc8ed14040386972530)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`698c449`](https://github.com/es-shims/object-is/commit/698c449a8fdc6634747765333cdd8071119932b8)
- Add `npm run security` [`1fe530a`](https://github.com/es-shims/object-is/commit/1fe530abd3164eb70dedd959a25f3b1ad0d02c01)
- Test up to `io.js` `v3.0` [`cd6ac03`](https://github.com/es-shims/object-is/commit/cd6ac034cda66916319c68aeea190e5817de106c)
- [Refactor] no-else-return [`a590382`](https://github.com/es-shims/object-is/commit/a590382d69134915f37039ae6841c9fffbdd5b81)
- [docs] clean up some links [`422df90`](https://github.com/es-shims/object-is/commit/422df90523f4d7f2463e404d394f656232f0fc7c)
- All grade A-supported `node`/`iojs` versions now ship with an `npm` that understands `^`. [`8684418`](https://github.com/es-shims/object-is/commit/8684418131004859fe18443d4f57f28510b72e13)
- Run `travis-ci` tests on `iojs` and `node` v0.12; speed up builds; allow 0.8 failures. [`6b618ac`](https://github.com/es-shims/object-is/commit/6b618acfa5bcab8d1684d926728000afb4f1ee52)
- [readme] prefer ES2015 over ES6 [`9aae9f9`](https://github.com/es-shims/object-is/commit/9aae9f9d1b4e699364f4822534b89887abb9089f)
- [meta] use keepachangelog with auto-changelog [`3abdcd9`](https://github.com/es-shims/object-is/commit/3abdcd915bc647a5f01af2422fa4a37b2980f946)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`65da8bb`](https://github.com/es-shims/object-is/commit/65da8bba76d4b0e95f95d2a6b98d7bfe5b294ff0)
- [Tests] use `has-symbols` [`9c88ec7`](https://github.com/es-shims/object-is/commit/9c88ec729227f35dc18fe52131c11afd296a042e)
- Update `tape`, `jscs` [`abc6021`](https://github.com/es-shims/object-is/commit/abc6021a0be38921688182e0f95376597056f6cd)
- Update `jscs` [`f85f0ba`](https://github.com/es-shims/object-is/commit/f85f0ba49809030c09dfda4af0022c4122438f90)
- Update `tape`, `jscs` [`9c8b28b`](https://github.com/es-shims/object-is/commit/9c8b28bf52521b70fdacdaba1035e101aa0a1240)
- Using single quotes. [`46a566e`](https://github.com/es-shims/object-is/commit/46a566efbc6366aa44ef9ac8b47668ede5830d50)
- [meta] add `funding` field [`f71e665`](https://github.com/es-shims/object-is/commit/f71e66510676f9bfeb6e81a05821161953879270)
- [Dev Deps] update `tape` [`694a94e`](https://github.com/es-shims/object-is/commit/694a94e83b4d415b1e43adb8011ad33351945105)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`4b29a71`](https://github.com/es-shims/object-is/commit/4b29a718b459ecde3512c4dd6577a2a5e95b7cc6)
- Update `jscs` [`204e6fe`](https://github.com/es-shims/object-is/commit/204e6febc368ca94091ac0b5ec0254c33b0ee462)
- Lock covert to v1.0.0. [`efc5a21`](https://github.com/es-shims/object-is/commit/efc5a21e1d0cc463ec7c7ef2f75a317085093d5e)
- Updating tape [`d1ff721`](https://github.com/es-shims/object-is/commit/d1ff721d2bdec538112cdbbfedcf06d578093831)
- Updating jscs [`35b4df9`](https://github.com/es-shims/object-is/commit/35b4df90d0c97a4f004f0188940b2e602b645c16)
- Updating jscs [`46c3b9d`](https://github.com/es-shims/object-is/commit/46c3b9d31a7f8ce43c496e0304b22ce142311917)
- Updating jscs [`75c9b8b`](https://github.com/es-shims/object-is/commit/75c9b8b45022d5e9b44b0c9a055974185b550a57)

## [v1.0.1](https://github.com/es-shims/object-is/compare/v1.0.0...v1.0.1) - 2014-08-27

### Merged

- Use svg instead of png to get better image quality [`#2`](https://github.com/es-shims/object-is/pull/2)

### Commits

- Adding `npm run lint` [`af5dedd`](https://github.com/es-shims/object-is/commit/af5dedd2f40df7584c856576123a2d2852dd9694)
- Using an easier isNaN check [`89474ae`](https://github.com/es-shims/object-is/commit/89474ae3e26eb857d89b01c1f3b20f859bd0f161)
- Oops, run quiet coverage as part of tests [`5149e18`](https://github.com/es-shims/object-is/commit/5149e1876808e67a016fd913f6d99481bf7091f2)
- Run linter as part of tests [`d5eee8a`](https://github.com/es-shims/object-is/commit/d5eee8a43b95c619cf3b06ef3d64cefc865f33f3)
- Updating covert [`0b862da`](https://github.com/es-shims/object-is/commit/0b862dad7873b9aab74d2f4262b5b587120c169b)
- Updating tape [`484e6ca`](https://github.com/es-shims/object-is/commit/484e6cab0d5734b8c9f23ceada58e93b09dc15ee)

## [v1.0.0](https://github.com/es-shims/object-is/compare/v0.0.0...v1.0.0) - 2014-08-01

### Commits

- Make sure old and unstable nodes don't break Travis [`c8d3dfe`](https://github.com/es-shims/object-is/commit/c8d3dfe4c0f6dc76a5b0cc22a6a6401fa1105cea)
- Bumping to v1.0.0. [`8811835`](https://github.com/es-shims/object-is/commit/8811835bff203cf0dc0dee1342beeb749ea63e10)
- Updating tape [`0767579`](https://github.com/es-shims/object-is/commit/0767579b3cbf59e49c73c131186bfcbad4448020)
- Updating covert [`6b67a0b`](https://github.com/es-shims/object-is/commit/6b67a0b4b6933ea23c74c24037f3f515942a005a)
- Run code coverage in tests [`8b5d70d`](https://github.com/es-shims/object-is/commit/8b5d70d9cbf6194d69ee22b8433fe4e0a3d7507e)
- Updating tape [`57b019c`](https://github.com/es-shims/object-is/commit/57b019c8b74030601dc71dc38cd2c41cf5b735d4)
- Updating tape [`91d94f9`](https://github.com/es-shims/object-is/commit/91d94f9b06c4a86942b077979536a8c2994ab374)

## v0.0.0 - 2014-02-17

### Commits

- package.json [`b724e50`](https://github.com/es-shims/object-is/commit/b724e50ea4a151f46ff5344f9dc3f00d48e60695)
- read me [`62470f5`](https://github.com/es-shims/object-is/commit/62470f58dbef4ba0b96c6c000b2802e328c54be6)
- Tests. [`639e212`](https://github.com/es-shims/object-is/commit/639e212c478afcb8c9a24753aa8fc2b2fdcfb925)
- Initial commit [`8e95b37`](https://github.com/es-shims/object-is/commit/8e95b3744c07594c49372c5732d96235f0b7d9d6)
- Implementation. [`47780bc`](https://github.com/es-shims/object-is/commit/47780bc0f483eeabd853877fa33295976cf201ae)
- Travis CI [`baaf8c7`](https://github.com/es-shims/object-is/commit/baaf8c70bc7fd88cf149af9b0c992febb5e89514)
- Covert is broken in node 0.6 [`ee040b2`](https://github.com/es-shims/object-is/commit/ee040b2f3a917da9a33a287daf8470e95db271e1)

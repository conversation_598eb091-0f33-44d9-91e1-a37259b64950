'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
exports.remove = exports.print = void 0;

function _chalk() {
  const data = _interopRequireDefault(require('chalk'));

  _chalk = function () {
    return data;
  };

  return data;
}

var _clearLine = _interopRequireDefault(require('./clearLine'));

var _isInteractive = _interopRequireDefault(require('./isInteractive'));

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {default: obj};
}

/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
const print = stream => {
  if (_isInteractive.default) {
    stream.write(
      _chalk().default.bold.dim('Determining test suites to run...')
    );
  }
};

exports.print = print;

const remove = stream => {
  if (_isInteractive.default) {
    (0, _clearLine.default)(stream);
  }
};

exports.remove = remove;

{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Car = createLucideIcon(\"Car\", [[\"path\", {\n  d: \"M14 16H9m10 0h3v-3.15a1 1 0 0 0-.84-.99L16 11l-2.7-3.6a1 1 0 0 0-.8-.4H5.24a2 2 0 0 0-1.8 1.1l-.8 1.63A6 6 0 0 0 2 12.42V16h2\",\n  key: \"l5np60\"\n}], [\"circle\", {\n  cx: \"6.5\",\n  cy: \"16.5\",\n  r: \"2.5\",\n  key: \"ae40ju\"\n}], [\"circle\", {\n  cx: \"16.5\",\n  cy: \"16.5\",\n  r: \"2.5\",\n  key: \"1smtlt\"\n}]]);\nexport { Car as default };", "map": {"version": 3, "names": ["Car", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\car.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Car\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTZIOW0xMCAwaDN2LTMuMTVhMSAxIDAgMCAwLS44NC0uOTlMMTYgMTFsLTIuNy0zLjZhMSAxIDAgMCAwLS44LS40SDUuMjRhMiAyIDAgMCAwLTEuOCAxLjFsLS44IDEuNjNBNiA2IDAgMCAwIDIgMTIuNDJWMTZoMiIgLz4KICA8Y2lyY2xlIGN4PSI2LjUiIGN5PSIxNi41IiByPSIyLjUiIC8+CiAgPGNpcmNsZSBjeD0iMTYuNSIgY3k9IjE2LjUiIHI9IjIuNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/car\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Car = createLucideIcon('Car', [\n  [\n    'path',\n    {\n      d: 'M14 16H9m10 0h3v-3.15a1 1 0 0 0-.84-.99L16 11l-2.7-3.6a1 1 0 0 0-.8-.4H5.24a2 2 0 0 0-1.8 1.1l-.8 1.63A6 6 0 0 0 2 12.42V16h2',\n      key: 'l5np60',\n    },\n  ],\n  ['circle', { cx: '6.5', cy: '16.5', r: '2.5', key: 'ae40ju' }],\n  ['circle', { cx: '16.5', cy: '16.5', r: '2.5', key: '1smtlt' }],\n]);\n\nexport default Car;\n"], "mappings": ";;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAU;EAAEC,EAAI;EAAOC,EAAI;EAAQC,CAAG;EAAOH,GAAK;AAAA,CAAU,GAC7D,CAAC,QAAU;EAAEC,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAOH,GAAK;AAAA,CAAU,EAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
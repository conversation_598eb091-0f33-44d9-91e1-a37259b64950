from datetime import datetime
from app import db, ma

class Course(db.Model):
    """Course model."""
    __tablename__ = 'courses'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), nullable=False, unique=True, index=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    credits = db.Column(db.Integer, nullable=False, default=3)
    department_id = db.Column(db.Integer, db.<PERSON>ey('departments.id'), nullable=False)
    faculty_id = db.Column(db.Integer, db.<PERSON><PERSON>('faculty.id'), nullable=True)
    semester = db.Column(db.Integer, nullable=False)  # 1-8
    year = db.Column(db.Integer, nullable=False)  # 1-4
    course_type = db.Column(db.<PERSON>('Core', 'Elective', 'Lab', 'Project', name='course_types'), 
                           default='Core')
    max_students = db.Column(db.Integer, default=60)
    syllabus = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    enrollments = db.relationship('Enrollment', backref='course', lazy='dynamic',
                                 cascade='all, delete-orphan')
    attendances = db.relationship('Attendance', backref='course', lazy='dynamic',
                                 cascade='all, delete-orphan')
    grades = db.relationship('Grade', backref='course', lazy='dynamic',
                            cascade='all, delete-orphan')
    
    def get_enrolled_students(self):
        """Get students enrolled in this course."""
        return [enrollment.student for enrollment in 
                self.enrollments.filter_by(is_active=True).all()]
    
    def get_enrollment_count(self):
        """Get current enrollment count."""
        return self.enrollments.filter_by(is_active=True).count()
    
    def is_full(self):
        """Check if course is at maximum capacity."""
        return self.get_enrollment_count() >= self.max_students
    
    def to_dict(self):
        """Convert course to dictionary."""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'credits': self.credits,
            'department_id': self.department_id,
            'department_name': self.department.name if self.department else None,
            'faculty_id': self.faculty_id,
            'faculty_name': self.faculty.full_name if self.faculty else None,
            'semester': self.semester,
            'year': self.year,
            'course_type': self.course_type,
            'max_students': self.max_students,
            'enrolled_count': self.get_enrollment_count(),
            'available_seats': self.max_students - self.get_enrollment_count(),
            'syllabus': self.syllabus,
            'is_active': self.is_active,
            'is_full': self.is_full(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Course {self.code}: {self.name}>'

class CourseSchema(ma.SQLAlchemyAutoSchema):
    """Course serialization schema."""
    class Meta:
        model = Course
        load_instance = True
        include_fk = True

course_schema = CourseSchema()
courses_schema = CourseSchema(many=True)

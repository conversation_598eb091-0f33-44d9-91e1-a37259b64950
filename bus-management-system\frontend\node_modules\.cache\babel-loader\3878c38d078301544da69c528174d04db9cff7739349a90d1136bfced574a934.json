{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { getDefaultState } from './mutation';\nimport { notify<PERSON>anager } from './notifyManager';\nimport { Subscribable } from './subscribable';\n// CLASS\nexport var MutationObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(MutationObserver, _Subscribable);\n  function MutationObserver(client, options) {\n    var _this;\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.setOptions(options);\n    _this.bindMethods();\n    _this.updateResult();\n    return _this;\n  }\n  var _proto = MutationObserver.prototype;\n  _proto.bindMethods = function bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  };\n  _proto.setOptions = function setOptions(options) {\n    this.options = this.client.defaultMutationOptions(options);\n  };\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      var _this$currentMutation;\n      (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.removeObserver(this);\n    }\n  };\n  _proto.onMutationUpdate = function onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    var notifyOptions = {\n      listeners: true\n    };\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n    this.notify(notifyOptions);\n  };\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n  _proto.reset = function reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  };\n  _proto.mutate = function mutate(variables, options) {\n    this.mutateOptions = options;\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n    this.currentMutation = this.client.getMutationCache().build(this.client, _extends({}, this.options, {\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    }));\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  };\n  _proto.updateResult = function updateResult() {\n    var state = this.currentMutation ? this.currentMutation.state : getDefaultState();\n    var result = _extends({}, state, {\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    });\n    this.currentResult = result;\n  };\n  _proto.notify = function notify(options) {\n    var _this2 = this;\n    notifyManager.batch(function () {\n      // First trigger the mutate callbacks\n      if (_this2.mutateOptions) {\n        if (options.onSuccess) {\n          _this2.mutateOptions.onSuccess == null ? void 0 : _this2.mutateOptions.onSuccess(_this2.currentResult.data, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(_this2.currentResult.data, null, _this2.currentResult.variables, _this2.currentResult.context);\n        } else if (options.onError) {\n          _this2.mutateOptions.onError == null ? void 0 : _this2.mutateOptions.onError(_this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(undefined, _this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n      if (options.listeners) {\n        _this2.listeners.forEach(function (listener) {\n          listener(_this2.currentResult);\n        });\n      }\n    });\n  };\n  return MutationObserver;\n}(Subscribable);", "map": {"version": 3, "names": ["_extends", "_inherits<PERSON><PERSON>e", "getDefaultState", "notify<PERSON><PERSON>ger", "Subscribable", "MutationObserver", "_Subscribable", "client", "options", "_this", "call", "setOptions", "bindMethods", "updateResult", "_proto", "prototype", "mutate", "bind", "reset", "defaultMutationOptions", "onUnsubscribe", "listeners", "length", "_this$currentMutation", "currentMutation", "removeObserver", "onMutationUpdate", "action", "notifyOptions", "type", "onSuccess", "onError", "notify", "getCurrentResult", "currentResult", "undefined", "variables", "mutateOptions", "getMutationCache", "build", "addObserver", "execute", "state", "result", "isLoading", "status", "isSuccess", "isError", "isIdle", "_this2", "batch", "data", "context", "onSettled", "error", "for<PERSON>ach", "listener"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/core/mutationObserver.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { getDefaultState } from './mutation';\nimport { notify<PERSON>anager } from './notifyManager';\nimport { Subscribable } from './subscribable';\n// CLASS\nexport var MutationObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(MutationObserver, _Subscribable);\n\n  function MutationObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n\n    _this.setOptions(options);\n\n    _this.bindMethods();\n\n    _this.updateResult();\n\n    return _this;\n  }\n\n  var _proto = MutationObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options) {\n    this.options = this.client.defaultMutationOptions(options);\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      var _this$currentMutation;\n\n      (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.removeObserver(this);\n    }\n  };\n\n  _proto.onMutationUpdate = function onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    var notifyOptions = {\n      listeners: true\n    };\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n\n    this.notify(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.reset = function reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  };\n\n  _proto.mutate = function mutate(variables, options) {\n    this.mutateOptions = options;\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, _extends({}, this.options, {\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    }));\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  };\n\n  _proto.updateResult = function updateResult() {\n    var state = this.currentMutation ? this.currentMutation.state : getDefaultState();\n\n    var result = _extends({}, state, {\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    });\n\n    this.currentResult = result;\n  };\n\n  _proto.notify = function notify(options) {\n    var _this2 = this;\n\n    notifyManager.batch(function () {\n      // First trigger the mutate callbacks\n      if (_this2.mutateOptions) {\n        if (options.onSuccess) {\n          _this2.mutateOptions.onSuccess == null ? void 0 : _this2.mutateOptions.onSuccess(_this2.currentResult.data, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(_this2.currentResult.data, null, _this2.currentResult.variables, _this2.currentResult.context);\n        } else if (options.onError) {\n          _this2.mutateOptions.onError == null ? void 0 : _this2.mutateOptions.onError(_this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(undefined, _this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n\n      if (options.listeners) {\n        _this2.listeners.forEach(function (listener) {\n          listener(_this2.currentResult);\n        });\n      }\n    });\n  };\n\n  return MutationObserver;\n}(Subscribable);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,eAAe,QAAQ,YAAY;AAC5C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C;AACA,OAAO,IAAIC,gBAAgB,GAAG,aAAa,UAAUC,aAAa,EAAE;EAClEL,cAAc,CAACI,gBAAgB,EAAEC,aAAa,CAAC;EAE/C,SAASD,gBAAgBA,CAACE,MAAM,EAAEC,OAAO,EAAE;IACzC,IAAIC,KAAK;IAETA,KAAK,GAAGH,aAAa,CAACI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACxCD,KAAK,CAACF,MAAM,GAAGA,MAAM;IAErBE,KAAK,CAACE,UAAU,CAACH,OAAO,CAAC;IAEzBC,KAAK,CAACG,WAAW,CAAC,CAAC;IAEnBH,KAAK,CAACI,YAAY,CAAC,CAAC;IAEpB,OAAOJ,KAAK;EACd;EAEA,IAAIK,MAAM,GAAGT,gBAAgB,CAACU,SAAS;EAEvCD,MAAM,CAACF,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAI,CAACI,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACD,IAAI,CAAC,IAAI,CAAC;EACpC,CAAC;EAEDH,MAAM,CAACH,UAAU,GAAG,SAASA,UAAUA,CAACH,OAAO,EAAE;IAC/C,IAAI,CAACA,OAAO,GAAG,IAAI,CAACD,MAAM,CAACY,sBAAsB,CAACX,OAAO,CAAC;EAC5D,CAAC;EAEDM,MAAM,CAACM,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC9C,IAAI,CAAC,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE;MAC1B,IAAIC,qBAAqB;MAEzB,CAACA,qBAAqB,GAAG,IAAI,CAACC,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,qBAAqB,CAACE,cAAc,CAAC,IAAI,CAAC;IAC9G;EACF,CAAC;EAEDX,MAAM,CAACY,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,MAAM,EAAE;IAC1D,IAAI,CAACd,YAAY,CAAC,CAAC,CAAC,CAAC;;IAErB,IAAIe,aAAa,GAAG;MAClBP,SAAS,EAAE;IACb,CAAC;IAED,IAAIM,MAAM,CAACE,IAAI,KAAK,SAAS,EAAE;MAC7BD,aAAa,CAACE,SAAS,GAAG,IAAI;IAChC,CAAC,MAAM,IAAIH,MAAM,CAACE,IAAI,KAAK,OAAO,EAAE;MAClCD,aAAa,CAACG,OAAO,GAAG,IAAI;IAC9B;IAEA,IAAI,CAACC,MAAM,CAACJ,aAAa,CAAC;EAC5B,CAAC;EAEDd,MAAM,CAACmB,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACpD,OAAO,IAAI,CAACC,aAAa;EAC3B,CAAC;EAEDpB,MAAM,CAACI,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9B,IAAI,CAACM,eAAe,GAAGW,SAAS;IAChC,IAAI,CAACtB,YAAY,CAAC,CAAC;IACnB,IAAI,CAACmB,MAAM,CAAC;MACVX,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAEDP,MAAM,CAACE,MAAM,GAAG,SAASA,MAAMA,CAACoB,SAAS,EAAE5B,OAAO,EAAE;IAClD,IAAI,CAAC6B,aAAa,GAAG7B,OAAO;IAE5B,IAAI,IAAI,CAACgB,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,cAAc,CAAC,IAAI,CAAC;IAC3C;IAEA,IAAI,CAACD,eAAe,GAAG,IAAI,CAACjB,MAAM,CAAC+B,gBAAgB,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAChC,MAAM,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACQ,OAAO,EAAE;MAClG4B,SAAS,EAAE,OAAOA,SAAS,KAAK,WAAW,GAAGA,SAAS,GAAG,IAAI,CAAC5B,OAAO,CAAC4B;IACzE,CAAC,CAAC,CAAC;IACH,IAAI,CAACZ,eAAe,CAACgB,WAAW,CAAC,IAAI,CAAC;IACtC,OAAO,IAAI,CAAChB,eAAe,CAACiB,OAAO,CAAC,CAAC;EACvC,CAAC;EAED3B,MAAM,CAACD,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IAC5C,IAAI6B,KAAK,GAAG,IAAI,CAAClB,eAAe,GAAG,IAAI,CAACA,eAAe,CAACkB,KAAK,GAAGxC,eAAe,CAAC,CAAC;IAEjF,IAAIyC,MAAM,GAAG3C,QAAQ,CAAC,CAAC,CAAC,EAAE0C,KAAK,EAAE;MAC/BE,SAAS,EAAEF,KAAK,CAACG,MAAM,KAAK,SAAS;MACrCC,SAAS,EAAEJ,KAAK,CAACG,MAAM,KAAK,SAAS;MACrCE,OAAO,EAAEL,KAAK,CAACG,MAAM,KAAK,OAAO;MACjCG,MAAM,EAAEN,KAAK,CAACG,MAAM,KAAK,MAAM;MAC/B7B,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBE,KAAK,EAAE,IAAI,CAACA;IACd,CAAC,CAAC;IAEF,IAAI,CAACgB,aAAa,GAAGS,MAAM;EAC7B,CAAC;EAED7B,MAAM,CAACkB,MAAM,GAAG,SAASA,MAAMA,CAACxB,OAAO,EAAE;IACvC,IAAIyC,MAAM,GAAG,IAAI;IAEjB9C,aAAa,CAAC+C,KAAK,CAAC,YAAY;MAC9B;MACA,IAAID,MAAM,CAACZ,aAAa,EAAE;QACxB,IAAI7B,OAAO,CAACsB,SAAS,EAAE;UACrBmB,MAAM,CAACZ,aAAa,CAACP,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGmB,MAAM,CAACZ,aAAa,CAACP,SAAS,CAACmB,MAAM,CAACf,aAAa,CAACiB,IAAI,EAAEF,MAAM,CAACf,aAAa,CAACE,SAAS,EAAEa,MAAM,CAACf,aAAa,CAACkB,OAAO,CAAC;UACzKH,MAAM,CAACZ,aAAa,CAACgB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,MAAM,CAACZ,aAAa,CAACgB,SAAS,CAACJ,MAAM,CAACf,aAAa,CAACiB,IAAI,EAAE,IAAI,EAAEF,MAAM,CAACf,aAAa,CAACE,SAAS,EAAEa,MAAM,CAACf,aAAa,CAACkB,OAAO,CAAC;QACjL,CAAC,MAAM,IAAI5C,OAAO,CAACuB,OAAO,EAAE;UAC1BkB,MAAM,CAACZ,aAAa,CAACN,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGkB,MAAM,CAACZ,aAAa,CAACN,OAAO,CAACkB,MAAM,CAACf,aAAa,CAACoB,KAAK,EAAEL,MAAM,CAACf,aAAa,CAACE,SAAS,EAAEa,MAAM,CAACf,aAAa,CAACkB,OAAO,CAAC;UACtKH,MAAM,CAACZ,aAAa,CAACgB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,MAAM,CAACZ,aAAa,CAACgB,SAAS,CAAClB,SAAS,EAAEc,MAAM,CAACf,aAAa,CAACoB,KAAK,EAAEL,MAAM,CAACf,aAAa,CAACE,SAAS,EAAEa,MAAM,CAACf,aAAa,CAACkB,OAAO,CAAC;QACvL;MACF,CAAC,CAAC;;MAGF,IAAI5C,OAAO,CAACa,SAAS,EAAE;QACrB4B,MAAM,CAAC5B,SAAS,CAACkC,OAAO,CAAC,UAAUC,QAAQ,EAAE;UAC3CA,QAAQ,CAACP,MAAM,CAACf,aAAa,CAAC;QAChC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,OAAO7B,gBAAgB;AACzB,CAAC,CAACD,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
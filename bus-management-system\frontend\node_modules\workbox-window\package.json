{"name": "workbox-window", "version": "6.6.0", "license": "MIT", "author": "Google's Web DevRel Team", "description": "Simplifies communications with Workbox packages running in the service worker", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "window", "message", "postMessage"], "workbox": {"packageType": "window", "primaryBuild": "build/workbox-window.prod.mjs"}, "main": "build/workbox-window.prod.umd.js", "module": "build/workbox-window.prod.es5.mjs", "types": "index.d.ts", "dependencies": {"@types/trusted-types": "^2.0.2", "workbox-core": "6.6.0"}, "gitHead": "252644491d9bb5a67518935ede6df530107c9475"}
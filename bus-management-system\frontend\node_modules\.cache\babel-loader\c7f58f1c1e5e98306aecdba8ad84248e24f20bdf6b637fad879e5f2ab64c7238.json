{"ast": null, "code": "import { QueryObserver } from '../core';\nimport { parseQueryArgs } from '../core/utils';\nimport { useBaseQuery } from './useBaseQuery'; // HOOK\n\nexport function useQuery(arg1, arg2, arg3) {\n  var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n  return useBaseQuery(parsedOptions, QueryObserver);\n}", "map": {"version": 3, "names": ["QueryObserver", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useBaseQuery", "useQuery", "arg1", "arg2", "arg3", "parsedOptions"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/react/useQuery.js"], "sourcesContent": ["import { QueryObserver } from '../core';\nimport { parseQueryArgs } from '../core/utils';\nimport { useBaseQuery } from './useBaseQuery'; // HOOK\n\nexport function useQuery(arg1, arg2, arg3) {\n  var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n  return useBaseQuery(parsedOptions, QueryObserver);\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,SAAS;AACvC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,YAAY,QAAQ,gBAAgB,CAAC,CAAC;;AAE/C,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACzC,IAAIC,aAAa,GAAGN,cAAc,CAACG,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;EACpD,OAAOJ,YAAY,CAACK,aAAa,EAAEP,aAAa,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
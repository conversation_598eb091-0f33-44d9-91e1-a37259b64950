from flask import Blueprint, request
from flask_jwt_extended import jwt_required
from app import db
from app.models.user import User
from app.models.faculty import Faculty
from app.models.department import Department
from app.utils.decorators import admin_required
from app.utils.helpers import (
    success_response, error_response, paginate_query,
    validate_email, validate_phone, generate_employee_id
)

faculty_bp = Blueprint('faculty', __name__)

@faculty_bp.route('', methods=['GET'])
@jwt_required()
def get_faculty():
    """Get all faculty with pagination and filtering."""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        department_id = request.args.get('department_id', type=int)
        search = request.args.get('search', '').strip()
        
        query = Faculty.query.filter_by(is_active=True)
        
        if department_id:
            query = query.filter_by(department_id=department_id)
        
        if search:
            query = query.filter(
                (Faculty.first_name.contains(search)) |
                (Faculty.last_name.contains(search)) |
                (Faculty.employee_id.contains(search))
            )
        
        query = query.order_by(Faculty.employee_id)
        paginated = paginate_query(query, page, per_page)
        
        if not paginated:
            return error_response('Invalid pagination parameters')
        
        faculty_data = [faculty.to_dict() for faculty in paginated['items']]
        
        return success_response('Faculty retrieved successfully', {
            'faculty': faculty_data,
            'pagination': {
                'total': paginated['total'],
                'pages': paginated['pages'],
                'current_page': paginated['current_page'],
                'per_page': paginated['per_page'],
                'has_next': paginated['has_next'],
                'has_prev': paginated['has_prev']
            }
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve faculty: {str(e)}', 500)

@faculty_bp.route('/<int:faculty_id>', methods=['GET'])
@jwt_required()
def get_faculty_member(faculty_id):
    """Get a specific faculty member by ID."""
    try:
        faculty = Faculty.query.filter_by(id=faculty_id, is_active=True).first()
        
        if not faculty:
            return error_response('Faculty not found', 404)
        
        return success_response('Faculty retrieved successfully', {
            'faculty': faculty.to_dict()
        })
        
    except Exception as e:
        return error_response(f'Failed to retrieve faculty: {str(e)}', 500)

@faculty_bp.route('', methods=['POST'])
@admin_required
def create_faculty(current_user):
    """Create a new faculty member."""
    try:
        data = request.get_json()
        
        if not data:
            return error_response('No data provided')
        
        required_fields = ['first_name', 'last_name', 'email', 'department_id', 'designation']
        for field in required_fields:
            if not data.get(field):
                return error_response(f'{field.replace("_", " ").title()} is required')
        
        email = data.get('email').strip().lower()
        if not validate_email(email):
            return error_response('Invalid email format')
        
        if User.query.filter_by(email=email).first():
            return error_response('Email already exists')
        
        phone = data.get('phone', '').strip()
        if phone and not validate_phone(phone):
            return error_response('Invalid phone number format')
        
        department = Department.query.get(data.get('department_id'))
        if not department:
            return error_response('Department not found')
        
        # Generate username and employee ID
        first_name = data.get('first_name').strip()
        last_name = data.get('last_name').strip()
        username = f"{first_name.lower()}.{last_name.lower()}"
        
        counter = 1
        original_username = username
        while User.query.filter_by(username=username).first():
            username = f"{original_username}{counter}"
            counter += 1
        
        # Generate employee ID
        last_faculty = Faculty.query.filter_by(
            department_id=department.id
        ).order_by(Faculty.employee_id.desc()).first()
        
        sequence = 1
        if last_faculty:
            try:
                sequence = int(last_faculty.employee_id[-3:]) + 1
            except:
                sequence = 1
        
        employee_id = generate_employee_id(department.code, sequence)
        
        # Create user account
        user = User(
            username=username,
            email=email,
            role='faculty',
            is_active=True
        )
        user.set_password('faculty123')  # Default password
        
        db.session.add(user)
        db.session.flush()
        
        # Create faculty record
        faculty = Faculty(
            user_id=user.id,
            employee_id=employee_id,
            first_name=first_name,
            last_name=last_name,
            date_of_birth=data.get('date_of_birth'),
            gender=data.get('gender'),
            phone=phone,
            address=data.get('address'),
            department_id=data.get('department_id'),
            designation=data.get('designation'),
            qualification=data.get('qualification'),
            specialization=data.get('specialization'),
            experience_years=data.get('experience_years'),
            salary=data.get('salary')
        )
        
        db.session.add(faculty)
        db.session.commit()
        
        return success_response('Faculty created successfully', {
            'faculty': faculty.to_dict(),
            'default_password': 'faculty123'
        }, 201)
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to create faculty: {str(e)}', 500)

@faculty_bp.route('/<int:faculty_id>', methods=['PUT'])
@admin_required
def update_faculty(current_user, faculty_id):
    """Update a faculty member."""
    try:
        faculty = Faculty.query.filter_by(id=faculty_id, is_active=True).first()
        
        if not faculty:
            return error_response('Faculty not found', 404)
        
        data = request.get_json()
        if not data:
            return error_response('No data provided')
        
        # Update fields if provided
        updateable_fields = [
            'first_name', 'last_name', 'date_of_birth', 'gender', 'phone',
            'address', 'designation', 'qualification', 'specialization',
            'experience_years', 'salary'
        ]
        
        for field in updateable_fields:
            if field in data:
                setattr(faculty, field, data[field])
        
        if 'phone' in data:
            phone = data['phone'].strip()
            if phone and not validate_phone(phone):
                return error_response('Invalid phone number format')
            faculty.phone = phone
        
        if 'email' in data:
            email = data['email'].strip().lower()
            if not validate_email(email):
                return error_response('Invalid email format')
            
            existing_user = User.query.filter(
                User.email == email, User.id != faculty.user_id
            ).first()
            if existing_user:
                return error_response('Email already exists')
            
            faculty.user.email = email
        
        db.session.commit()
        
        return success_response('Faculty updated successfully', {
            'faculty': faculty.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to update faculty: {str(e)}', 500)

@faculty_bp.route('/<int:faculty_id>', methods=['DELETE'])
@admin_required
def delete_faculty(current_user, faculty_id):
    """Delete (deactivate) a faculty member."""
    try:
        faculty = Faculty.query.filter_by(id=faculty_id, is_active=True).first()
        
        if not faculty:
            return error_response('Faculty not found', 404)
        
        faculty.is_active = False
        faculty.user.is_active = False
        
        db.session.commit()
        
        return success_response('Faculty deleted successfully')
        
    except Exception as e:
        db.session.rollback()
        return error_response(f'Failed to delete faculty: {str(e)}', 500)

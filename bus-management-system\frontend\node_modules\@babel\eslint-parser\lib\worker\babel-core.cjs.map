{"version": 3, "names": ["exports", "initialize", "babel", "init", "version", "traverse", "types", "tokTypes", "parseSync", "parseAsync", "loadPartialConfigSync", "loadPartialConfigAsync", "createConfigItemAsync", "createConfigItemSync", "createConfigItem", "require"], "sources": ["../../src/worker/babel-core.cts"], "sourcesContent": ["export = exports as typeof import(\"@babel/core\") & {\n  init: Promise<void> | null;\n};\n\nfunction initialize(babel: typeof import(\"@babel/core\")) {\n  exports.init = null;\n  exports.version = babel.version;\n  exports.traverse = babel.traverse;\n  exports.types = babel.types;\n  exports.tokTypes = babel.tokTypes;\n  exports.parseSync = babel.parseSync;\n  exports.parseAsync = babel.parseAsync;\n  exports.loadPartialConfigSync = babel.loadPartialConfigSync;\n  exports.loadPartialConfigAsync = babel.loadPartialConfigAsync;\n  exports.createConfigItemAsync = babel.createConfigItemAsync;\n\n  if (process.env.BABEL_8_BREAKING) {\n    exports.createConfigItemSync = babel.createConfigItemSync;\n  } else {\n    // babel.createConfigItemSync is available on 7.13+\n    // we support Babel 7.11+\n    exports.createConfigItemSync =\n      babel.createConfigItemSync || babel.createConfigItem;\n  }\n}\n\nif (USE_ESM) {\n  exports.init = import(\"@babel/core\").then(initialize);\n} else {\n  initialize(require(\"@babel/core\"));\n}\n"], "mappings": ";;iBAASA,OAAO;AAIhB,SAASC,UAAUA,CAACC,KAAmC,EAAE;EACvDF,OAAO,CAACG,IAAI,GAAG,IAAI;EACnBH,OAAO,CAACI,OAAO,GAAGF,KAAK,CAACE,OAAO;EAC/BJ,OAAO,CAACK,QAAQ,GAAGH,KAAK,CAACG,QAAQ;EACjCL,OAAO,CAACM,KAAK,GAAGJ,KAAK,CAACI,KAAK;EAC3BN,OAAO,CAACO,QAAQ,GAAGL,KAAK,CAACK,QAAQ;EACjCP,OAAO,CAACQ,SAAS,GAAGN,KAAK,CAACM,SAAS;EACnCR,OAAO,CAACS,UAAU,GAAGP,KAAK,CAACO,UAAU;EACrCT,OAAO,CAACU,qBAAqB,GAAGR,KAAK,CAACQ,qBAAqB;EAC3DV,OAAO,CAACW,sBAAsB,GAAGT,KAAK,CAACS,sBAAsB;EAC7DX,OAAO,CAACY,qBAAqB,GAAGV,KAAK,CAACU,qBAAqB;EAIpD;IAGLZ,OAAO,CAACa,oBAAoB,GAC1BX,KAAK,CAACW,oBAAoB,IAAIX,KAAK,CAACY,gBAAgB;EACxD;AACF;AAIO;EACLb,UAAU,CAACc,OAAO,CAAC,aAAa,CAAC,CAAC;AACpC", "ignoreList": []}
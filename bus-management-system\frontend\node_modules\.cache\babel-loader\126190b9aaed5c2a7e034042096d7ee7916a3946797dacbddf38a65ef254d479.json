{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\college management system\\\\bus-management-system\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Bus, Users, UserCheck, Calendar, Wrench, MapPin, DollarSign, BarChart3, User, LogOut, Menu, X } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const {\n    user,\n    logout\n  } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n  const navigation = [{\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: BarChart3,\n    roles: ['admin', 'driver', 'conductor', 'student', 'parent']\n  },\n  // Phase 1: Bus Setup & Staff Allocation\n  {\n    name: 'Bus Management',\n    href: '/buses',\n    icon: Bus,\n    roles: ['admin']\n  }, {\n    name: 'Driver Management',\n    href: '/drivers',\n    icon: Users,\n    roles: ['admin']\n  }, {\n    name: 'Route Management',\n    href: '/routes',\n    icon: Route,\n    roles: ['admin']\n  },\n  // Phase 2: Daily Operations\n  {\n    name: 'Student Management',\n    href: '/students',\n    icon: UserCheck,\n    roles: ['admin', 'driver', 'conductor']\n  }, {\n    name: 'Trip Management',\n    href: '/trips',\n    icon: Calendar,\n    roles: ['admin', 'driver', 'conductor']\n  }, {\n    name: 'Attendance',\n    href: '/attendance',\n    icon: UserCheck,\n    roles: ['admin', 'driver', 'conductor']\n  }, {\n    name: 'Fee Management',\n    href: '/fees',\n    icon: DollarSign,\n    roles: ['admin']\n  },\n  // Phase 3: Maintenance & Monitoring\n  {\n    name: 'Maintenance',\n    href: '/maintenance',\n    icon: Wrench,\n    roles: ['admin']\n  }, {\n    name: 'Live Tracking',\n    href: '/tracking',\n    icon: MapPin,\n    roles: ['admin', 'driver', 'conductor', 'student', 'parent']\n  }, {\n    name: 'Reports',\n    href: '/reports',\n    icon: BarChart3,\n    roles: ['admin']\n  }];\n  const filteredNavigation = navigation.filter(item => !item.roles || item.roles.includes(user === null || user === void 0 ? void 0 : user.role));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex overflow-hidden bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${sidebarOpen ? 'block' : 'hidden'} fixed inset-0 flex z-40 md:hidden`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 right-0 -mr-12 pt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n            onClick: () => setSidebarOpen(false),\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SidebarContent, {\n          navigation: filteredNavigation,\n          location: location\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden md:flex md:flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col w-64\",\n        children: /*#__PURE__*/_jsxDEV(SidebarContent, {\n          navigation: filteredNavigation,\n          location: location\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col w-0 flex-1 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden\",\n          onClick: () => setSidebarOpen(true),\n          children: /*#__PURE__*/_jsxDEV(Menu, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 px-4 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full flex md:ml-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative w-full text-gray-400 focus-within:text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-5 w-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 86,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent sm:text-sm\",\n                  placeholder: \"Search buses, routes, students...\",\n                  type: \"search\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4 flex items-center md:ml-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3 relative\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: [user === null || user === void 0 ? void 0 : user.first_name, \" \", user === null || user === void 0 ? void 0 : user.last_name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 capitalize\",\n                    children: user === null || user === void 0 ? void 0 : user.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/profile\",\n                  className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                  children: /*#__PURE__*/_jsxDEV(User, {\n                    className: \"h-6 w-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleLogout,\n                  className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                  children: /*#__PURE__*/_jsxDEV(LogOut, {\n                    className: \"h-6 w-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 relative overflow-y-auto focus:outline-none\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n            children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"4XeGbH0uZYTp7AIKnWcw5Qm5mxM=\", false, function () {\n  return [useAuth, useLocation, useNavigate];\n});\n_c = Layout;\nconst SidebarContent = ({\n  navigation,\n  location\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center flex-shrink-0 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Bus, {\n          className: \"h-8 w-8 text-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"ml-2 text-xl font-bold text-gray-900\",\n          children: \"BusMS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"mt-5 flex-1 px-2 space-y-1\",\n      children: navigation.map(item => {\n        const isActive = location.pathname === item.href;\n        const Icon = item.icon;\n        return /*#__PURE__*/_jsxDEV(Link, {\n          to: item.href,\n          className: `${isActive ? 'sidebar-link-active' : 'sidebar-link-inactive'} sidebar-link group`,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            className: `${isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'} mr-3 h-6 w-6`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), item.name]\n        }, item.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-shrink-0 flex border-t border-gray-200 p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-xs text-gray-500\",\n      children: \"Bus Management System v1.0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 143,\n  columnNumber: 3\n}, this);\n_c2 = SidebarContent;\nexport default Layout;\nvar _c, _c2;\n$RefreshReg$(_c, \"Layout\");\n$RefreshReg$(_c2, \"SidebarContent\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "Link", "useLocation", "useNavigate", "useAuth", "Bus", "Users", "UserCheck", "Calendar", "<PERSON><PERSON>", "MapPin", "DollarSign", "BarChart3", "User", "LogOut", "<PERSON><PERSON>", "X", "jsxDEV", "_jsxDEV", "Layout", "_s", "sidebarOpen", "setSidebarOpen", "user", "logout", "location", "navigate", "handleLogout", "navigation", "name", "href", "icon", "roles", "Route", "filteredNavigation", "filter", "item", "includes", "role", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON>bar<PERSON><PERSON>nt", "fill", "viewBox", "fillRule", "d", "clipRule", "placeholder", "type", "first_name", "last_name", "to", "_c", "map", "isActive", "pathname", "Icon", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  Bus, Users, UserCheck, Calendar, Wrench,\n  MapPin, DollarSign, BarChart3, User, LogOut, Menu, X\n} from 'lucide-react';\n\nconst Layout = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user, logout } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', icon: BarChart3, roles: ['admin', 'driver', 'conductor', 'student', 'parent'] },\n    \n    // Phase 1: Bus Setup & Staff Allocation\n    { name: 'Bus Management', href: '/buses', icon: Bus, roles: ['admin'] },\n    { name: 'Driver Management', href: '/drivers', icon: Users, roles: ['admin'] },\n    { name: 'Route Management', href: '/routes', icon: Route, roles: ['admin'] },\n    \n    // Phase 2: Daily Operations\n    { name: 'Student Management', href: '/students', icon: UserCheck, roles: ['admin', 'driver', 'conductor'] },\n    { name: 'Trip Management', href: '/trips', icon: Calendar, roles: ['admin', 'driver', 'conductor'] },\n    { name: 'Attendance', href: '/attendance', icon: UserCheck, roles: ['admin', 'driver', 'conductor'] },\n    { name: 'Fee Management', href: '/fees', icon: DollarSign, roles: ['admin'] },\n    \n    // Phase 3: Maintenance & Monitoring\n    { name: 'Maintenance', href: '/maintenance', icon: Wrench, roles: ['admin'] },\n    { name: 'Live Tracking', href: '/tracking', icon: MapPin, roles: ['admin', 'driver', 'conductor', 'student', 'parent'] },\n    { name: 'Reports', href: '/reports', icon: BarChart3, roles: ['admin'] },\n  ];\n\n  const filteredNavigation = navigation.filter(item => \n    !item.roles || item.roles.includes(user?.role)\n  );\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gray-100\">\n      {/* Mobile sidebar */}\n      <div className={`${sidebarOpen ? 'block' : 'hidden'} fixed inset-0 flex z-40 md:hidden`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)}></div>\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n          <SidebarContent navigation={filteredNavigation} location={location} />\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:flex-shrink-0\">\n        <div className=\"flex flex-col w-64\">\n          <SidebarContent navigation={filteredNavigation} location={location} />\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        {/* Top navigation */}\n        <div className=\"relative z-10 flex-shrink-0 flex h-16 bg-white shadow\">\n          <button\n            className=\"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-6 w-6\" />\n          </button>\n          \n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <input\n                    className=\"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent sm:text-sm\"\n                    placeholder=\"Search buses, routes, students...\"\n                    type=\"search\"\n                  />\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"ml-4 flex items-center md:ml-6\">\n              <div className=\"ml-3 relative\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-medium text-gray-700\">\n                      {user?.first_name} {user?.last_name}\n                    </div>\n                    <div className=\"text-xs text-gray-500 capitalize\">\n                      {user?.role}\n                    </div>\n                  </div>\n                  \n                  <Link\n                    to=\"/profile\"\n                    className=\"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n                  >\n                    <User className=\"h-6 w-6\" />\n                  </Link>\n                  \n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n                  >\n                    <LogOut className=\"h-6 w-6\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              <Outlet />\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nconst SidebarContent = ({ navigation, location }) => (\n  <div className=\"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white\">\n    <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n      <div className=\"flex items-center flex-shrink-0 px-4\">\n        <div className=\"flex items-center\">\n          <Bus className=\"h-8 w-8 text-primary-600\" />\n          <h1 className=\"ml-2 text-xl font-bold text-gray-900\">BusMS</h1>\n        </div>\n      </div>\n      \n      <nav className=\"mt-5 flex-1 px-2 space-y-1\">\n        {navigation.map((item) => {\n          const isActive = location.pathname === item.href;\n          const Icon = item.icon;\n          \n          return (\n            <Link\n              key={item.name}\n              to={item.href}\n              className={`${\n                isActive\n                  ? 'sidebar-link-active'\n                  : 'sidebar-link-inactive'\n              } sidebar-link group`}\n            >\n              <Icon\n                className={`${\n                  isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'\n                } mr-3 h-6 w-6`}\n              />\n              {item.name}\n            </Link>\n          );\n        })}\n      </nav>\n    </div>\n    \n    <div className=\"flex-shrink-0 flex border-t border-gray-200 p-4\">\n      <div className=\"text-xs text-gray-500\">\n        Bus Management System v1.0\n      </div>\n    </div>\n  </div>\n);\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACzE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EACvCC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,CAAC,QAC/C,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAEwB,IAAI;IAAEC;EAAO,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAClC,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9B,MAAMwB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMH,MAAM,CAAC,CAAC;IACdE,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAME,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAEnB,SAAS;IAAEoB,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ;EAAE,CAAC;EAExH;EACA;IAAEH,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE1B,GAAG;IAAE2B,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EACvE;IAAEH,IAAI,EAAE,mBAAmB;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAEzB,KAAK;IAAE0B,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EAC9E;IAAEH,IAAI,EAAE,kBAAkB;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAEE,KAAK;IAAED,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC;EAE5E;EACA;IAAEH,IAAI,EAAE,oBAAoB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAExB,SAAS;IAAEyB,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW;EAAE,CAAC,EAC3G;IAAEH,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAEvB,QAAQ;IAAEwB,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW;EAAE,CAAC,EACpG;IAAEH,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAExB,SAAS;IAAEyB,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW;EAAE,CAAC,EACrG;IAAEH,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAEpB,UAAU;IAAEqB,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC;EAE7E;EACA;IAAEH,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAEtB,MAAM;IAAEuB,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EAC7E;IAAEH,IAAI,EAAE,eAAe;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAErB,MAAM;IAAEsB,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ;EAAE,CAAC,EACxH;IAAEH,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAEnB,SAAS;IAAEoB,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,CACzE;EAED,MAAME,kBAAkB,GAAGN,UAAU,CAACO,MAAM,CAACC,IAAI,IAC/C,CAACA,IAAI,CAACJ,KAAK,IAAII,IAAI,CAACJ,KAAK,CAACK,QAAQ,CAACd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,IAAI,CAC/C,CAAC;EAED,oBACEpB,OAAA;IAAKqB,SAAS,EAAC,2CAA2C;IAAAC,QAAA,gBAExDtB,OAAA;MAAKqB,SAAS,EAAE,GAAGlB,WAAW,GAAG,OAAO,GAAG,QAAQ,oCAAqC;MAAAmB,QAAA,gBACtFtB,OAAA;QAAKqB,SAAS,EAAC,yCAAyC;QAACE,OAAO,EAAEA,CAAA,KAAMnB,cAAc,CAAC,KAAK;MAAE;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrG3B,OAAA;QAAKqB,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrEtB,OAAA;UAAKqB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDtB,OAAA;YACEqB,SAAS,EAAC,gIAAgI;YAC1IE,OAAO,EAAEA,CAAA,KAAMnB,cAAc,CAAC,KAAK,CAAE;YAAAkB,QAAA,eAErCtB,OAAA,CAACF,CAAC;cAACuB,SAAS,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN3B,OAAA,CAAC4B,cAAc;UAAClB,UAAU,EAAEM,kBAAmB;UAACT,QAAQ,EAAEA;QAAS;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKqB,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eAC9CtB,OAAA;QAAKqB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCtB,OAAA,CAAC4B,cAAc;UAAClB,UAAU,EAAEM,kBAAmB;UAACT,QAAQ,EAAEA;QAAS;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKqB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBAEvDtB,OAAA;QAAKqB,SAAS,EAAC,uDAAuD;QAAAC,QAAA,gBACpEtB,OAAA;UACEqB,SAAS,EAAC,+HAA+H;UACzIE,OAAO,EAAEA,CAAA,KAAMnB,cAAc,CAAC,IAAI,CAAE;UAAAkB,QAAA,eAEpCtB,OAAA,CAACH,IAAI;YAACwB,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAET3B,OAAA;UAAKqB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CtB,OAAA;YAAKqB,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BtB,OAAA;cAAKqB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAClCtB,OAAA;gBAAKqB,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,gBACvEtB,OAAA;kBAAKqB,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,eAC9EtB,OAAA;oBAAKqB,SAAS,EAAC,SAAS;oBAACQ,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAR,QAAA,eAC9DtB,OAAA;sBAAM+B,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,kHAAkH;sBAACC,QAAQ,EAAC;oBAAS;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3B,OAAA;kBACEqB,SAAS,EAAC,yLAAyL;kBACnMa,WAAW,EAAC,mCAAmC;kBAC/CC,IAAI,EAAC;gBAAQ;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3B,OAAA;YAAKqB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7CtB,OAAA;cAAKqB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BtB,OAAA;gBAAKqB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CtB,OAAA;kBAAKqB,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBtB,OAAA;oBAAKqB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAC/CjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,UAAU,EAAC,GAAC,EAAC/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,SAAS;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACN3B,OAAA;oBAAKqB,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9CjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3B,OAAA,CAACjB,IAAI;kBACHuD,EAAE,EAAC,UAAU;kBACbjB,SAAS,EAAC,wIAAwI;kBAAAC,QAAA,eAElJtB,OAAA,CAACL,IAAI;oBAAC0B,SAAS,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eAEP3B,OAAA;kBACEuB,OAAO,EAAEd,YAAa;kBACtBY,SAAS,EAAC,wIAAwI;kBAAAC,QAAA,eAElJtB,OAAA,CAACJ,MAAM;oBAACyB,SAAS,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAMqB,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eAClEtB,OAAA;UAAKqB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBtB,OAAA;YAAKqB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,eACrDtB,OAAA,CAAClB,MAAM;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAnIID,MAAM;EAAA,QAEef,OAAO,EACfF,WAAW,EACXC,WAAW;AAAA;AAAAsD,EAAA,GAJxBtC,MAAM;AAqIZ,MAAM2B,cAAc,GAAGA,CAAC;EAAElB,UAAU;EAAEH;AAAS,CAAC,kBAC9CP,OAAA;EAAKqB,SAAS,EAAC,4DAA4D;EAAAC,QAAA,gBACzEtB,OAAA;IAAKqB,SAAS,EAAC,gDAAgD;IAAAC,QAAA,gBAC7DtB,OAAA;MAAKqB,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnDtB,OAAA;QAAKqB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtB,OAAA,CAACb,GAAG;UAACkC,SAAS,EAAC;QAA0B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5C3B,OAAA;UAAIqB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3B,OAAA;MAAKqB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EACxCZ,UAAU,CAAC8B,GAAG,CAAEtB,IAAI,IAAK;QACxB,MAAMuB,QAAQ,GAAGlC,QAAQ,CAACmC,QAAQ,KAAKxB,IAAI,CAACN,IAAI;QAChD,MAAM+B,IAAI,GAAGzB,IAAI,CAACL,IAAI;QAEtB,oBACEb,OAAA,CAACjB,IAAI;UAEHuD,EAAE,EAAEpB,IAAI,CAACN,IAAK;UACdS,SAAS,EAAE,GACToB,QAAQ,GACJ,qBAAqB,GACrB,uBAAuB,qBACP;UAAAnB,QAAA,gBAEtBtB,OAAA,CAAC2C,IAAI;YACHtB,SAAS,EAAE,GACToB,QAAQ,GAAG,kBAAkB,GAAG,yCAAyC;UAC3D;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,EACDT,IAAI,CAACP,IAAI;QAAA,GAbLO,IAAI,CAACP,IAAI;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcV,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,eAEN3B,OAAA;IAAKqB,SAAS,EAAC,iDAAiD;IAAAC,QAAA,eAC9DtB,OAAA;MAAKqB,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAAC;IAEvC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACiB,GAAA,GA3CIhB,cAAc;AA6CpB,eAAe3B,MAAM;AAAC,IAAAsC,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
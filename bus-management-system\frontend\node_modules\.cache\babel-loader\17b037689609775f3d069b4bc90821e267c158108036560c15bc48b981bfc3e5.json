{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ImageMinus = createLucideIcon(\"ImageMinus\", [[\"path\", {\n  d: \"M21 9v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7\",\n  key: \"m87ecr\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"22\",\n  y1: \"5\",\n  y2: \"5\",\n  key: \"ez7e4s\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}], [\"path\", {\n  d: \"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\",\n  key: \"1xmnt7\"\n}]]);\nexport { ImageMinus as default };", "map": {"version": 3, "names": ["ImageMinus", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\image-minus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ImageMinus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgOXYxMGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMlY1YTIgMiAwIDAgMSAyLTJoNyIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIyMiIgeTE9IjUiIHkyPSI1IiAvPgogIDxjaXJjbGUgY3g9IjkiIGN5PSI5IiByPSIyIiAvPgogIDxwYXRoIGQ9Im0yMSAxNS0zLjA4Ni0zLjA4NmEyIDIgMCAwIDAtMi44MjggMEw2IDIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/image-minus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ImageMinus = createLucideIcon('ImageMinus', [\n  [\n    'path',\n    {\n      d: 'M21 9v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7',\n      key: 'm87ecr',\n    },\n  ],\n  ['line', { x1: '16', x2: '22', y1: '5', y2: '5', key: 'ez7e4s' }],\n  ['circle', { cx: '9', cy: '9', r: '2', key: 'af1f0g' }],\n  ['path', { d: 'm21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21', key: '1xmnt7' }],\n]);\n\nexport default ImageMinus;\n"], "mappings": ";;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAU;EAAEK,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKP,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Network = createLucideIcon(\"Network\", [[\"rect\", {\n  x: \"16\",\n  y: \"16\",\n  width: \"6\",\n  height: \"6\",\n  rx: \"1\",\n  key: \"4q2zg0\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"16\",\n  width: \"6\",\n  height: \"6\",\n  rx: \"1\",\n  key: \"8cvhb9\"\n}], [\"rect\", {\n  x: \"9\",\n  y: \"2\",\n  width: \"6\",\n  height: \"6\",\n  rx: \"1\",\n  key: \"1egb70\"\n}], [\"path\", {\n  d: \"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3\",\n  key: \"1jsf9p\"\n}], [\"path\", {\n  d: \"M12 12V8\",\n  key: \"2874zd\"\n}]]);\nexport { Network as default };", "map": {"version": 3, "names": ["Network", "createLucideIcon", "x", "y", "width", "height", "rx", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\network.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Network\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSIxNiIgeT0iMTYiIHdpZHRoPSI2IiBoZWlnaHQ9IjYiIHJ4PSIxIiAvPgogIDxyZWN0IHg9IjIiIHk9IjE2IiB3aWR0aD0iNiIgaGVpZ2h0PSI2IiByeD0iMSIgLz4KICA8cmVjdCB4PSI5IiB5PSIyIiB3aWR0aD0iNiIgaGVpZ2h0PSI2IiByeD0iMSIgLz4KICA8cGF0aCBkPSJNNSAxNnYtM2ExIDEgMCAwIDEgMS0xaDEyYTEgMSAwIDAgMSAxIDF2MyIgLz4KICA8cGF0aCBkPSJNMTIgMTJWOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/network\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Network = createLucideIcon('Network', [\n  [\n    'rect',\n    { x: '16', y: '16', width: '6', height: '6', rx: '1', key: '4q2zg0' },\n  ],\n  [\n    'rect',\n    { x: '2', y: '16', width: '6', height: '6', rx: '1', key: '8cvhb9' },\n  ],\n  ['rect', { x: '9', y: '2', width: '6', height: '6', rx: '1', key: '1egb70' }],\n  ['path', { d: 'M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3', key: '1jsf9p' }],\n  ['path', { d: 'M12 12V8', key: '2874zd' }],\n]);\n\nexport default Network;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EAAEC,CAAA,EAAG,IAAM;EAAAC,CAAA,EAAG,IAAM;EAAAC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CACE,QACA;EAAEL,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,QAAQ;EAAEL,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CornerRightDown = createLucideIcon(\"CornerRightDown\", [[\"polyline\", {\n  points: \"10 15 15 20 20 15\",\n  key: \"axus6l\"\n}], [\"path\", {\n  d: \"M4 4h7a4 4 0 0 1 4 4v12\",\n  key: \"wcbgct\"\n}]]);\nexport { CornerRightDown as default };", "map": {"version": 3, "names": ["CornerRightDown", "createLucideIcon", "points", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\corner-right-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CornerRightDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxMCAxNSAxNSAyMCAyMCAxNSIgLz4KICA8cGF0aCBkPSJNNCA0aDdhNCA0IDAgMCAxIDQgNHYxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/corner-right-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CornerRightDown = createLucideIcon('CornerRightDown', [\n  ['polyline', { points: '10 15 15 20 20 15', key: 'axus6l' }],\n  ['path', { d: 'M4 4h7a4 4 0 0 1 4 4v12', key: 'wcbgct' }],\n]);\n\nexport default CornerRightDown;\n"], "mappings": ";;;;;AAaM,MAAAA,eAAA,GAAkBC,gBAAA,CAAiB,iBAAmB,GAC1D,CAAC,UAAY;EAAEC,MAAA,EAAQ,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": 3, "file": "workbox-expiration.prod.js", "sources": ["../node_modules/idb/build/wrap-idb-value.js", "../node_modules/idb/build/index.js", "../_version.js", "../models/CacheTimestampsModel.js", "../CacheExpiration.js", "../ExpirationPlugin.js"], "sourcesContent": ["const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction));\n        });\n    }\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking)\n            db.addEventListener('versionchange', () => blocking());\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:expiration:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { openDB, deleteDB } from 'idb';\nimport '../_version.js';\nconst DB_NAME = 'workbox-expiration';\nconst CACHE_OBJECT_STORE = 'cache-entries';\nconst normalizeURL = (unNormalizedUrl) => {\n    const url = new URL(unNormalizedUrl, location.href);\n    url.hash = '';\n    return url.href;\n};\n/**\n * Returns the timestamp model.\n *\n * @private\n */\nclass CacheTimestampsModel {\n    /**\n     *\n     * @param {string} cacheName\n     *\n     * @private\n     */\n    constructor(cacheName) {\n        this._db = null;\n        this._cacheName = cacheName;\n    }\n    /**\n     * Performs an upgrade of indexedDB.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDb(db) {\n        // TODO(philipwalton): EdgeHTML doesn't support arrays as a keyPath, so we\n        // have to use the `id` keyPath here and create our own values (a\n        // concatenation of `url + cacheName`) instead of simply using\n        // `keyPath: ['url', 'cacheName']`, which is supported in other browsers.\n        const objStore = db.createObjectStore(CACHE_OBJECT_STORE, { keyPath: 'id' });\n        // TODO(philipwalton): once we don't have to support EdgeHTML, we can\n        // create a single index with the keyPath `['cacheName', 'timestamp']`\n        // instead of doing both these indexes.\n        objStore.createIndex('cacheName', 'cacheName', { unique: false });\n        objStore.createIndex('timestamp', 'timestamp', { unique: false });\n    }\n    /**\n     * Performs an upgrade of indexedDB and deletes deprecated DBs.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDbAndDeleteOldDbs(db) {\n        this._upgradeDb(db);\n        if (this._cacheName) {\n            void deleteDB(this._cacheName);\n        }\n    }\n    /**\n     * @param {string} url\n     * @param {number} timestamp\n     *\n     * @private\n     */\n    async setTimestamp(url, timestamp) {\n        url = normalizeURL(url);\n        const entry = {\n            url,\n            timestamp,\n            cacheName: this._cacheName,\n            // Creating an ID from the URL and cache name won't be necessary once\n            // Edge switches to Chromium and all browsers we support work with\n            // array keyPaths.\n            id: this._getId(url),\n        };\n        const db = await this.getDb();\n        const tx = db.transaction(CACHE_OBJECT_STORE, 'readwrite', {\n            durability: 'relaxed',\n        });\n        await tx.store.put(entry);\n        await tx.done;\n    }\n    /**\n     * Returns the timestamp stored for a given URL.\n     *\n     * @param {string} url\n     * @return {number | undefined}\n     *\n     * @private\n     */\n    async getTimestamp(url) {\n        const db = await this.getDb();\n        const entry = await db.get(CACHE_OBJECT_STORE, this._getId(url));\n        return entry === null || entry === void 0 ? void 0 : entry.timestamp;\n    }\n    /**\n     * Iterates through all the entries in the object store (from newest to\n     * oldest) and removes entries once either `maxCount` is reached or the\n     * entry's timestamp is less than `minTimestamp`.\n     *\n     * @param {number} minTimestamp\n     * @param {number} maxCount\n     * @return {Array<string>}\n     *\n     * @private\n     */\n    async expireEntries(minTimestamp, maxCount) {\n        const db = await this.getDb();\n        let cursor = await db\n            .transaction(CACHE_OBJECT_STORE)\n            .store.index('timestamp')\n            .openCursor(null, 'prev');\n        const entriesToDelete = [];\n        let entriesNotDeletedCount = 0;\n        while (cursor) {\n            const result = cursor.value;\n            // TODO(philipwalton): once we can use a multi-key index, we\n            // won't have to check `cacheName` here.\n            if (result.cacheName === this._cacheName) {\n                // Delete an entry if it's older than the max age or\n                // if we already have the max number allowed.\n                if ((minTimestamp && result.timestamp < minTimestamp) ||\n                    (maxCount && entriesNotDeletedCount >= maxCount)) {\n                    // TODO(philipwalton): we should be able to delete the\n                    // entry right here, but doing so causes an iteration\n                    // bug in Safari stable (fixed in TP). Instead we can\n                    // store the keys of the entries to delete, and then\n                    // delete the separate transactions.\n                    // https://github.com/GoogleChrome/workbox/issues/1978\n                    // cursor.delete();\n                    // We only need to return the URL, not the whole entry.\n                    entriesToDelete.push(cursor.value);\n                }\n                else {\n                    entriesNotDeletedCount++;\n                }\n            }\n            cursor = await cursor.continue();\n        }\n        // TODO(philipwalton): once the Safari bug in the following issue is fixed,\n        // we should be able to remove this loop and do the entry deletion in the\n        // cursor loop above:\n        // https://github.com/GoogleChrome/workbox/issues/1978\n        const urlsDeleted = [];\n        for (const entry of entriesToDelete) {\n            await db.delete(CACHE_OBJECT_STORE, entry.id);\n            urlsDeleted.push(entry.url);\n        }\n        return urlsDeleted;\n    }\n    /**\n     * Takes a URL and returns an ID that will be unique in the object store.\n     *\n     * @param {string} url\n     * @return {string}\n     *\n     * @private\n     */\n    _getId(url) {\n        // Creating an ID from the URL and cache name won't be necessary once\n        // Edge switches to Chromium and all browsers we support work with\n        // array keyPaths.\n        return this._cacheName + '|' + normalizeURL(url);\n    }\n    /**\n     * Returns an open connection to the database.\n     *\n     * @private\n     */\n    async getDb() {\n        if (!this._db) {\n            this._db = await openDB(DB_NAME, 1, {\n                upgrade: this._upgradeDbAndDeleteOldDbs.bind(this),\n            });\n        }\n        return this._db;\n    }\n}\nexport { CacheTimestampsModel };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheTimestampsModel } from './models/CacheTimestampsModel.js';\nimport './_version.js';\n/**\n * The `CacheExpiration` class allows you define an expiration and / or\n * limit on the number of responses stored in a\n * [`Cache`](https://developer.mozilla.org/en-US/docs/Web/API/Cache).\n *\n * @memberof workbox-expiration\n */\nclass CacheExpiration {\n    /**\n     * To construct a new CacheExpiration instance you must provide at least\n     * one of the `config` properties.\n     *\n     * @param {string} cacheName Name of the cache to apply restrictions to.\n     * @param {Object} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     */\n    constructor(cacheName, config = {}) {\n        this._isRunning = false;\n        this._rerunRequested = false;\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(cacheName, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'constructor',\n                paramName: 'cacheName',\n            });\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._maxEntries = config.maxEntries;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._matchOptions = config.matchOptions;\n        this._cacheName = cacheName;\n        this._timestampModel = new CacheTimestampsModel(cacheName);\n    }\n    /**\n     * Expires entries for the given cache and given criteria.\n     */\n    async expireEntries() {\n        if (this._isRunning) {\n            this._rerunRequested = true;\n            return;\n        }\n        this._isRunning = true;\n        const minTimestamp = this._maxAgeSeconds\n            ? Date.now() - this._maxAgeSeconds * 1000\n            : 0;\n        const urlsExpired = await this._timestampModel.expireEntries(minTimestamp, this._maxEntries);\n        // Delete URLs from the cache\n        const cache = await self.caches.open(this._cacheName);\n        for (const url of urlsExpired) {\n            await cache.delete(url, this._matchOptions);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (urlsExpired.length > 0) {\n                logger.groupCollapsed(`Expired ${urlsExpired.length} ` +\n                    `${urlsExpired.length === 1 ? 'entry' : 'entries'} and removed ` +\n                    `${urlsExpired.length === 1 ? 'it' : 'them'} from the ` +\n                    `'${this._cacheName}' cache.`);\n                logger.log(`Expired the following ${urlsExpired.length === 1 ? 'URL' : 'URLs'}:`);\n                urlsExpired.forEach((url) => logger.log(`    ${url}`));\n                logger.groupEnd();\n            }\n            else {\n                logger.debug(`Cache expiration ran and found no entries to remove.`);\n            }\n        }\n        this._isRunning = false;\n        if (this._rerunRequested) {\n            this._rerunRequested = false;\n            dontWaitFor(this.expireEntries());\n        }\n    }\n    /**\n     * Update the timestamp for the given URL. This ensures the when\n     * removing entries based on maximum entries, most recently used\n     * is accurate or when expiring, the timestamp is up-to-date.\n     *\n     * @param {string} url\n     */\n    async updateTimestamp(url) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(url, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'updateTimestamp',\n                paramName: 'url',\n            });\n        }\n        await this._timestampModel.setTimestamp(url, Date.now());\n    }\n    /**\n     * Can be used to check if a URL has expired or not before it's used.\n     *\n     * This requires a look up from IndexedDB, so can be slow.\n     *\n     * Note: This method will not remove the cached entry, call\n     * `expireEntries()` to remove indexedDB and Cache entries.\n     *\n     * @param {string} url\n     * @return {boolean}\n     */\n    async isURLExpired(url) {\n        if (!this._maxAgeSeconds) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new WorkboxError(`expired-test-without-max-age`, {\n                    methodName: 'isURLExpired',\n                    paramName: 'maxAgeSeconds',\n                });\n            }\n            return false;\n        }\n        else {\n            const timestamp = await this._timestampModel.getTimestamp(url);\n            const expireOlderThan = Date.now() - this._maxAgeSeconds * 1000;\n            return timestamp !== undefined ? timestamp < expireOlderThan : true;\n        }\n    }\n    /**\n     * Removes the IndexedDB object store used to keep track of cache expiration\n     * metadata.\n     */\n    async delete() {\n        // Make sure we don't attempt another rerun if we're called in the middle of\n        // a cache expiration.\n        this._rerunRequested = false;\n        await this._timestampModel.expireEntries(Infinity); // Expires all.\n    }\n}\nexport { CacheExpiration };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { registerQuotaErrorCallback } from 'workbox-core/registerQuotaErrorCallback.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheExpiration } from './CacheExpiration.js';\nimport './_version.js';\n/**\n * This plugin can be used in a `workbox-strategy` to regularly enforce a\n * limit on the age and / or the number of cached requests.\n *\n * It can only be used with `workbox-strategy` instances that have a\n * [custom `cacheName` property set](/web/tools/workbox/guides/configure-workbox#custom_cache_names_in_strategies).\n * In other words, it can't be used to expire entries in strategy that uses the\n * default runtime cache name.\n *\n * Whenever a cached response is used or updated, this plugin will look\n * at the associated cache and remove any old or extra responses.\n *\n * When using `maxAgeSeconds`, responses may be used *once* after expiring\n * because the expiration clean up will not have occurred until *after* the\n * cached response has been used. If the response has a \"Date\" header, then\n * a light weight expiration check is performed and the response will not be\n * used immediately.\n *\n * When using `maxEntries`, the entry least-recently requested will be removed\n * from the cache first.\n *\n * @memberof workbox-expiration\n */\nclass ExpirationPlugin {\n    /**\n     * @param {ExpirationPluginOptions} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     * @param {boolean} [config.purgeOnQuotaError] Whether to opt this cache in to\n     * automatic deletion if the available storage quota has been exceeded.\n     */\n    constructor(config = {}) {\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when a `Response` is about to be returned\n         * from a [Cache](https://developer.mozilla.org/en-US/docs/Web/API/Cache) to\n         * the handler. It allows the `Response` to be inspected for freshness and\n         * prevents it from being used if the `Response`'s `Date` header value is\n         * older than the configured `maxAgeSeconds`.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache the response is in.\n         * @param {Response} options.cachedResponse The `Response` object that's been\n         *     read from a cache and whose freshness should be checked.\n         * @return {Response} Either the `cachedResponse`, if it's\n         *     fresh, or `null` if the `Response` is older than `maxAgeSeconds`.\n         *\n         * @private\n         */\n        this.cachedResponseWillBeUsed = async ({ event, request, cacheName, cachedResponse, }) => {\n            if (!cachedResponse) {\n                return null;\n            }\n            const isFresh = this._isResponseDateFresh(cachedResponse);\n            // Expire entries to ensure that even if the expiration date has\n            // expired, it'll only be used once.\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            dontWaitFor(cacheExpiration.expireEntries());\n            // Update the metadata for the request URL to the current timestamp,\n            // but don't `await` it as we don't want to block the response.\n            const updateTimestampDone = cacheExpiration.updateTimestamp(request.url);\n            if (event) {\n                try {\n                    event.waitUntil(updateTimestampDone);\n                }\n                catch (error) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // The event may not be a fetch event; only log the URL if it is.\n                        if ('request' in event) {\n                            logger.warn(`Unable to ensure service worker stays alive when ` +\n                                `updating cache entry for ` +\n                                `'${getFriendlyURL(event.request.url)}'.`);\n                        }\n                    }\n                }\n            }\n            return isFresh ? cachedResponse : null;\n        };\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when an entry is added to a cache.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache that was updated.\n         * @param {string} options.request The Request for the cached entry.\n         *\n         * @private\n         */\n        this.cacheDidUpdate = async ({ cacheName, request, }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                assert.isType(cacheName, 'string', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'cacheName',\n                });\n                assert.isInstance(request, Request, {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'request',\n                });\n            }\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            await cacheExpiration.updateTimestamp(request.url);\n            await cacheExpiration.expireEntries();\n        };\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._config = config;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._cacheExpirations = new Map();\n        if (config.purgeOnQuotaError) {\n            registerQuotaErrorCallback(() => this.deleteCacheAndMetadata());\n        }\n    }\n    /**\n     * A simple helper method to return a CacheExpiration instance for a given\n     * cache name.\n     *\n     * @param {string} cacheName\n     * @return {CacheExpiration}\n     *\n     * @private\n     */\n    _getCacheExpiration(cacheName) {\n        if (cacheName === cacheNames.getRuntimeName()) {\n            throw new WorkboxError('expire-custom-caches-only');\n        }\n        let cacheExpiration = this._cacheExpirations.get(cacheName);\n        if (!cacheExpiration) {\n            cacheExpiration = new CacheExpiration(cacheName, this._config);\n            this._cacheExpirations.set(cacheName, cacheExpiration);\n        }\n        return cacheExpiration;\n    }\n    /**\n     * @param {Response} cachedResponse\n     * @return {boolean}\n     *\n     * @private\n     */\n    _isResponseDateFresh(cachedResponse) {\n        if (!this._maxAgeSeconds) {\n            // We aren't expiring by age, so return true, it's fresh\n            return true;\n        }\n        // Check if the 'date' header will suffice a quick expiration check.\n        // See https://github.com/GoogleChromeLabs/sw-toolbox/issues/164 for\n        // discussion.\n        const dateHeaderTimestamp = this._getDateHeaderTimestamp(cachedResponse);\n        if (dateHeaderTimestamp === null) {\n            // Unable to parse date, so assume it's fresh.\n            return true;\n        }\n        // If we have a valid headerTime, then our response is fresh iff the\n        // headerTime plus maxAgeSeconds is greater than the current time.\n        const now = Date.now();\n        return dateHeaderTimestamp >= now - this._maxAgeSeconds * 1000;\n    }\n    /**\n     * This method will extract the data header and parse it into a useful\n     * value.\n     *\n     * @param {Response} cachedResponse\n     * @return {number|null}\n     *\n     * @private\n     */\n    _getDateHeaderTimestamp(cachedResponse) {\n        if (!cachedResponse.headers.has('date')) {\n            return null;\n        }\n        const dateHeader = cachedResponse.headers.get('date');\n        const parsedDate = new Date(dateHeader);\n        const headerTime = parsedDate.getTime();\n        // If the Date header was invalid for some reason, parsedDate.getTime()\n        // will return NaN.\n        if (isNaN(headerTime)) {\n            return null;\n        }\n        return headerTime;\n    }\n    /**\n     * This is a helper method that performs two operations:\n     *\n     * - Deletes *all* the underlying Cache instances associated with this plugin\n     * instance, by calling caches.delete() on your behalf.\n     * - Deletes the metadata from IndexedDB used to keep track of expiration\n     * details for each Cache instance.\n     *\n     * When using cache expiration, calling this method is preferable to calling\n     * `caches.delete()` directly, since this will ensure that the IndexedDB\n     * metadata is also cleanly removed and open IndexedDB instances are deleted.\n     *\n     * Note that if you're *not* using cache expiration for a given cache, calling\n     * `caches.delete()` and passing in the cache's name should be sufficient.\n     * There is no Workbox-specific method needed for cleanup in that case.\n     */\n    async deleteCacheAndMetadata() {\n        // Do this one at a time instead of all at once via `Promise.all()` to\n        // reduce the chance of inconsistency if a promise rejects.\n        for (const [cacheName, cacheExpiration] of this._cacheExpirations) {\n            await self.caches.delete(cacheName);\n            await cacheExpiration.delete();\n        }\n        // Reset this._cacheExpirations to its initial state.\n        this._cacheExpirations = new Map();\n    }\n}\nexport { ExpirationPlugin };\n"], "names": ["instanceOfAny", "object", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "idbProxyTraps", "get", "target", "prop", "receiver", "IDBTransaction", "objectStoreNames", "undefined", "objectStore", "wrap", "set", "value", "has", "wrapFunction", "func", "IDBDatabase", "prototype", "transaction", "IDBCursor", "advance", "continue", "continuePrimaryKey", "includes", "args", "apply", "unwrap", "this", "storeNames", "tx", "call", "sort", "transformCachableValue", "done", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "complete", "error", "DOMException", "addEventListener", "cacheDonePromiseForTransaction", "IDBObjectStore", "IDBIndex", "Proxy", "IDBRequest", "request", "promise", "success", "result", "then", "catch", "promisifyRequest", "newValue", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "replace", "useIndex", "isWrite", "method", "async", "storeName", "store", "index", "shift", "all", "oldTraps", "callback", "self", "_", "e", "CACHE_OBJECT_STORE", "normalizeURL", "unNormalizedUrl", "url", "URL", "location", "href", "hash", "CacheTimestampsModel", "constructor", "cacheName", "_db", "_cacheName", "_upgradeDb", "db", "objStore", "createObjectStore", "keyP<PERSON>", "createIndex", "unique", "_upgradeDbAndDeleteOldDbs", "name", "blocked", "indexedDB", "deleteDatabase", "deleteDB", "timestamp", "entry", "id", "_getId", "getDb", "durability", "put", "minTimestamp", "maxCount", "cursor", "openCursor", "entriesToDelete", "entriesNotDeletedCount", "push", "urlsDeleted", "delete", "version", "upgrade", "blocking", "terminated", "open", "openPromise", "event", "oldVersion", "newVersion", "openDB", "bind", "CacheExpiration", "config", "_isRunning", "_rerunRequested", "_maxEntries", "maxEntries", "_maxAgeSeconds", "maxAgeSeconds", "_matchOptions", "matchOptions", "_timestampModel", "Date", "now", "urlsExpired", "expireEntries", "cache", "caches", "dontWait<PERSON>or", "setTimestamp", "getTimestamp", "expire<PERSON><PERSON><PERSON><PERSON>", "Infinity", "cachedResponseWillBeUsed", "cachedResponse", "isFresh", "_isResponseDateFresh", "cacheExpiration", "_getCacheExpiration", "updateTimestampDone", "updateTimestamp", "waitUntil", "cacheDidUpdate", "_config", "_cacheExpirations", "purgeOnQuotaError", "registerQuotaErrorCallback", "deleteCacheAndMetadata", "cacheNames", "getRuntimeName", "WorkboxError", "dateHeaderTimestamp", "_getDateHeaderTimestamp", "headers", "<PERSON><PERSON><PERSON><PERSON>", "headerTime", "getTime", "isNaN"], "mappings": "uSAAA,MAAMA,EAAgB,CAACC,EAAQC,IAAiBA,EAAaC,MAAMC,GAAMH,aAAkBG,IAE3F,IAAIC,EACAC,EAqBJ,MAAMC,EAAmB,IAAIC,QACvBC,EAAqB,IAAID,QACzBE,EAA2B,IAAIF,QAC/BG,EAAiB,IAAIH,QACrBI,EAAwB,IAAIJ,QA0DlC,IAAIK,EAAgB,CAChBC,IAAIC,EAAQC,EAAMC,MACVF,aAAkBG,eAAgB,IAErB,SAATF,EACA,OAAOP,EAAmBK,IAAIC,MAErB,qBAATC,SACOD,EAAOI,kBAAoBT,EAAyBI,IAAIC,MAGtD,UAATC,SACOC,EAASE,iBAAiB,QAC3BC,EACAH,EAASI,YAAYJ,EAASE,iBAAiB,WAItDG,EAAKP,EAAOC,KAEvBO,IAAG,CAACR,EAAQC,EAAMQ,KACdT,EAAOC,GAAQQ,GACR,GAEXC,IAAG,CAACV,EAAQC,IACJD,aAAkBG,iBACR,SAATF,GAA4B,UAATA,IAGjBA,KAAQD,GAMvB,SAASW,EAAaC,UAIdA,IAASC,YAAYC,UAAUC,aAC7B,qBAAsBZ,eAAeW,WA7GnCvB,IACHA,EAAuB,CACpByB,UAAUF,UAAUG,QACpBD,UAAUF,UAAUI,SACpBF,UAAUF,UAAUK,sBAqHEC,SAASR,GAC5B,YAAaS,UAGhBT,EAAKU,MAAMC,EAAOC,MAAOH,GAClBd,EAAKf,EAAiBO,IAAIyB,QAGlC,YAAaH,UAGTd,EAAKK,EAAKU,MAAMC,EAAOC,MAAOH,KAtB9B,SAAUI,KAAeJ,SACtBK,EAAKd,EAAKe,KAAKJ,EAAOC,MAAOC,KAAeJ,UAClD1B,EAAyBa,IAAIkB,EAAID,EAAWG,KAAOH,EAAWG,OAAS,CAACH,IACjElB,EAAKmB,GAqBvB,CACD,SAASG,EAAuBpB,SACP,mBAAVA,EACAE,EAAaF,IAGpBA,aAAiBN,gBAhGzB,SAAwCuB,MAEhChC,EAAmBgB,IAAIgB,GACvB,aACEI,EAAO,IAAIC,SAAQ,CAACC,EAASC,WACzBC,EAAW,KACbR,EAAGS,oBAAoB,WAAYC,GACnCV,EAAGS,oBAAoB,QAASE,GAChCX,EAAGS,oBAAoB,QAASE,IAE9BD,EAAW,KACbJ,IACAE,KAEEG,EAAQ,KACVJ,EAAOP,EAAGW,OAAS,IAAIC,aAAa,aAAc,eAClDJ,KAEJR,EAAGa,iBAAiB,WAAYH,GAChCV,EAAGa,iBAAiB,QAASF,GAC7BX,EAAGa,iBAAiB,QAASF,MAGjC3C,EAAmBc,IAAIkB,EAAII,EAC9B,CAyEOU,CAA+B/B,GAC/BxB,EAAcwB,EAzJVnB,IACHA,EAAoB,CACjBuB,YACA4B,eACAC,SACA1B,UACAb,kBAoJG,IAAIwC,MAAMlC,EAAOX,GAErBW,EACV,CACD,SAASF,EAAKE,MAGNA,aAAiBmC,WACjB,OA3IR,SAA0BC,SAChBC,EAAU,IAAIf,SAAQ,CAACC,EAASC,WAC5BC,EAAW,KACbW,EAAQV,oBAAoB,UAAWY,GACvCF,EAAQV,oBAAoB,QAASE,IAEnCU,EAAU,KACZf,EAAQzB,EAAKsC,EAAQG,SACrBd,KAEEG,EAAQ,KACVJ,EAAOY,EAAQR,OACfH,KAEJW,EAAQN,iBAAiB,UAAWQ,GACpCF,EAAQN,iBAAiB,QAASF,aAEtCS,EACKG,MAAMxC,IAGHA,aAAiBO,WACjBxB,EAAiBgB,IAAIC,EAAOoC,MAI/BK,OAAM,SAGXrD,EAAsBW,IAAIsC,EAASD,GAC5BC,CACV,CA4GcK,CAAiB1C,MAGxBb,EAAec,IAAID,GACnB,OAAOb,EAAeG,IAAIU,SACxB2C,EAAWvB,EAAuBpB,UAGpC2C,IAAa3C,IACbb,EAAeY,IAAIC,EAAO2C,GAC1BvD,EAAsBW,IAAI4C,EAAU3C,IAEjC2C,CACV,CACD,MAAM7B,EAAUd,GAAUZ,EAAsBE,IAAIU,GC5IpD,MAAM4C,EAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,EAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,EAAgB,IAAIC,IAC1B,SAASC,EAAUzD,EAAQC,QACjBD,aAAkBa,cAClBZ,KAAQD,GACM,iBAATC,YAGPsD,EAAcxD,IAAIE,GAClB,OAAOsD,EAAcxD,IAAIE,SACvByD,EAAiBzD,EAAK0D,QAAQ,aAAc,IAC5CC,EAAW3D,IAASyD,EACpBG,EAAUP,EAAalC,SAASsC,QAGpCA,KAAmBE,EAAWlB,SAAWD,gBAAgB3B,aACrD+C,IAAWR,EAAYjC,SAASsC,gBAGhCI,EAASC,eAAgBC,KAAc3C,SAEnCK,EAAKF,KAAKT,YAAYiD,EAAWH,EAAU,YAAc,gBAC3D7D,EAAS0B,EAAGuC,aACZL,IACA5D,EAASA,EAAOkE,MAAM7C,EAAK8C,iBAMjBpC,QAAQqC,IAAI,CACtBpE,EAAO0D,MAAmBrC,GAC1BwC,GAAWnC,EAAGI,QACd,WAERyB,EAAc/C,IAAIP,EAAM6D,GACjBA,CACV,CDuCGhE,ECtCUuE,SACPA,GACHtE,IAAK,CAACC,EAAQC,EAAMC,IAAauD,EAAUzD,EAAQC,IAASoE,EAAStE,IAAIC,EAAQC,EAAMC,GACvFQ,IAAK,CAACV,EAAQC,MAAWwD,EAAUzD,EAAQC,IAASoE,EAAS3D,IAAIV,EAAQC,KDmCzDqE,CAASxE,GErH7B,IACIyE,KAAK,6BAA+BC,GACvC,CACD,MAAOC,ICIP,MACMC,EAAqB,gBACrBC,EAAgBC,UACZC,EAAM,IAAIC,IAAIF,EAAiBG,SAASC,aAC9CH,EAAII,KAAO,GACJJ,EAAIG,IAAX,EAOJ,MAAME,EAOFC,YAAYC,QACHC,EAAM,UACNC,EAAaF,EAStBG,EAAWC,SAKDC,EAAWD,EAAGE,kBAAkBhB,EAAoB,CAAEiB,QAAS,OAIrEF,EAASG,YAAY,YAAa,YAAa,CAAEC,QAAQ,IACzDJ,EAASG,YAAY,YAAa,YAAa,CAAEC,QAAQ,IAS7DC,EAA0BN,QACjBD,EAAWC,GACZhE,KAAK8D,GFzBjB,SAAkBS,GAAMC,QAAEA,GAAY,UAC5BnD,EAAUoD,UAAUC,eAAeH,GACrCC,GACAnD,EAAQN,iBAAiB,WAAW,IAAMyD,MACvCzF,EAAKsC,GAASI,MAAK,KAAnB,GACV,CEqBgBkD,CAAS3E,KAAK8D,sBASRT,EAAKuB,SAEdC,EAAQ,CACVxB,IAFJA,EAAMF,EAAaE,GAGfuB,YACAhB,UAAW5D,KAAK8D,EAIhBgB,GAAI9E,KAAK+E,EAAO1B,IAGdnD,SADWF,KAAKgF,SACRzF,YAAY2D,EAAoB,YAAa,CACvD+B,WAAY,kBAEV/E,EAAGuC,MAAMyC,IAAIL,SACb3E,EAAGI,wBAUM+C,SACTW,QAAWhE,KAAKgF,QAChBH,QAAcb,EAAGzF,IAAI2E,EAAoBlD,KAAK+E,EAAO1B,WACpDwB,aAAqC,EAASA,EAAMD,8BAa3CO,EAAcC,SACxBpB,QAAWhE,KAAKgF,YAClBK,QAAerB,EACdzE,YAAY2D,GACZT,MAAMC,MAAM,aACZ4C,WAAW,KAAM,cAChBC,EAAkB,OACpBC,EAAyB,OACtBH,GAAQ,OACL7D,EAAS6D,EAAOpG,MAGlBuC,EAAOoC,YAAc5D,KAAK8D,IAGrBqB,GAAgB3D,EAAOoD,UAAYO,GACnCC,GAAYI,GAA0BJ,EASvCG,EAAgBE,KAAKJ,EAAOpG,OAG5BuG,KAGRH,QAAeA,EAAO3F,iBAMpBgG,EAAc,OACf,MAAMb,KAASU,QACVvB,EAAG2B,OAAOzC,EAAoB2B,EAAMC,IAC1CY,EAAYD,KAAKZ,EAAMxB,YAEpBqC,EAUXX,EAAO1B,UAIIrD,KAAK8D,EAAa,IAAMX,EAAaE,wBAQvCrD,KAAK6D,SACDA,QFvKjB,SAAgBU,EAAMqB,GAASpB,QAAEA,EAAFqB,QAAWA,EAAXC,SAAoBA,EAApBC,WAA8BA,GAAe,UAClE1E,EAAUoD,UAAUuB,KAAKzB,EAAMqB,GAC/BK,EAAclH,EAAKsC,UACrBwE,GACAxE,EAAQN,iBAAiB,iBAAkBmF,IACvCL,EAAQ9G,EAAKsC,EAAQG,QAAS0E,EAAMC,WAAYD,EAAME,WAAYrH,EAAKsC,EAAQ9B,aAA/E,IAGJiF,GACAnD,EAAQN,iBAAiB,WAAW,IAAMyD,MAC9CyB,EACKxE,MAAMuC,IACH+B,GACA/B,EAAGjD,iBAAiB,SAAS,IAAMgF,MACnCD,GACA9B,EAAGjD,iBAAiB,iBAAiB,IAAM+E,SAE9CpE,OAAM,SACJuE,CACV,CEoJ4BI,CAxKb,qBAwK6B,EAAG,CAChCR,QAAS7F,KAAKsE,EAA0BgC,KAAKtG,SAG9CA,KAAK6D,GCjKpB,MAAM0C,EAcF5C,YAAYC,EAAW4C,EAAS,SACvBC,GAAa,OACbC,GAAkB,OAgClBC,EAAcH,EAAOI,gBACrBC,EAAiBL,EAAOM,mBACxBC,EAAgBP,EAAOQ,kBACvBlD,EAAaF,OACbqD,EAAkB,IAAIvD,EAAqBE,4BAM5C5D,KAAKyG,mBACAC,GAAkB,QAGtBD,GAAa,QACZtB,EAAenF,KAAK6G,EACpBK,KAAKC,MAA8B,IAAtBnH,KAAK6G,EAClB,EACAO,QAAoBpH,KAAKiH,EAAgBI,cAAclC,EAAcnF,KAAK2G,GAE1EW,QAAcvE,KAAKwE,OAAOvB,KAAKhG,KAAK8D,OACrC,MAAMT,KAAO+D,QACRE,EAAM3B,OAAOtC,EAAKrD,KAAK+G,QAgB5BN,GAAa,EACdzG,KAAK0G,SACAA,GAAkB,EACvBc,cAAYxH,KAAKqH,wCAUHhE,SASZrD,KAAKiH,EAAgBQ,aAAapE,EAAK6D,KAAKC,0BAanC9D,MACVrD,KAAK6G,EASL,OACKjC,QAAkB5E,KAAKiH,EAAgBS,aAAarE,GACpDsE,EAAkBT,KAAKC,MAA8B,IAAtBnH,KAAK6G,cACrBhI,IAAd+F,GAA0BA,EAAY+C,SALtC,sBAeNjB,GAAkB,QACjB1G,KAAKiH,EAAgBI,cAAcO,oDC9HjD,MAYIjE,YAAY6C,EAAS,SAkBZqB,yBAA2BtF,OAAS2D,QAAO7E,UAASuC,YAAWkE,yBAC3DA,SACM,WAELC,EAAU/H,KAAKgI,EAAqBF,GAGpCG,EAAkBjI,KAAKkI,EAAoBtE,GACjD4D,cAAYS,EAAgBZ,uBAGtBc,EAAsBF,EAAgBG,gBAAgB/G,EAAQgC,QAChE6C,MAEIA,EAAMmC,UAAUF,GAEpB,MAAOtH,WAWJkH,EAAUD,EAAiB,IAAlC,OAYCQ,eAAiB/F,OAASqB,YAAWvC,oBAehC4G,EAAkBjI,KAAKkI,EAAoBtE,SAC3CqE,EAAgBG,gBAAgB/G,EAAQgC,WACxC4E,EAAgBZ,eAAtB,OA2BCkB,EAAU/B,OACVK,EAAiBL,EAAOM,mBACxB0B,EAAoB,IAAIxG,IACzBwE,EAAOiC,mBACPC,8BAA2B,IAAM1I,KAAK2I,2BAY9CT,EAAoBtE,MACZA,IAAcgF,aAAWC,uBACnB,IAAIC,eAAa,iCAEvBb,EAAkBjI,KAAKwI,EAAkBjK,IAAIqF,UAC5CqE,IACDA,EAAkB,IAAI1B,EAAgB3C,EAAW5D,KAAKuI,QACjDC,EAAkBxJ,IAAI4E,EAAWqE,IAEnCA,EAQXD,EAAqBF,OACZ9H,KAAK6G,SAEC,QAKLkC,EAAsB/I,KAAKgJ,EAAwBlB,MAC7B,OAAxBiB,SAEO,SAKJA,GADK7B,KAAKC,MACyC,IAAtBnH,KAAK6G,EAW7CmC,EAAwBlB,OACfA,EAAemB,QAAQ/J,IAAI,eACrB,WAELgK,EAAapB,EAAemB,QAAQ1K,IAAI,QAExC4K,EADa,IAAIjC,KAAKgC,GACEE,iBAG1BC,MAAMF,GACC,KAEJA,qCAqBF,MAAOvF,EAAWqE,KAAoBjI,KAAKwI,QACtCzF,KAAKwE,OAAO5B,OAAO/B,SACnBqE,EAAgBtC,cAGrB6C,EAAoB,IAAIxG"}
{"additionalProperties": false, "type": "object", "properties": {"additionalManifestEntries": {"description": "A list of entries to be precached, in addition to any entries that are\ngenerated as part of the build configuration.", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/ManifestEntry"}, {"type": "string"}]}}, "dontCacheBustURLsMatching": {"description": "Assets that match this will be assumed to be uniquely versioned via their\nURL, and exempted from the normal HTTP cache-busting that's done when\npopulating the precache. While not required, it's recommended that if your\nexisting build process already inserts a `[hash]` value into each filename,\nyou provide a RegExp that will detect that, as it will reduce the bandwidth\nconsumed when precaching.", "$ref": "#/definitions/RegExp"}, "manifestTransforms": {"description": "One or more functions which will be applied sequentially against the\ngenerated manifest. If `modifyURLPrefix` or `dontCacheBustURLsMatching` are\nalso specified, their corresponding transformations will be applied first.", "type": "array", "items": {}}, "maximumFileSizeToCacheInBytes": {"description": "This value can be used to determine the maximum size of files that will be\nprecached. This prevents you from inadvertently precaching very large files\nthat might have accidentally matched one of your patterns.", "default": 2097152, "type": "number"}, "modifyURLPrefix": {"description": "An object mapping string prefixes to replacement string values. This can be\nused to, e.g., remove or add a path prefix from a manifest entry if your\nweb hosting setup doesn't match your local filesystem setup. As an\nalternative with more flexibility, you can use the `manifestTransforms`\noption and provide a function that modifies the entries in the manifest\nusing whatever logic you provide.\n\nExample usage:\n\n```\n// Replace a '/dist/' prefix with '/', and also prepend\n// '/static' to every URL.\nmodifyURLPrefix: {\n  '/dist/': '/',\n  '': '/static',\n}\n```", "type": "object", "additionalProperties": {"type": "string"}}, "chunks": {"description": "One or more chunk names whose corresponding output files should be included\nin the precache manifest.", "type": "array", "items": {"type": "string"}}, "exclude": {"description": "One or more specifiers used to exclude assets from the precache manifest.\nThis is interpreted following\n[the same rules](https://webpack.js.org/configuration/module/#condition)\nas `webpack`'s standard `exclude` option.\nIf not provided, the default value is `[/\\.map$/, /^manifest.*\\.js$]`.", "type": "array", "items": {}}, "excludeChunks": {"description": "One or more chunk names whose corresponding output files should be excluded\nfrom the precache manifest.", "type": "array", "items": {"type": "string"}}, "include": {"description": "One or more specifiers used to include assets in the precache manifest.\nThis is interpreted following\n[the same rules](https://webpack.js.org/configuration/module/#condition)\nas `webpack`'s standard `include` option.", "type": "array", "items": {}}, "mode": {"description": "If set to 'production', then an optimized service worker bundle that\nexcludes debugging info will be produced. If not explicitly configured\nhere, the `mode` value configured in the current `webpack` compilation\nwill be used.", "type": ["null", "string"]}, "injectionPoint": {"description": "The string to find inside of the `swSrc` file. Once found, it will be\nreplaced by the generated precache manifest.", "default": "self.__WB_MANIFEST", "type": "string"}, "swSrc": {"description": "The path and filename of the service worker file that will be read during\nthe build process, relative to the current working directory.", "type": "string"}, "compileSrc": {"description": "When `true` (the default), the `swSrc` file will be compiled by webpack.\nWhen `false`, compilation will not occur (and `webpackCompilationPlugins`\ncan't be used.) Set to `false` if you want to inject the manifest into,\ne.g., a JSON file.", "default": true, "type": "boolean"}, "swDest": {"description": "The asset name of the service worker file that will be created by this\nplugin. If omitted, the name will be based on the `swSrc` name.", "type": "string"}, "webpackCompilationPlugins": {"description": "Optional `webpack` plugins that will be used when compiling the `swSrc`\ninput file. Only valid if `compileSrc` is `true`.", "type": "array", "items": {}}}, "required": ["swSrc"], "definitions": {"ManifestEntry": {"type": "object", "properties": {"integrity": {"type": "string"}, "revision": {"type": ["null", "string"]}, "url": {"type": "string"}}, "additionalProperties": false, "required": ["revision", "url"]}, "RegExp": {"type": "object", "properties": {"source": {"type": "string"}, "global": {"type": "boolean"}, "ignoreCase": {"type": "boolean"}, "multiline": {"type": "boolean"}, "lastIndex": {"type": "number"}, "flags": {"type": "string"}, "sticky": {"type": "boolean"}, "unicode": {"type": "boolean"}, "dotAll": {"type": "boolean"}}, "additionalProperties": false, "required": ["dotAll", "flags", "global", "ignoreCase", "lastIndex", "multiline", "source", "sticky", "unicode"]}}, "$schema": "http://json-schema.org/draft-07/schema#"}
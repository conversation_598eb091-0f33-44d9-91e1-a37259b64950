import React from 'react';
import { BarChart3, Download, Filter } from 'lucide-react';

const Reports = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600">Comprehensive reporting with AI-powered insights</p>
        </div>
        <button className="btn btn-primary flex items-center space-x-2">
          <Download className="h-5 w-5" />
          <span>Export Report</span>
        </button>
      </div>

      <div className="card text-center py-12">
        <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Reports & Analytics</h3>
        <p className="text-gray-600 mb-6">
          Advanced analytics and reporting with AI-powered insights and recommendations.
        </p>
        <div className="space-y-2 text-sm text-gray-500">
          <p>✓ Attendance and performance reports</p>
          <p>✓ Financial and fee collection analytics</p>
          <p>✓ Route efficiency and optimization reports</p>
          <p>✓ Driver performance analytics</p>
          <p>✓ Maintenance cost analysis</p>
          <p>✓ AI-powered insights and recommendations</p>
        </div>
      </div>
    </div>
  );
};

export default Reports;

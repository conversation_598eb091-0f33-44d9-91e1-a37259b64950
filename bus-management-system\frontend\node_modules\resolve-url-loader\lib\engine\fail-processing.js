/*
 * MIT License http://opensource.org/licenses/MIT
 * Author: <PERSON> @bholloway
 */
'use strict';

/**
 * Process the given CSS content into reworked CSS content.
 */
function process() {
  return new Promise(function (_, reject) {
    setTimeout(function () {
      reject(new Error('This "engine" is designed to fail at processing time, for testing purposes only'));
    }, 100);
  });
}

module.exports = process;

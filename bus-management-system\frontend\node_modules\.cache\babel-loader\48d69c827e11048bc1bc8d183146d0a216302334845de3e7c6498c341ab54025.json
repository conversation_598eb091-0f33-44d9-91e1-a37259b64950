{"ast": null, "code": "import { setLogger } from '../core';\nimport { logger } from './logger';\nsetLogger(logger);", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "logger"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/react/setLogger.js"], "sourcesContent": ["import { setLogger } from '../core';\nimport { logger } from './logger';\nsetLogger(logger);"], "mappings": "AAAA,SAASA,SAAS,QAAQ,SAAS;AACnC,SAASC,MAAM,QAAQ,UAAU;AACjCD,SAAS,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
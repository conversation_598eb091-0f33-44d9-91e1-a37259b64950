{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst MessageCircle = createLucideIcon(\"MessageCircle\", [[\"path\", {\n  d: \"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z\",\n  key: \"v2veuj\"\n}]]);\nexport { MessageCircle as default };", "map": {"version": 3, "names": ["MessageCircle", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\college management system\\bus-management-system\\frontend\\node_modules\\lucide-react\\src\\icons\\message-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAyMSAxLjktNS43YTguNSA4LjUgMCAxIDEgMy44IDMuOHoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('MessageCircle', [\n  ['path', { d: 'm3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z', key: 'v2veuj' }],\n]);\n\nexport default MessageCircle;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,EACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
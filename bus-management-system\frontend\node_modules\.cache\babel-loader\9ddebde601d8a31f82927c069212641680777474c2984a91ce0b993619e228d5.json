{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport React from 'react';\nimport { useTheme } from './theme';\nimport useMediaQuery from './useMediaQuery';\nexport var isServer = typeof window === 'undefined';\nexport function getQueryStatusColor(query, theme) {\n  return query.state.isFetching ? theme.active : !query.getObserversCount() ? theme.gray : query.isStale() ? theme.warning : theme.success;\n}\nexport function getQueryStatusLabel(query) {\n  return query.state.isFetching ? 'fetching' : !query.getObserversCount() ? 'inactive' : query.isStale() ? 'stale' : 'fresh';\n}\nexport function styled(type, newStyles, queries) {\n  if (queries === void 0) {\n    queries = {};\n  }\n  return /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n    var style = _ref.style,\n      rest = _objectWithoutPropertiesLoose(_ref, [\"style\"]);\n    var theme = useTheme();\n    var mediaStyles = Object.entries(queries).reduce(function (current, _ref2) {\n      var key = _ref2[0],\n        value = _ref2[1];\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      return useMediaQuery(key) ? _extends({}, current, typeof value === 'function' ? value(rest, theme) : value) : current;\n    }, {});\n    return /*#__PURE__*/React.createElement(type, _extends({}, rest, {\n      style: _extends({}, typeof newStyles === 'function' ? newStyles(rest, theme) : newStyles, style, mediaStyles),\n      ref: ref\n    }));\n  });\n}\nexport function useIsMounted() {\n  var mountedRef = React.useRef(false);\n  var isMounted = React.useCallback(function () {\n    return mountedRef.current;\n  }, []);\n  React[isServer ? 'useEffect' : 'useLayoutEffect'](function () {\n    mountedRef.current = true;\n    return function () {\n      mountedRef.current = false;\n    };\n  }, []);\n  return isMounted;\n}\n/**\n * This hook is a safe useState version which schedules state updates in microtasks\n * to prevent updating a component state while React is rendering different components\n * or when the component is not mounted anymore.\n */\n\nexport function useSafeState(initialState) {\n  var isMounted = useIsMounted();\n  var _React$useState = React.useState(initialState),\n    state = _React$useState[0],\n    setState = _React$useState[1];\n  var safeSetState = React.useCallback(function (value) {\n    scheduleMicrotask(function () {\n      if (isMounted()) {\n        setState(value);\n      }\n    });\n  }, [isMounted]);\n  return [state, safeSetState];\n}\n/**\n * Displays a string regardless the type of the data\n * @param {unknown} value Value to be stringified\n */\n\nexport var displayValue = function displayValue(value) {\n  var name = Object.getOwnPropertyNames(Object(value));\n  var newValue = typeof value === 'bigint' ? value.toString() + \"n\" : value;\n  return JSON.stringify(newValue, name);\n};\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  Promise.resolve().then(callback).catch(function (error) {\n    return setTimeout(function () {\n      throw error;\n    });\n  });\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "React", "useTheme", "useMediaQuery", "isServer", "window", "getQueryStatusColor", "query", "theme", "state", "isFetching", "active", "getObserversCount", "gray", "isStale", "warning", "success", "getQueryStatusLabel", "styled", "type", "newStyles", "queries", "forwardRef", "_ref", "ref", "style", "rest", "mediaStyles", "Object", "entries", "reduce", "current", "_ref2", "key", "value", "createElement", "useIsMounted", "mountedRef", "useRef", "isMounted", "useCallback", "useSafeState", "initialState", "_React$useState", "useState", "setState", "safeSetState", "scheduleMicrotask", "displayValue", "name", "getOwnPropertyNames", "newValue", "toString", "JSON", "stringify", "callback", "Promise", "resolve", "then", "catch", "error", "setTimeout"], "sources": ["C:/Users/<USER>/Desktop/college management system/bus-management-system/frontend/node_modules/react-query/es/devtools/utils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport React from 'react';\nimport { useTheme } from './theme';\nimport useMediaQuery from './useMediaQuery';\nexport var isServer = typeof window === 'undefined';\nexport function getQueryStatusColor(query, theme) {\n  return query.state.isFetching ? theme.active : !query.getObserversCount() ? theme.gray : query.isStale() ? theme.warning : theme.success;\n}\nexport function getQueryStatusLabel(query) {\n  return query.state.isFetching ? 'fetching' : !query.getObserversCount() ? 'inactive' : query.isStale() ? 'stale' : 'fresh';\n}\nexport function styled(type, newStyles, queries) {\n  if (queries === void 0) {\n    queries = {};\n  }\n\n  return /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n    var style = _ref.style,\n        rest = _objectWithoutPropertiesLoose(_ref, [\"style\"]);\n\n    var theme = useTheme();\n    var mediaStyles = Object.entries(queries).reduce(function (current, _ref2) {\n      var key = _ref2[0],\n          value = _ref2[1];\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      return useMediaQuery(key) ? _extends({}, current, typeof value === 'function' ? value(rest, theme) : value) : current;\n    }, {});\n    return /*#__PURE__*/React.createElement(type, _extends({}, rest, {\n      style: _extends({}, typeof newStyles === 'function' ? newStyles(rest, theme) : newStyles, style, mediaStyles),\n      ref: ref\n    }));\n  });\n}\nexport function useIsMounted() {\n  var mountedRef = React.useRef(false);\n  var isMounted = React.useCallback(function () {\n    return mountedRef.current;\n  }, []);\n  React[isServer ? 'useEffect' : 'useLayoutEffect'](function () {\n    mountedRef.current = true;\n    return function () {\n      mountedRef.current = false;\n    };\n  }, []);\n  return isMounted;\n}\n/**\n * This hook is a safe useState version which schedules state updates in microtasks\n * to prevent updating a component state while React is rendering different components\n * or when the component is not mounted anymore.\n */\n\nexport function useSafeState(initialState) {\n  var isMounted = useIsMounted();\n\n  var _React$useState = React.useState(initialState),\n      state = _React$useState[0],\n      setState = _React$useState[1];\n\n  var safeSetState = React.useCallback(function (value) {\n    scheduleMicrotask(function () {\n      if (isMounted()) {\n        setState(value);\n      }\n    });\n  }, [isMounted]);\n  return [state, safeSetState];\n}\n/**\n * Displays a string regardless the type of the data\n * @param {unknown} value Value to be stringified\n */\n\nexport var displayValue = function displayValue(value) {\n  var name = Object.getOwnPropertyNames(Object(value));\n  var newValue = typeof value === 'bigint' ? value.toString() + \"n\" : value;\n  return JSON.stringify(newValue, name);\n};\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  Promise.resolve().then(callback).catch(function (error) {\n    return setTimeout(function () {\n      throw error;\n    });\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAO,IAAIC,QAAQ,GAAG,OAAOC,MAAM,KAAK,WAAW;AACnD,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAChD,OAAOD,KAAK,CAACE,KAAK,CAACC,UAAU,GAAGF,KAAK,CAACG,MAAM,GAAG,CAACJ,KAAK,CAACK,iBAAiB,CAAC,CAAC,GAAGJ,KAAK,CAACK,IAAI,GAAGN,KAAK,CAACO,OAAO,CAAC,CAAC,GAAGN,KAAK,CAACO,OAAO,GAAGP,KAAK,CAACQ,OAAO;AAC1I;AACA,OAAO,SAASC,mBAAmBA,CAACV,KAAK,EAAE;EACzC,OAAOA,KAAK,CAACE,KAAK,CAACC,UAAU,GAAG,UAAU,GAAG,CAACH,KAAK,CAACK,iBAAiB,CAAC,CAAC,GAAG,UAAU,GAAGL,KAAK,CAACO,OAAO,CAAC,CAAC,GAAG,OAAO,GAAG,OAAO;AAC5H;AACA,OAAO,SAASI,MAAMA,CAACC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAC/C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,OAAO,aAAapB,KAAK,CAACqB,UAAU,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;IACxD,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;MAClBC,IAAI,GAAG1B,6BAA6B,CAACuB,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC;IAEzD,IAAIf,KAAK,GAAGN,QAAQ,CAAC,CAAC;IACtB,IAAIyB,WAAW,GAAGC,MAAM,CAACC,OAAO,CAACR,OAAO,CAAC,CAACS,MAAM,CAAC,UAAUC,OAAO,EAAEC,KAAK,EAAE;MACzE,IAAIC,GAAG,GAAGD,KAAK,CAAC,CAAC,CAAC;QACdE,KAAK,GAAGF,KAAK,CAAC,CAAC,CAAC;MACpB;MACA,OAAO7B,aAAa,CAAC8B,GAAG,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,EAAEgC,OAAO,EAAE,OAAOG,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACR,IAAI,EAAElB,KAAK,CAAC,GAAG0B,KAAK,CAAC,GAAGH,OAAO;IACvH,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,OAAO,aAAa9B,KAAK,CAACkC,aAAa,CAAChB,IAAI,EAAEpB,QAAQ,CAAC,CAAC,CAAC,EAAE2B,IAAI,EAAE;MAC/DD,KAAK,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAOqB,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACM,IAAI,EAAElB,KAAK,CAAC,GAAGY,SAAS,EAAEK,KAAK,EAAEE,WAAW,CAAC;MAC7GH,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,OAAO,SAASY,YAAYA,CAAA,EAAG;EAC7B,IAAIC,UAAU,GAAGpC,KAAK,CAACqC,MAAM,CAAC,KAAK,CAAC;EACpC,IAAIC,SAAS,GAAGtC,KAAK,CAACuC,WAAW,CAAC,YAAY;IAC5C,OAAOH,UAAU,CAACN,OAAO;EAC3B,CAAC,EAAE,EAAE,CAAC;EACN9B,KAAK,CAACG,QAAQ,GAAG,WAAW,GAAG,iBAAiB,CAAC,CAAC,YAAY;IAC5DiC,UAAU,CAACN,OAAO,GAAG,IAAI;IACzB,OAAO,YAAY;MACjBM,UAAU,CAACN,OAAO,GAAG,KAAK;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOQ,SAAS;AAClB;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,YAAYA,CAACC,YAAY,EAAE;EACzC,IAAIH,SAAS,GAAGH,YAAY,CAAC,CAAC;EAE9B,IAAIO,eAAe,GAAG1C,KAAK,CAAC2C,QAAQ,CAACF,YAAY,CAAC;IAC9CjC,KAAK,GAAGkC,eAAe,CAAC,CAAC,CAAC;IAC1BE,QAAQ,GAAGF,eAAe,CAAC,CAAC,CAAC;EAEjC,IAAIG,YAAY,GAAG7C,KAAK,CAACuC,WAAW,CAAC,UAAUN,KAAK,EAAE;IACpDa,iBAAiB,CAAC,YAAY;MAC5B,IAAIR,SAAS,CAAC,CAAC,EAAE;QACfM,QAAQ,CAACX,KAAK,CAAC;MACjB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACK,SAAS,CAAC,CAAC;EACf,OAAO,CAAC9B,KAAK,EAAEqC,YAAY,CAAC;AAC9B;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACd,KAAK,EAAE;EACrD,IAAIe,IAAI,GAAGrB,MAAM,CAACsB,mBAAmB,CAACtB,MAAM,CAACM,KAAK,CAAC,CAAC;EACpD,IAAIiB,QAAQ,GAAG,OAAOjB,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACkB,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAGlB,KAAK;EACzE,OAAOmB,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAEF,IAAI,CAAC;AACvC,CAAC;AACD;AACA;AACA;AACA;;AAEA,SAASF,iBAAiBA,CAACQ,QAAQ,EAAE;EACnCC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,KAAK,CAAC,UAAUC,KAAK,EAAE;IACtD,OAAOC,UAAU,CAAC,YAAY;MAC5B,MAAMD,KAAK;IACb,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}